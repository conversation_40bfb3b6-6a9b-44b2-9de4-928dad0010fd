"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Upload, 
  Link, 
  Zap, 
  Shield, 
  Accessibility, 
  Smartphone,
  Search,
  Settings,
  Info,
} from 'lucide-react';

import PDFViewer from '@/components/pdf/pdf-viewer';

export default function DemoPage() {
  const [pdfUrl, setPdfUrl] = useState('');
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [activeDemo, setActiveDemo] = useState<'url' | 'file' | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setPdfFile(file);
      setActiveDemo('file');
    }
  };

  const handleUrlSubmit = () => {
    if (pdfUrl.trim()) {
      setActiveDemo('url');
    }
  };

  const samplePDFs = [
    {
      name: 'Sample Document 1',
      url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      description: 'Technical paper with complex formatting',
    },
    {
      name: 'Sample Document 2', 
      url: 'https://www.w3.org/WAI/WCAG21/Understanding/understanding-wcag.pdf',
      description: 'WCAG accessibility guidelines',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Cobalt PDF Viewer Demo
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Experience the next generation of PDF viewing with advanced accessibility, 
            mobile optimization, and enterprise-grade performance features.
          </p>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-semibold">High Performance</h3>
              <p className="text-sm text-muted-foreground">Progressive loading & worker optimization</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Accessibility className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-semibold">Accessibility</h3>
              <p className="text-sm text-muted-foreground">Screen reader & keyboard navigation</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Smartphone className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <h3 className="font-semibold">Mobile Optimized</h3>
              <p className="text-sm text-muted-foreground">Touch gestures & responsive design</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Search className="h-8 w-8 mx-auto mb-2 text-orange-500" />
              <h3 className="font-semibold">Advanced Search</h3>
              <p className="text-sm text-muted-foreground">Regex, fuzzy search & highlighting</p>
            </CardContent>
          </Card>
        </div>

        {/* PDF Loader */}
        {!activeDemo && (
          <Card className="max-w-2xl mx-auto mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Load PDF Document
              </CardTitle>
              <CardDescription>
                Choose a PDF file from your device or enter a URL to get started
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="url" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="url">From URL</TabsTrigger>
                  <TabsTrigger value="file">Upload File</TabsTrigger>
                </TabsList>
                
                <TabsContent value="url" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="pdf-url">PDF URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="pdf-url"
                        placeholder="https://example.com/document.pdf"
                        value={pdfUrl}
                        onChange={(e) => setPdfUrl(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleUrlSubmit()}
                      />
                      <Button onClick={handleUrlSubmit} disabled={!pdfUrl.trim()}>
                        <Link className="h-4 w-4 mr-2" />
                        Load
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Sample Documents</Label>
                    <div className="grid gap-2">
                      {samplePDFs.map((sample, index) => (
                        <Card key={index} className="cursor-pointer hover:bg-muted/50" onClick={() => {
                          setPdfUrl(sample.url);
                          setActiveDemo('url');
                        }}>
                          <CardContent className="p-3">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium">{sample.name}</h4>
                                <p className="text-sm text-muted-foreground">{sample.description}</p>
                              </div>
                              <Button variant="ghost" size="sm">
                                Load
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="file" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="pdf-file">Choose PDF File</Label>
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">
                          Click to upload or drag and drop
                        </p>
                        <Input
                          id="pdf-file"
                          type="file"
                          accept=".pdf"
                          onChange={handleFileChange}
                          className="max-w-xs mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        )}

        {/* PDF Viewer */}
        {activeDemo && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">PDF Viewer</h2>
              <Button 
                variant="outline" 
                onClick={() => {
                  setActiveDemo(null);
                  setPdfUrl('');
                  setPdfFile(null);
                }}
              >
                Load Different PDF
              </Button>
            </div>
            
            <Card className="h-[800px]">
              <PDFViewer
                url={activeDemo === 'url' ? pdfUrl : undefined}
                file={activeDemo === 'file' ? pdfFile : undefined}
                className="w-full h-full"
                onDocumentLoad={(doc) => {
                  console.log('Document loaded:', doc);
                }}
                onPageChange={(page) => {
                  console.log('Page changed:', page);
                }}
                onError={(error) => {
                  console.error('PDF error:', error);
                }}
              />
            </Card>
          </div>
        )}

        {/* Instructions */}
        {activeDemo && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                How to Use
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Navigation</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Arrow keys: Navigate pages</li>
                    <li>• Home/End: First/last page</li>
                    <li>• Space: Next page</li>
                    <li>• Shift+Space: Previous page</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Zoom & View</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Ctrl + / Ctrl -: Zoom in/out</li>
                    <li>• Ctrl 0: Reset zoom</li>
                    <li>• Ctrl 1: Fit to page</li>
                    <li>• Ctrl 2: Fit to width</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Features</h4>
                  <ul className="text-sm space-y-1 text-muted-foreground">
                    <li>• Ctrl F: Search document</li>
                    <li>• F11: Toggle fullscreen</li>
                    <li>• ?: Show keyboard shortcuts</li>
                    <li>• Settings: Quality controls</li>
                  </ul>
                </div>
              </div>
              
              <Alert className="mt-4">
                <Accessibility className="h-4 w-4" />
                <AlertDescription>
                  This viewer is fully accessible with screen reader support, keyboard navigation, 
                  and mobile touch gestures. Enable accessibility features in the settings panel.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center mt-12 text-muted-foreground">
          <p>
            Built with React, TypeScript, and PDF.js • 
            Features progressive loading, worker optimization, and comprehensive accessibility
          </p>
        </div>
      </div>
    </div>
  );
}
