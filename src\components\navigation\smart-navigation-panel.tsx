"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BookOpen, 
  Bookmark, 
  Clock, 
  TrendingUp, 
  Target, 
  Star,
  MapPin,
  Brain,
  CheckCircle,
  Circle,
  Timer,
  BarChart3,
  Lightbulb,
  ArrowRight,
  Calendar,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  SmartNavigationManager, 
  type ReadingProgress, 
  type SmartBookmark,
  type DocumentOutline,
} from '@/lib/navigation/smart-navigation';

interface SmartNavigationPanelProps {
  navigationManager: SmartNavigationManager;
  documentId: string;
  currentPage: number;
  totalPages: number;
  onNavigateToPage: (page: number) => void;
  className?: string;
}

export default function SmartNavigationPanel({
  navigationManager,
  documentId,
  currentPage,
  totalPages,
  onNavigateToPage,
  className,
}: SmartNavigationPanelProps) {
  const [progress, setProgress] = useState<ReadingProgress | null>(null);
  const [bookmarks, setBookmarks] = useState<SmartBookmark[]>([]);
  const [outline, setOutline] = useState<DocumentOutline[]>([]);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('progress');

  // Update data when document or page changes
  useEffect(() => {
    updateData();
  }, [documentId, currentPage]);

  // Listen for navigation events
  useEffect(() => {
    const handleProgressUpdate = () => updateData();
    const handleBookmarkAdded = () => updateData();
    const handleBookmarkRemoved = () => updateData();

    navigationManager.addEventListener('page-visited', handleProgressUpdate);
    navigationManager.addEventListener('bookmark-added', handleBookmarkAdded);
    navigationManager.addEventListener('bookmark-removed', handleBookmarkRemoved);

    return () => {
      navigationManager.removeEventListener('page-visited', handleProgressUpdate);
      navigationManager.removeEventListener('bookmark-added', handleBookmarkAdded);
      navigationManager.removeEventListener('bookmark-removed', handleBookmarkRemoved);
    };
  }, [navigationManager]);

  const updateData = useCallback(() => {
    const readingProgress = navigationManager.getReadingProgress(documentId);
    const documentBookmarks = navigationManager.getBookmarks(documentId);
    const documentOutline = navigationManager.getDocumentOutline(documentId);
    const readingSuggestions = navigationManager.getReadingSuggestions(documentId);
    const readingStats = navigationManager.getReadingStats(documentId);

    setProgress(readingProgress);
    setBookmarks(documentBookmarks);
    setOutline(documentOutline);
    setSuggestions(readingSuggestions);
    setStats(readingStats);
  }, [navigationManager, documentId]);

  const handleBookmarkClick = useCallback((bookmark: SmartBookmark) => {
    onNavigateToPage(bookmark.pageNumber);
    // Update access count
    bookmark.accessCount++;
    bookmark.lastAccessed = new Date();
  }, [onNavigateToPage]);

  const handleOutlineClick = useCallback((item: DocumentOutline) => {
    onNavigateToPage(item.pageNumber);
    item.lastVisited = new Date();
    item.isRead = true;
    navigationManager.markSectionAsRead(documentId, item.id);
  }, [onNavigateToPage, navigationManager, documentId]);

  const handleSuggestionClick = useCallback((suggestion: any) => {
    onNavigateToPage(suggestion.pageNumber);
  }, [onNavigateToPage]);

  const formatTime = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const formatReadingSpeed = (pagesPerMinute: number): string => {
    if (pagesPerMinute < 1) {
      const minutesPerPage = Math.round(1 / pagesPerMinute);
      return `${minutesPerPage} min/page`;
    }
    return `${pagesPerMinute.toFixed(1)} pages/min`;
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Smart Navigation
          </CardTitle>
          <CardDescription>
            Intelligent reading assistance and progress tracking
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="bookmarks">Bookmarks</TabsTrigger>
          <TabsTrigger value="outline">Outline</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="space-y-4">
          {/* Reading Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <BookOpen className="h-5 w-5" />
                Reading Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {progress ? (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Page {progress.currentPage} of {progress.totalPages}</span>
                      <span>{Math.round(progress.percentage)}% complete</span>
                    </div>
                    <Progress value={progress.percentage} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">Time Spent</div>
                        <div className="text-muted-foreground">
                          {formatTime(progress.timeSpent)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">Reading Speed</div>
                        <div className="text-muted-foreground">
                          {formatReadingSpeed(progress.readingSpeed)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Timer className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">Est. Remaining</div>
                        <div className="text-muted-foreground">
                          {Math.round(progress.estimatedTimeRemaining)} min
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">Last Read</div>
                        <div className="text-muted-foreground">
                          {progress.lastReadDate.toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center text-muted-foreground py-4">
                  Start reading to track your progress
                </div>
              )}
            </CardContent>
          </Card>

          {/* Smart Suggestions */}
          {suggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Lightbulb className="h-5 w-5" />
                  Smart Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {suggestions.slice(0, 3).map((suggestion, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          "p-2 rounded-full",
                          suggestion.type === 'bookmark' && "bg-blue-100 text-blue-600",
                          suggestion.type === 'section' && "bg-green-100 text-green-600",
                          suggestion.type === 'review' && "bg-orange-100 text-orange-600"
                        )}>
                          {suggestion.type === 'bookmark' && <Bookmark className="h-4 w-4" />}
                          {suggestion.type === 'section' && <BookOpen className="h-4 w-4" />}
                          {suggestion.type === 'review' && <Target className="h-4 w-4" />}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{suggestion.title}</div>
                          <div className="text-xs text-muted-foreground">
                            {suggestion.description}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          Page {suggestion.pageNumber}
                        </Badge>
                        <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="bookmarks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Bookmark className="h-5 w-5" />
                  Bookmarks ({bookmarks.length})
                </span>
                <Button size="sm" variant="outline">
                  Add Bookmark
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {bookmarks.map((bookmark) => (
                    <div
                      key={bookmark.id}
                      className="flex items-start justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleBookmarkClick(bookmark)}
                    >
                      <div className="flex items-start gap-3">
                        <div className={cn(
                          "p-1 rounded-full mt-1",
                          bookmark.type === 'manual' && "bg-blue-100 text-blue-600",
                          bookmark.type === 'auto' && "bg-gray-100 text-gray-600",
                          bookmark.type === 'important' && "bg-red-100 text-red-600",
                          bookmark.type === 'reference' && "bg-green-100 text-green-600",
                          bookmark.type === 'note' && "bg-yellow-100 text-yellow-600"
                        )}>
                          <Bookmark className="h-3 w-3" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm">{bookmark.title}</div>
                          {bookmark.description && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {bookmark.description}
                            </div>
                          )}
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              Page {bookmark.pageNumber}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {bookmark.type}
                            </Badge>
                            {bookmark.accessCount > 0 && (
                              <Badge variant="outline" className="text-xs">
                                {bookmark.accessCount} visits
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Star 
                          className={cn(
                            "h-4 w-4",
                            bookmark.metadata.importance > 0.7 
                              ? "text-yellow-500 fill-current" 
                              : "text-gray-300"
                          )} 
                        />
                      </div>
                    </div>
                  ))}
                  
                  {bookmarks.length === 0 && (
                    <div className="text-center text-muted-foreground py-8">
                      No bookmarks yet. Add some to keep track of important sections.
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="outline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Document Outline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-1">
                  {outline.map((item) => (
                    <div
                      key={item.id}
                      className={cn(
                        "flex items-center justify-between p-2 rounded cursor-pointer hover:bg-muted/50",
                        `ml-${item.level * 4}`
                      )}
                      onClick={() => handleOutlineClick(item)}
                    >
                      <div className="flex items-center gap-2">
                        {item.isRead ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <Circle className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className={cn(
                          "text-sm",
                          item.level === 1 && "font-semibold",
                          item.level === 2 && "font-medium",
                          item.isRead && "text-muted-foreground"
                        )}>
                          {item.title}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          Page {item.pageNumber}
                        </Badge>
                        {item.estimatedReadTime > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {item.estimatedReadTime}m
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {outline.length === 0 && (
                    <div className="text-center text-muted-foreground py-8">
                      Document outline will appear here when available
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Reading Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats ? (
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 rounded-lg bg-muted/50">
                    <div className="text-2xl font-bold text-blue-600">
                      {stats.sessionCount}
                    </div>
                    <div className="text-sm text-muted-foreground">Sessions</div>
                  </div>
                  
                  <div className="text-center p-4 rounded-lg bg-muted/50">
                    <div className="text-2xl font-bold text-green-600">
                      {stats.pagesRead}
                    </div>
                    <div className="text-sm text-muted-foreground">Pages Read</div>
                  </div>
                  
                  <div className="text-center p-4 rounded-lg bg-muted/50">
                    <div className="text-2xl font-bold text-purple-600">
                      {Math.round(stats.focusScore * 100)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Focus Score</div>
                  </div>
                  
                  <div className="text-center p-4 rounded-lg bg-muted/50">
                    <div className="text-2xl font-bold text-orange-600">
                      {stats.bookmarkCount}
                    </div>
                    <div className="text-sm text-muted-foreground">Bookmarks</div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  Reading insights will appear as you use the document
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
