"use client";

import React, { createContext, useContext, useState, useCallback, useEffect, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface TouchInterfaceConfig {
  // Touch target sizes
  minTouchTarget: number;
  comfortableTouchTarget: number;
  
  // Spacing and padding
  mobileSpacing: number;
  tabletSpacing: number;
  
  // Gesture settings
  enableGestures: boolean;
  gestureThreshold: number;
  
  // Accessibility
  enableHighContrast: boolean;
  enableLargeText: boolean;
  enableReducedMotion: boolean;
  
  // Performance
  enableHardwareAcceleration: boolean;
  enableVirtualization: boolean;
  
  // Platform-specific
  enableHapticFeedback: boolean;
  enableVoiceOver: boolean;
}

interface TouchInterfaceState {
  isTouch: boolean;
  isMobile: boolean;
  isTablet: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: { width: number; height: number };
  devicePixelRatio: number;
  hasHover: boolean;
  hasPointer: boolean;
  prefersReducedMotion: boolean;
  colorScheme: 'light' | 'dark';
}

interface TouchInterfaceContextValue {
  config: TouchInterfaceConfig;
  state: TouchInterfaceState;
  updateConfig: (updates: Partial<TouchInterfaceConfig>) => void;
  getTouchTargetClass: (size?: 'default' | 'comfortable' | 'large') => string;
  getSpacingClass: (type?: 'padding' | 'margin' | 'gap') => string;
  isAccessibilityEnabled: () => boolean;
  triggerHapticFeedback: (type?: 'light' | 'medium' | 'heavy') => void;
}

const DEFAULT_CONFIG: TouchInterfaceConfig = {
  minTouchTarget: 44,
  comfortableTouchTarget: 48,
  mobileSpacing: 16,
  tabletSpacing: 24,
  enableGestures: true,
  gestureThreshold: 10,
  enableHighContrast: false,
  enableLargeText: false,
  enableReducedMotion: false,
  enableHardwareAcceleration: true,
  enableVirtualization: true,
  enableHapticFeedback: true,
  enableVoiceOver: false,
};

const TouchInterfaceContext = createContext<TouchInterfaceContextValue | null>(null);

export function useTouchInterface() {
  const context = useContext(TouchInterfaceContext);
  if (!context) {
    throw new Error('useTouchInterface must be used within a TouchInterfaceProvider');
  }
  return context;
}

interface TouchInterfaceProviderProps {
  children: React.ReactNode;
  initialConfig?: Partial<TouchInterfaceConfig>;
}

export function TouchInterfaceProvider({ children, initialConfig = {} }: TouchInterfaceProviderProps) {
  const [config, setConfig] = useState<TouchInterfaceConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig,
  });

  const [state, setState] = useState<TouchInterfaceState>(() => {
    if (typeof window === 'undefined') {
      return {
        isTouch: false,
        isMobile: false,
        isTablet: false,
        orientation: 'portrait',
        screenSize: { width: 1024, height: 768 },
        devicePixelRatio: 1,
        hasHover: true,
        hasPointer: true,
        prefersReducedMotion: false,
        colorScheme: 'light',
      };
    }

    return {
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      isMobile: window.innerWidth < 768,
      isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
      orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
      screenSize: { width: window.innerWidth, height: window.innerHeight },
      devicePixelRatio: window.devicePixelRatio || 1,
      hasHover: window.matchMedia('(hover: hover)').matches,
      hasPointer: window.matchMedia('(pointer: fine)').matches,
      prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
    };
  });

  // Update state on window resize and orientation change
  useEffect(() => {
    const updateState = () => {
      setState(prev => ({
        ...prev,
        isMobile: window.innerWidth < 768,
        isTablet: window.innerWidth >= 768 && window.innerWidth < 1024,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        screenSize: { width: window.innerWidth, height: window.innerHeight },
      }));
    };

    const updateMediaQueries = () => {
      setState(prev => ({
        ...prev,
        hasHover: window.matchMedia('(hover: hover)').matches,
        hasPointer: window.matchMedia('(pointer: fine)').matches,
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        colorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
      }));
    };

    window.addEventListener('resize', updateState);
    window.addEventListener('orientationchange', updateState);
    
    // Listen for media query changes
    const hoverQuery = window.matchMedia('(hover: hover)');
    const pointerQuery = window.matchMedia('(pointer: fine)');
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const colorQuery = window.matchMedia('(prefers-color-scheme: dark)');

    hoverQuery.addEventListener('change', updateMediaQueries);
    pointerQuery.addEventListener('change', updateMediaQueries);
    motionQuery.addEventListener('change', updateMediaQueries);
    colorQuery.addEventListener('change', updateMediaQueries);

    return () => {
      window.removeEventListener('resize', updateState);
      window.removeEventListener('orientationchange', updateState);
      hoverQuery.removeEventListener('change', updateMediaQueries);
      pointerQuery.removeEventListener('change', updateMediaQueries);
      motionQuery.removeEventListener('change', updateMediaQueries);
      colorQuery.removeEventListener('change', updateMediaQueries);
    };
  }, []);

  // Update config based on accessibility preferences
  useEffect(() => {
    if (state.prefersReducedMotion && !config.enableReducedMotion) {
      setConfig(prev => ({ ...prev, enableReducedMotion: true }));
    }
  }, [state.prefersReducedMotion, config.enableReducedMotion]);

  const updateConfig = useCallback((updates: Partial<TouchInterfaceConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const getTouchTargetClass = useCallback((size: 'default' | 'comfortable' | 'large' = 'default') => {
    const classes = ['touch-target'];
    
    switch (size) {
      case 'comfortable':
        classes.push('touch-target-comfortable');
        break;
      case 'large':
        classes.push('touch-target-large');
        break;
      default:
        break;
    }

    if (state.isTouch) {
      classes.push('touch-device');
    }

    if (config.enableLargeText) {
      classes.push('large-text');
    }

    return classes.join(' ');
  }, [state.isTouch, config.enableLargeText]);

  const getSpacingClass = useCallback((type: 'padding' | 'margin' | 'gap' = 'padding') => {
    const spacing = state.isMobile ? config.mobileSpacing : config.tabletSpacing;
    const baseClass = state.isMobile ? 'mobile-spacing' : 'tablet-spacing';
    
    return `${baseClass} ${type}-${spacing}`;
  }, [state.isMobile, config.mobileSpacing, config.tabletSpacing]);

  const isAccessibilityEnabled = useCallback(() => {
    return config.enableHighContrast || config.enableLargeText || config.enableReducedMotion || config.enableVoiceOver;
  }, [config]);

  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!config.enableHapticFeedback || !state.isTouch) return;

    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
      };
      navigator.vibrate(patterns[type]);
    }
  }, [config.enableHapticFeedback, state.isTouch]);

  const contextValue: TouchInterfaceContextValue = useMemo(() => ({
    config,
    state,
    updateConfig,
    getTouchTargetClass,
    getSpacingClass,
    isAccessibilityEnabled,
    triggerHapticFeedback,
  }), [config, state, updateConfig, getTouchTargetClass, getSpacingClass, isAccessibilityEnabled, triggerHapticFeedback]);

  return (
    <TouchInterfaceContext.Provider value={contextValue}>
      <div
        className={cn(
          'touch-interface-root',
          state.isTouch && 'touch-device',
          state.isMobile && 'mobile-device',
          state.isTablet && 'tablet-device',
          state.orientation === 'landscape' && 'landscape',
          state.orientation === 'portrait' && 'portrait',
          !state.hasHover && 'no-hover',
          !state.hasPointer && 'coarse-pointer',
          config.enableHighContrast && 'high-contrast',
          config.enableLargeText && 'large-text',
          config.enableReducedMotion && 'reduced-motion',
          config.enableHardwareAcceleration && 'hardware-accelerated'
        )}
        style={{
          '--touch-target-min': `${config.minTouchTarget}px`,
          '--touch-target-comfortable': `${config.comfortableTouchTarget}px`,
          '--mobile-spacing': `${config.mobileSpacing}px`,
          '--tablet-spacing': `${config.tabletSpacing}px`,
          '--screen-width': `${state.screenSize.width}px`,
          '--screen-height': `${state.screenSize.height}px`,
          '--device-pixel-ratio': state.devicePixelRatio,
        } as React.CSSProperties}
      >
        {children}
      </div>
    </TouchInterfaceContext.Provider>
  );
}

// Touch-optimized components

interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: 'default' | 'comfortable' | 'large';
  hapticFeedback?: boolean;
  children: React.ReactNode;
}

export function TouchButton({ 
  size = 'default', 
  hapticFeedback = true, 
  className, 
  onClick, 
  children, 
  ...props 
}: TouchButtonProps) {
  const { getTouchTargetClass, triggerHapticFeedback } = useTouchInterface();

  const handleClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    if (hapticFeedback) {
      triggerHapticFeedback('light');
    }
    onClick?.(e);
  }, [onClick, hapticFeedback, triggerHapticFeedback]);

  return (
    <button
      className={cn(
        getTouchTargetClass(size),
        'transition-all duration-200',
        'active:scale-95 active:opacity-80',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      {children}
    </button>
  );
}

interface TouchScrollAreaProps {
  children: React.ReactNode;
  className?: string;
  enableMomentumScrolling?: boolean;
  enableOverscrollBehavior?: boolean;
}

export function TouchScrollArea({ 
  children, 
  className, 
  enableMomentumScrolling = true,
  enableOverscrollBehavior = true 
}: TouchScrollAreaProps) {
  const { state, config } = useTouchInterface();

  return (
    <div
      className={cn(
        'overflow-auto',
        enableMomentumScrolling && state.isTouch && 'momentum-scroll',
        enableOverscrollBehavior && 'overscroll-behavior-contain',
        config.enableReducedMotion && 'scroll-smooth-disabled',
        className
      )}
      style={{
        WebkitOverflowScrolling: enableMomentumScrolling ? 'touch' : 'auto',
        overscrollBehavior: enableOverscrollBehavior ? 'contain' : 'auto',
      }}
    >
      {children}
    </div>
  );
}

interface TouchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
}

export function TouchInput({ label, error, className, ...props }: TouchInputProps) {
  const { getTouchTargetClass, state } = useTouchInterface();

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium">
          {label}
        </label>
      )}
      <input
        className={cn(
          getTouchTargetClass('comfortable'),
          'w-full px-3 py-2 border rounded-md',
          'focus:outline-none focus:ring-2 focus:ring-primary',
          state.isTouch && 'text-base', // Prevent zoom on iOS
          error && 'border-destructive',
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}

// Hook for touch-optimized interactions
export function useTouchOptimized() {
  const { state, config, triggerHapticFeedback } = useTouchInterface();

  const handleTouchStart = useCallback((callback?: () => void) => {
    return (e: React.TouchEvent) => {
      if (config.enableHapticFeedback) {
        triggerHapticFeedback('light');
      }
      callback?.();
    };
  }, [config.enableHapticFeedback, triggerHapticFeedback]);

  const handleTouchEnd = useCallback((callback?: () => void) => {
    return (e: React.TouchEvent) => {
      callback?.();
    };
  }, []);

  const preventZoom = useCallback((e: React.TouchEvent) => {
    if (e.touches.length > 1) {
      e.preventDefault();
    }
  }, []);

  const getOptimizedProps = useCallback((options: {
    hapticFeedback?: boolean;
    preventZoom?: boolean;
    onTouch?: () => void;
  } = {}) => {
    const props: any = {};

    if (state.isTouch) {
      if (options.hapticFeedback !== false) {
        props.onTouchStart = handleTouchStart(options.onTouch);
      }
      
      if (options.preventZoom) {
        props.onTouchStart = preventZoom;
      }
    }

    return props;
  }, [state.isTouch, handleTouchStart, preventZoom]);

  return {
    isTouch: state.isTouch,
    isMobile: state.isMobile,
    isTablet: state.isTablet,
    handleTouchStart,
    handleTouchEnd,
    preventZoom,
    getOptimizedProps,
    triggerHapticFeedback,
  };
}
