"use client";

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Maximize, 
  Minimize,
  Share,
  Download,
  Search,
  Bookmark,
  Menu,
  X,
  Home,
  Settings,
  Eye,
  EyeOff,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GestureEngine, type GestureEvent, type GestureCallbacks } from '@/lib/mobile/gesture-engine';
import { useLayoutContext } from '@/components/layout/responsive-layout-manager';

interface MobilePDFViewerProps {
  file: File | string;
  initialPage?: number;
  onClose?: () => void;
  onPageChange?: (page: number) => void;
  onZoomChange?: (zoom: number) => void;
  enableGestures?: boolean;
  enableFullscreen?: boolean;
  showToolbar?: boolean;
  showPageIndicator?: boolean;
  className?: string;
}

interface ViewState {
  currentPage: number;
  totalPages: number;
  scale: number;
  rotation: number;
  isFullscreen: boolean;
  isLoading: boolean;
  error: string | null;
}

interface PanState {
  x: number;
  y: number;
  isDragging: boolean;
}

const ZOOM_LEVELS = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4, 5];
const MIN_ZOOM = 0.25;
const MAX_ZOOM = 5;

export default function MobilePDFViewer({
  file,
  initialPage = 1,
  onClose,
  onPageChange,
  onZoomChange,
  enableGestures = true,
  enableFullscreen = true,
  showToolbar = true,
  showPageIndicator = true,
  className,
}: MobilePDFViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gestureEngineRef = useRef<GestureEngine | null>(null);
  const layoutContext = useLayoutContext();

  const [viewState, setViewState] = useState<ViewState>({
    currentPage: initialPage,
    totalPages: 1,
    scale: 1,
    rotation: 0,
    isFullscreen: false,
    isLoading: true,
    error: null,
  });

  const [panState, setPanState] = useState<PanState>({
    x: 0,
    y: 0,
    isDragging: false,
  });

  const [showControls, setShowControls] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<number | null>(null);

  // Auto-hide controls after inactivity
  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    
    setShowControls(true);
    const timeout = window.setTimeout(() => {
      if (viewState.isFullscreen) {
        setShowControls(false);
      }
    }, 3000);
    
    setControlsTimeout(timeout);
  }, [controlsTimeout, viewState.isFullscreen]);

  // Gesture callbacks
  const gestureCallbacks: GestureCallbacks = useMemo(() => ({
    onTap: (event: GestureEvent) => {
      resetControlsTimeout();
      
      // Center tap toggles controls in fullscreen
      if (viewState.isFullscreen) {
        const rect = containerRef.current?.getBoundingClientRect();
        if (rect) {
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          const tapX = event.center.x - rect.left;
          const tapY = event.center.y - rect.top;
          
          // Check if tap is in center area (30% of screen)
          const centerThreshold = 0.15;
          if (
            Math.abs(tapX - centerX) < rect.width * centerThreshold &&
            Math.abs(tapY - centerY) < rect.height * centerThreshold
          ) {
            setShowControls(prev => !prev);
          }
        }
      }
    },

    onDoubleTap: (event: GestureEvent) => {
      // Double tap to zoom
      const newScale = viewState.scale === 1 ? 2 : 1;
      setViewState(prev => ({ ...prev, scale: newScale }));
      onZoomChange?.(newScale);
      resetControlsTimeout();
    },

    onPinch: (event: GestureEvent) => {
      const newScale = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, viewState.scale * event.scale));
      setViewState(prev => ({ ...prev, scale: newScale }));
      onZoomChange?.(newScale);
      resetControlsTimeout();
    },

    onPan: (event: GestureEvent) => {
      if (event.touches.length === 1) {
        // Single finger pan for navigation
        const sensitivity = 0.5;
        setPanState(prev => ({
          ...prev,
          x: prev.x + event.deltaX * sensitivity,
          y: prev.y + event.deltaY * sensitivity,
          isDragging: true,
        }));
      }
      resetControlsTimeout();
    },

    onSwipe: (event: GestureEvent) => {
      const { deltaX, velocity } = event;
      const minSwipeDistance = 50;
      const minVelocity = 0.3;
      
      if (Math.abs(deltaX) > minSwipeDistance && velocity.magnitude > minVelocity) {
        if (deltaX > 0 && viewState.currentPage > 1) {
          // Swipe right - previous page
          handlePageChange(viewState.currentPage - 1);
        } else if (deltaX < 0 && viewState.currentPage < viewState.totalPages) {
          // Swipe left - next page
          handlePageChange(viewState.currentPage + 1);
        }
      }
      resetControlsTimeout();
    },

    onTwoFingerTap: () => {
      // Two finger tap to rotate
      const newRotation = (viewState.rotation + 90) % 360;
      setViewState(prev => ({ ...prev, rotation: newRotation }));
      resetControlsTimeout();
    },

    onLongPress: () => {
      // Long press for context menu or bookmark
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      resetControlsTimeout();
    },

    onGestureEnd: () => {
      setPanState(prev => ({ ...prev, isDragging: false }));
    },
  }), [viewState, onZoomChange, resetControlsTimeout]);

  // Initialize gesture engine
  useEffect(() => {
    if (!enableGestures || !containerRef.current) return;

    gestureEngineRef.current = new GestureEngine(
      containerRef.current,
      gestureCallbacks,
      {
        tapTimeout: 300,
        doubleTapTimeout: 300,
        longPressTimeout: 500,
        swipeMinDistance: 50,
        pinchMinScale: MIN_ZOOM,
        pinchMaxScale: MAX_ZOOM,
        enableHapticFeedback: true,
      }
    );

    return () => {
      gestureEngineRef.current?.destroy();
    };
  }, [enableGestures, gestureCallbacks]);

  // Handle page navigation
  const handlePageChange = useCallback((page: number) => {
    if (page >= 1 && page <= viewState.totalPages) {
      setViewState(prev => ({ ...prev, currentPage: page }));
      onPageChange?.(page);
      
      // Reset pan position when changing pages
      setPanState({ x: 0, y: 0, isDragging: false });
    }
  }, [viewState.totalPages, onPageChange]);

  // Handle zoom controls
  const handleZoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.findIndex(level => level >= viewState.scale);
    const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
    const newScale = ZOOM_LEVELS[nextIndex];
    setViewState(prev => ({ ...prev, scale: newScale }));
    onZoomChange?.(newScale);
  }, [viewState.scale, onZoomChange]);

  const handleZoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.findIndex(level => level >= viewState.scale);
    const prevIndex = Math.max(currentIndex - 1, 0);
    const newScale = ZOOM_LEVELS[prevIndex];
    setViewState(prev => ({ ...prev, scale: newScale }));
    onZoomChange?.(newScale);
  }, [viewState.scale, onZoomChange]);

  // Handle fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    if (!enableFullscreen) return;

    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen?.();
      setViewState(prev => ({ ...prev, isFullscreen: true }));
    } else {
      document.exitFullscreen?.();
      setViewState(prev => ({ ...prev, isFullscreen: false }));
    }
  }, [enableFullscreen]);

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setViewState(prev => ({ 
        ...prev, 
        isFullscreen: !!document.fullscreenElement 
      }));
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [controlsTimeout]);

  const isMobile = layoutContext?.isMobile || false;
  const isTablet = layoutContext?.isTablet || false;

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full h-full bg-black overflow-hidden",
        "touch-none select-none", // Prevent default touch behaviors
        viewState.isFullscreen && "fixed inset-0 z-50",
        className
      )}
      data-testid="mobile-pdf-viewer"
    >
      {/* PDF Canvas */}
      <div
        className="absolute inset-0 flex items-center justify-center"
        style={{
          transform: `translate(${panState.x}px, ${panState.y}px)`,
          transition: panState.isDragging ? 'none' : 'transform 0.3s ease-out',
        }}
      >
        <canvas
          ref={canvasRef}
          className="max-w-full max-h-full"
          style={{
            transform: `scale(${viewState.scale}) rotate(${viewState.rotation}deg)`,
            transformOrigin: 'center',
            transition: 'transform 0.3s ease-out',
          }}
        />
      </div>

      {/* Loading State */}
      {viewState.isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <Card className="p-6">
            <CardContent className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="text-sm">Loading PDF...</p>
              <Progress value={50} className="w-48" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error State */}
      {viewState.error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <Card className="p-6 max-w-sm mx-4">
            <CardContent className="text-center space-y-4">
              <div className="text-destructive">
                <X className="h-8 w-8 mx-auto mb-2" />
                <p className="font-medium">Error Loading PDF</p>
              </div>
              <p className="text-sm text-muted-foreground">{viewState.error}</p>
              <Button onClick={onClose} variant="outline" className="w-full">
                Close
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Toolbar */}
      {showToolbar && showControls && (
        <div className={cn(
          "absolute top-0 left-0 right-0 z-10",
          "bg-black/80 backdrop-blur-sm",
          "safe-area-top",
          viewState.isFullscreen ? "animate-in slide-in-from-top duration-300" : ""
        )}>
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <ChevronLeft className="h-4 w-4" />
                {!isMobile && <span className="ml-1">Back</span>}
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <Search className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <Bookmark className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <Share className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Controls */}
      {showControls && (
        <div className={cn(
          "absolute bottom-0 left-0 right-0 z-10",
          "bg-black/80 backdrop-blur-sm",
          "safe-area-bottom",
          viewState.isFullscreen ? "animate-in slide-in-from-bottom duration-300" : ""
        )}>
          {/* Page Indicator */}
          {showPageIndicator && (
            <div className="flex items-center justify-center py-2">
              <Badge variant="secondary" className="text-xs">
                {viewState.currentPage} of {viewState.totalPages}
              </Badge>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex items-center justify-between p-4">
            {/* Navigation */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlePageChange(viewState.currentPage - 1)}
                disabled={viewState.currentPage <= 1}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handlePageChange(viewState.currentPage + 1)}
                disabled={viewState.currentPage >= viewState.totalPages}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                disabled={viewState.scale <= MIN_ZOOM}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Badge variant="secondary" className="text-xs min-w-[3rem] text-center">
                {Math.round(viewState.scale * 100)}%
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                disabled={viewState.scale >= MAX_ZOOM}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>

            {/* Additional Controls */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewState(prev => ({ 
                  ...prev, 
                  rotation: (prev.rotation + 90) % 360 
                }))}
                className="text-white hover:bg-white/20 touch-target-comfortable"
              >
                <RotateCw className="h-4 w-4" />
              </Button>
              {enableFullscreen && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleFullscreenToggle}
                  className="text-white hover:bg-white/20 touch-target-comfortable"
                >
                  {viewState.isFullscreen ? (
                    <Minimize className="h-4 w-4" />
                  ) : (
                    <Maximize className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Gesture Hints (for first-time users) */}
      {enableGestures && !viewState.isFullscreen && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
          <div className="bg-black/60 text-white text-xs p-2 rounded-lg opacity-0 animate-pulse">
            <p>• Tap to show/hide controls</p>
            <p>• Double tap to zoom</p>
            <p>• Pinch to zoom</p>
            <p>• Swipe to navigate</p>
          </div>
        </div>
      )}
    </div>
  );
}
