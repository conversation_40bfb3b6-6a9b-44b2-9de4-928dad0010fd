"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Document } from 'react-pdf';
import { VirtualPageRenderer, DEFAULT_VIRTUAL_CONFIG, type VirtualRenderConfig } from '@/lib/virtual-renderer';
import { useCacheManager } from '@/hooks/use-cache-manager';
import PDFSimplePage from './pdf-simple-page';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { extractPDFDocument } from '@/lib/types/pdf';
import type { Annotation } from '../annotations/pdf-annotations';
import type { FormField, FormData } from '../forms/pdf-form-manager';

interface VirtualizedPDFViewerProps {
  file: string | File;
  scale?: number;
  rotation?: number;
  className?: string;
  
  // Virtual rendering config
  virtualConfig?: Partial<VirtualRenderConfig>;
  enableVirtualization?: boolean;
  
  // PDF features
  searchText?: string;
  annotations?: Annotation[];
  selectedTool?: string | null;
  selectedColor?: string;
  onAnnotationAdd?: (annotation: Omit<Annotation, "id" | "timestamp">) => void;
  formFields?: FormField[];
  formData?: FormData;
  onFormDataChange?: (data: FormData) => void;
  isFormDesignMode?: boolean;
  
  // Feature toggles
  enableAnnotations?: boolean;
  enableForms?: boolean;
  enableTextSelection?: boolean;
  enableSearch?: boolean;
  enableContextMenu?: boolean;
  
  // Callbacks
  onDocumentLoadSuccess?: (pdf: { numPages: number }) => void;
  onDocumentLoadError?: (error: Error) => void;
  onPageChange?: (pageNumber: number) => void;
}

interface VirtualPagePlaceholder {
  pageNumber: number;
  height: number;
  width: number;
  isVisible: boolean;
  isLoaded: boolean;
  isLoading: boolean;
}

export default function VirtualizedPDFViewer({
  file,
  scale = 1.0,
  rotation = 0,
  className,
  virtualConfig = {},
  enableVirtualization = true,
  searchText = '',
  annotations = [],
  selectedTool = null,
  selectedColor = '#FFEB3B',
  onAnnotationAdd,
  formFields = [],
  formData = {},
  onFormDataChange,
  isFormDesignMode = false,
  enableAnnotations = true,
  enableForms = true,
  enableTextSelection = true,
  enableSearch = true,
  enableContextMenu = false,
  onDocumentLoadSuccess,
  onDocumentLoadError,
  onPageChange,
}: VirtualizedPDFViewerProps) {
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [numPages, setNumPages] = useState(0);
  const [virtualPages, setVirtualPages] = useState<VirtualPagePlaceholder[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [performanceStats, setPerformanceStats] = useState({
    renderedPages: 0,
    cachedPages: 0,
    averageRenderTime: 0,
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const virtualRenderer = useRef<VirtualPageRenderer | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize cache manager
  const cacheManager = useCacheManager({
    config: {
      enableServiceWorker: true,
      enablePreloading: true,
      enableBackgroundSync: true,
    },
    enableAutoStats: true,
    statsInterval: 10000, // Update every 10 seconds
  });

  // Merge virtual config with defaults
  const finalVirtualConfig = useMemo(() => ({
    ...DEFAULT_VIRTUAL_CONFIG,
    ...virtualConfig,
  }), [virtualConfig]);

  // Initialize virtual renderer
  useEffect(() => {
    if (!enableVirtualization) return;

    virtualRenderer.current = new VirtualPageRenderer(finalVirtualConfig);

    return () => {
      virtualRenderer.current?.destroy();
    };
  }, [enableVirtualization, finalVirtualConfig]);

  // Handle document load success
  const handleDocumentLoadSuccess = useCallback(async (pdf: { numPages: number }) => {
    setNumPages(pdf.numPages);
    onDocumentLoadSuccess?.(pdf);

    if (!enableVirtualization || !virtualRenderer.current) {
      setIsInitialized(true);
      return;
    }

    try {
      // Extract the actual PDF document proxy
      const pdfDoc = extractPDFDocument(pdf);
      if (!pdfDoc) {
        throw new Error('Failed to extract PDF document');
      }

      setPdfDocument(pdfDoc);
      
      // Initialize virtual renderer
      await virtualRenderer.current.initialize(pdfDoc);
      
      // Get initial virtual pages
      const state = virtualRenderer.current.getState();
      const pages: VirtualPagePlaceholder[] = [];
      
      for (let i = 1; i <= pdf.numPages; i++) {
        const pageItem = state.pages.get(i);
        if (pageItem) {
          pages.push({
            pageNumber: i,
            height: pageItem.height,
            width: pageItem.width,
            isVisible: pageItem.isVisible,
            isLoaded: pageItem.isLoaded,
            isLoading: pageItem.isLoading,
          });
        }
      }
      
      setVirtualPages(pages);
      setIsInitialized(true);
      
      // Update performance stats
      updatePerformanceStats();
      
    } catch (error) {
      console.error('Failed to initialize virtual renderer:', error);
      onDocumentLoadError?.(error as Error);
    }
  }, [enableVirtualization, onDocumentLoadSuccess, onDocumentLoadError]);

  // Handle document load error
  const handleDocumentLoadError = useCallback((error: Error) => {
    console.error('PDF load error:', error);
    onDocumentLoadError?.(error);
  }, [onDocumentLoadError]);

  // Handle scroll events for virtualization
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    if (!enableVirtualization || !virtualRenderer.current) return;

    const scrollTop = event.currentTarget.scrollTop;
    
    // Debounce scroll updates
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      virtualRenderer.current?.updateVisibleRange(scrollTop);
      
      // Update virtual pages state
      const state = virtualRenderer.current?.getState();
      if (state) {
        const updatedPages = virtualPages.map(page => {
          const pageItem = state.pages.get(page.pageNumber);
          return pageItem ? {
            ...page,
            isVisible: pageItem.isVisible,
            isLoaded: pageItem.isLoaded,
            isLoading: pageItem.isLoading,
          } : page;
        });
        
        setVirtualPages(updatedPages);
        updatePerformanceStats();
      }
    }, 16); // ~60fps
  }, [enableVirtualization, virtualPages]);

  // Update performance statistics
  const updatePerformanceStats = useCallback(() => {
    if (!virtualRenderer.current) return;

    const state = virtualRenderer.current.getState();
    const cacheStats = cacheManager.stats;

    setPerformanceStats({
      renderedPages: state.cachedPages.size,
      cachedPages: state.cachedPages.size + (cacheStats?.pages.memoryPages || 0),
      averageRenderTime: Math.round(state.metrics.averageRenderTime),
    });
  }, [cacheManager.stats]);

  // Update virtual renderer config when viewport changes
  useEffect(() => {
    if (!virtualRenderer.current || !containerRef.current) return;

    const updateViewport = () => {
      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();
      virtualRenderer.current?.updateConfig({
        viewportHeight: rect.height,
        viewportWidth: rect.width,
      });
    };

    updateViewport();
    
    const resizeObserver = new ResizeObserver(updateViewport);
    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [isInitialized]);

  // Render a single page (virtual or real)
  const renderPage = useCallback((pageData: VirtualPagePlaceholder) => {
    const { pageNumber, height, width, isVisible, isLoaded } = pageData;

    // For virtualization, only render visible or recently accessed pages
    if (enableVirtualization && !isVisible && !isLoaded) {
      return (
        <div
          key={pageNumber}
          className="flex items-center justify-center bg-muted/20 border border-dashed border-muted-foreground/20 rounded"
          style={{ 
            height: `${height * scale}px`, 
            width: `${width * scale}px`,
            minHeight: '200px'
          }}
        >
          <div className="text-center text-muted-foreground">
            <div className="text-sm font-medium">Page {pageNumber}</div>
            <div className="text-xs">Loading...</div>
          </div>
        </div>
      );
    }

    // Render actual PDF page
    return (
      <div key={pageNumber} className="relative">
        <PDFSimplePage
          pageNumber={pageNumber}
          scale={scale}
          rotation={rotation}
          className="shadow-sm"
          pdfDocument={pdfDocument}
          searchText={searchText}
          annotations={annotations}
          selectedTool={selectedTool}
          selectedColor={selectedColor}
          onAnnotationAdd={onAnnotationAdd}
          formFields={formFields}
          formData={formData}
          onFormDataChange={onFormDataChange}
          isFormDesignMode={isFormDesignMode}
          enableAnnotations={enableAnnotations}
          enableForms={enableForms}
          enableTextSelection={enableTextSelection}
          enableSearch={enableSearch}
          enableContextMenu={enableContextMenu}
        />
        
        {/* Page number overlay */}
        <div className="absolute top-2 right-2 z-10">
          <Badge variant="secondary" className="text-xs">
            {pageNumber}
          </Badge>
        </div>
      </div>
    );
  }, [
    enableVirtualization,
    scale,
    rotation,
    pdfDocument,
    searchText,
    annotations,
    selectedTool,
    selectedColor,
    onAnnotationAdd,
    formFields,
    formData,
    onFormDataChange,
    isFormDesignMode,
    enableAnnotations,
    enableForms,
    enableTextSelection,
    enableSearch,
    enableContextMenu,
  ]);

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <div className="text-sm text-muted-foreground">
            {enableVirtualization ? 'Initializing virtual renderer...' : 'Loading PDF...'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Performance stats (development only) */}
      {process.env.NODE_ENV === 'development' && enableVirtualization && (
        <div className="flex gap-2 p-2 bg-muted/20 text-xs">
          <Badge variant="outline">
            Rendered: {performanceStats.renderedPages}/{numPages}
          </Badge>
          <Badge variant="outline">
            Cached: {performanceStats.cachedPages}
          </Badge>
          <Badge variant="outline">
            Avg Render: {performanceStats.averageRenderTime}ms
          </Badge>
        </div>
      )}

      {/* PDF Document Container */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-auto"
        onScroll={handleScroll}
      >
        <Document
          file={file}
          onLoadSuccess={handleDocumentLoadSuccess}
          onLoadError={handleDocumentLoadError}
          loading={
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          }
          className="max-w-full"
        >
          <div className="flex flex-col items-center gap-4 p-4">
            {enableVirtualization ? (
              // Virtual rendering mode
              virtualPages.map(renderPage)
            ) : (
              // Traditional rendering mode
              Array.from({ length: numPages }, (_, index) => {
                const pageNumber = index + 1;
                return renderPage({
                  pageNumber,
                  height: 792, // Default height
                  width: 612,  // Default width
                  isVisible: true,
                  isLoaded: true,
                  isLoading: false,
                });
              })
            )}
          </div>
        </Document>
      </div>
    </div>
  );
}
