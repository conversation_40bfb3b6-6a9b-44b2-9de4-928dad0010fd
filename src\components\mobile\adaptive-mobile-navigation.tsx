"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Menu, 
  Home, 
  FileText, 
  Search, 
  Bookmark, 
  Settings, 
  Download, 
  Share, 
  Eye, 
  EyeOff,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  MoreHorizontal,
  Grid3X3,
  List,
  Layers,
  Palette,
  Zap,
  Shield,
  HelpCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLayoutContext } from '@/components/layout/responsive-layout-manager';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  badge?: string | number;
  disabled?: boolean;
  hidden?: boolean;
  category?: 'primary' | 'secondary' | 'tools' | 'settings';
}

interface AdaptiveMobileNavigationProps {
  items: NavigationItem[];
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  showPageNavigation?: boolean;
  showQuickActions?: boolean;
  position?: 'bottom' | 'top' | 'floating';
  variant?: 'tabs' | 'drawer' | 'sheet' | 'auto';
  className?: string;
}

interface QuickAction {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  action: () => void;
  primary?: boolean;
}

const DEFAULT_QUICK_ACTIONS: QuickAction[] = [
  {
    id: 'search',
    icon: Search,
    label: 'Search',
    action: () => console.log('Search'),
    primary: true,
  },
  {
    id: 'bookmark',
    icon: Bookmark,
    label: 'Bookmark',
    action: () => console.log('Bookmark'),
  },
  {
    id: 'share',
    icon: Share,
    label: 'Share',
    action: () => console.log('Share'),
  },
  {
    id: 'download',
    icon: Download,
    label: 'Download',
    action: () => console.log('Download'),
  },
];

export default function AdaptiveMobileNavigation({
  items,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  showPageNavigation = true,
  showQuickActions = true,
  position = 'bottom',
  variant = 'auto',
  className,
}: AdaptiveMobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string>('primary');
  const [quickActions] = useState<QuickAction[]>(DEFAULT_QUICK_ACTIONS);
  const layoutContext = useLayoutContext();

  // Determine the best navigation variant based on context
  const navigationVariant = useMemo(() => {
    if (variant !== 'auto') return variant;
    
    if (layoutContext?.isMobile) {
      return position === 'bottom' ? 'tabs' : 'drawer';
    } else if (layoutContext?.isTablet) {
      return 'sheet';
    } else {
      return 'sheet';
    }
  }, [variant, layoutContext, position]);

  // Group items by category
  const categorizedItems = useMemo(() => {
    const categories: Record<string, NavigationItem[]> = {
      primary: [],
      secondary: [],
      tools: [],
      settings: [],
    };

    items.forEach(item => {
      if (!item.hidden) {
        const category = item.category || 'primary';
        categories[category].push(item);
      }
    });

    return categories;
  }, [items]);

  // Get visible primary items for tab navigation
  const primaryItems = useMemo(() => {
    return categorizedItems.primary.slice(0, 4); // Limit to 4 for mobile tabs
  }, [categorizedItems.primary]);

  // Handle page navigation
  const handlePageChange = useCallback((direction: 'prev' | 'next') => {
    if (!onPageChange) return;
    
    if (direction === 'prev' && currentPage > 1) {
      onPageChange(currentPage - 1);
    } else if (direction === 'next' && currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  }, [currentPage, totalPages, onPageChange]);

  // Handle quick action
  const handleQuickAction = useCallback((action: QuickAction) => {
    action.action();
    
    // Add haptic feedback on mobile
    if ('vibrate' in navigator && layoutContext?.isMobile) {
      navigator.vibrate(10);
    }
  }, [layoutContext?.isMobile]);

  // Render page navigation controls
  const renderPageNavigation = () => {
    if (!showPageNavigation || totalPages <= 1) return null;

    return (
      <div className="flex items-center justify-center space-x-4 py-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageChange('prev')}
          disabled={currentPage <= 1}
          className="touch-target-comfortable"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-xs">
            {currentPage}
          </Badge>
          <span className="text-xs text-muted-foreground">of</span>
          <Badge variant="outline" className="text-xs">
            {totalPages}
          </Badge>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageChange('next')}
          disabled={currentPage >= totalPages}
          className="touch-target-comfortable"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  // Render quick actions
  const renderQuickActions = () => {
    if (!showQuickActions) return null;

    return (
      <div className="flex items-center justify-center space-x-2 py-2">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.id}
              variant={action.primary ? "default" : "ghost"}
              size="sm"
              onClick={() => handleQuickAction(action)}
              className="touch-target-comfortable"
              title={action.label}
            >
              <Icon className="h-4 w-4" />
              {layoutContext?.isTablet && (
                <span className="ml-1 text-xs">{action.label}</span>
              )}
            </Button>
          );
        })}
      </div>
    );
  };

  // Render navigation items
  const renderNavigationItems = (items: NavigationItem[]) => (
    <div className="space-y-1">
      {items.map((item) => {
        const Icon = item.icon;
        return (
          <Button
            key={item.id}
            variant="ghost"
            onClick={() => {
              item.action();
              setIsOpen(false);
            }}
            disabled={item.disabled}
            className="w-full justify-start touch-target-comfortable"
          >
            <Icon className="h-4 w-4 mr-3" />
            <span className="flex-1 text-left">{item.label}</span>
            {item.badge && (
              <Badge variant="secondary" className="text-xs">
                {item.badge}
              </Badge>
            )}
          </Button>
        );
      })}
    </div>
  );

  // Render tab navigation (mobile bottom tabs)
  const renderTabNavigation = () => (
    <div className={cn(
      "fixed bottom-0 left-0 right-0 z-50",
      "bg-background border-t",
      "safe-area-bottom",
      className
    )}>
      {renderPageNavigation()}
      <Separator />
      
      <div className="grid grid-cols-5 gap-1 p-2">
        {primaryItems.map((item) => {
          const Icon = item.icon;
          return (
            <Button
              key={item.id}
              variant="ghost"
              onClick={item.action}
              disabled={item.disabled}
              className="flex flex-col items-center space-y-1 h-auto py-2 touch-target-comfortable"
            >
              <Icon className="h-4 w-4" />
              <span className="text-xs truncate">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="text-xs absolute -top-1 -right-1">
                  {item.badge}
                </Badge>
              )}
            </Button>
          );
        })}
        
        {/* More menu */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              className="flex flex-col items-center space-y-1 h-auto py-2 touch-target-comfortable"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="text-xs">More</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[80vh]">
            <SheetHeader>
              <SheetTitle>Menu</SheetTitle>
              <SheetDescription>
                Access all features and settings
              </SheetDescription>
            </SheetHeader>
            <ScrollArea className="h-full py-4">
              <div className="space-y-6">
                {showQuickActions && (
                  <div>
                    <h4 className="text-sm font-medium mb-3">Quick Actions</h4>
                    {renderQuickActions()}
                  </div>
                )}
                
                {Object.entries(categorizedItems).map(([category, items]) => {
                  if (items.length === 0 || category === 'primary') return null;
                  
                  return (
                    <div key={category}>
                      <h4 className="text-sm font-medium mb-3 capitalize">
                        {category}
                      </h4>
                      {renderNavigationItems(items)}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );

  // Render drawer navigation (mobile side drawer)
  const renderDrawerNavigation = () => (
    <div className={cn(
      position === 'top' ? "fixed top-0" : "fixed bottom-0",
      "left-0 right-0 z-50",
      "bg-background border-t",
      "safe-area-bottom",
      className
    )}>
      <div className="flex items-center justify-between p-4">
        {renderPageNavigation()}
        
        <Drawer open={isOpen} onOpenChange={setIsOpen}>
          <DrawerTrigger asChild>
            <Button variant="outline" size="sm" className="touch-target-comfortable">
              <Menu className="h-4 w-4 mr-2" />
              Menu
            </Button>
          </DrawerTrigger>
          <DrawerContent className="h-[80vh]">
            <DrawerHeader>
              <DrawerTitle>Navigation</DrawerTitle>
              <DrawerDescription>
                Access all features and tools
              </DrawerDescription>
            </DrawerHeader>
            <ScrollArea className="h-full p-4">
              <div className="space-y-6">
                {showQuickActions && (
                  <div>
                    <h4 className="text-sm font-medium mb-3">Quick Actions</h4>
                    {renderQuickActions()}
                  </div>
                )}
                
                {Object.entries(categorizedItems).map(([category, items]) => {
                  if (items.length === 0) return null;
                  
                  return (
                    <div key={category}>
                      <h4 className="text-sm font-medium mb-3 capitalize">
                        {category}
                      </h4>
                      {renderNavigationItems(items)}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </DrawerContent>
        </Drawer>
      </div>
    </div>
  );

  // Render sheet navigation (tablet/desktop)
  const renderSheetNavigation = () => (
    <div className={cn(
      "fixed top-4 right-4 z-50",
      className
    )}>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="touch-target-comfortable">
            <Menu className="h-4 w-4 mr-2" />
            Menu
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-80">
          <SheetHeader>
            <SheetTitle>Navigation</SheetTitle>
            <SheetDescription>
              Access all features and tools
            </SheetDescription>
          </SheetHeader>
          <ScrollArea className="h-full py-4">
            <div className="space-y-6">
              {showPageNavigation && (
                <div>
                  <h4 className="text-sm font-medium mb-3">Page Navigation</h4>
                  {renderPageNavigation()}
                </div>
              )}
              
              {showQuickActions && (
                <div>
                  <h4 className="text-sm font-medium mb-3">Quick Actions</h4>
                  {renderQuickActions()}
                </div>
              )}
              
              {Object.entries(categorizedItems).map(([category, items]) => {
                if (items.length === 0) return null;
                
                return (
                  <div key={category}>
                    <h4 className="text-sm font-medium mb-3 capitalize">
                      {category}
                    </h4>
                    {renderNavigationItems(items)}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </div>
  );

  // Render floating navigation (overlay)
  const renderFloatingNavigation = () => (
    <div className={cn(
      "fixed bottom-4 right-4 z-50",
      className
    )}>
      <div className="flex flex-col items-end space-y-2">
        {showPageNavigation && (
          <div className="bg-background border rounded-lg p-2 shadow-lg">
            {renderPageNavigation()}
          </div>
        )}
        
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button size="lg" className="rounded-full shadow-lg touch-target-comfortable">
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[60vh]">
            <SheetHeader>
              <SheetTitle>Quick Menu</SheetTitle>
            </SheetHeader>
            <ScrollArea className="h-full py-4">
              <div className="space-y-4">
                {showQuickActions && renderQuickActions()}
                
                <div className="grid grid-cols-2 gap-2">
                  {primaryItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <Button
                        key={item.id}
                        variant="outline"
                        onClick={() => {
                          item.action();
                          setIsOpen(false);
                        }}
                        disabled={item.disabled}
                        className="flex flex-col items-center space-y-2 h-auto py-4 touch-target-comfortable"
                      >
                        <Icon className="h-6 w-6" />
                        <span className="text-xs">{item.label}</span>
                        {item.badge && (
                          <Badge variant="secondary" className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </ScrollArea>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );

  // Render based on variant
  switch (navigationVariant) {
    case 'tabs':
      return renderTabNavigation();
    case 'drawer':
      return renderDrawerNavigation();
    case 'sheet':
      return renderSheetNavigation();
    default:
      return position === 'floating' ? renderFloatingNavigation() : renderTabNavigation();
  }
}
