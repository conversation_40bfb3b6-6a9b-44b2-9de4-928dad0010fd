import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { MemoryManager, type MemoryConfig } from '@/lib/pdf/memory-manager';

// Mock Worker
class MockWorker {
  onmessage: ((event: MessageEvent) => void) | null = null;
  
  constructor(public scriptURL: string) {}
  
  postMessage(message: any): void {
    // Simulate compression/decompression
    setTimeout(() => {
      if (this.onmessage) {
        if (message.compress) {
          const compressed = JSON.stringify(message.data);
          this.onmessage(new MessageEvent('message', {
            data: {
              id: message.id,
              compressed,
              originalSize: JSON.stringify(message.data).length,
              compressedSize: compressed.length * 0.7, // Simulate 30% compression
            }
          }));
        } else {
          const decompressed = JSON.parse(message.data);
          this.onmessage(new MessageEvent('message', {
            data: {
              id: message.id,
              decompressed,
            }
          }));
        }
      }
    }, 10);
  }
  
  terminate(): void {}
}

// Mock global Worker
global.Worker = MockWorker as any;
global.URL = {
  createObjectURL: vi.fn(() => 'mock-url'),
  revokeObjectURL: vi.fn(),
} as any;

// Mock performance.memory
Object.defineProperty(performance, 'memory', {
  value: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024, // 2GB
  },
  writable: true,
});

describe('MemoryManager', () => {
  let memoryManager: MemoryManager;

  beforeEach(() => {
    vi.clearAllMocks();
    memoryManager = new MemoryManager({
      maxMemoryMB: 10, // Small limit for testing
      maxCacheItems: 100,
      cleanupInterval: 100, // Fast cleanup for testing
    });
  });

  afterEach(() => {
    memoryManager.destroy();
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      const manager = new MemoryManager();
      expect(manager).toBeDefined();
      expect(manager.getSize()).toBe(0);
      expect(manager.getMemoryUsage()).toBe(0);
      manager.destroy();
    });

    it('should initialize with custom config', () => {
      const config: Partial<MemoryConfig> = {
        maxMemoryMB: 256,
        compressionEnabled: false,
        preloadStrategy: 'none',
      };
      
      const manager = new MemoryManager(config);
      expect(manager).toBeDefined();
      manager.destroy();
    });

    it('should initialize metrics correctly', () => {
      const metrics = memoryManager.getMetrics();
      expect(metrics.totalMemoryUsed).toBe(0);
      expect(metrics.itemCount).toBe(0);
      expect(metrics.cacheHitRate).toBe(0);
      expect(metrics.compressionRatio).toBe(0);
      expect(metrics.memoryPressure).toBe('low');
    });
  });

  describe('Cache Operations', () => {
    it('should store and retrieve items', async () => {
      const testData = { test: 'data', value: 123 };
      
      const stored = await memoryManager.store('test-1', testData);
      expect(stored).toBe(true);
      
      const retrieved = await memoryManager.get('test-1');
      expect(retrieved).toEqual(testData);
      
      expect(memoryManager.has('test-1')).toBe(true);
      expect(memoryManager.getSize()).toBe(1);
    });

    it('should handle different data types', async () => {
      const stringData = 'test string';
      const arrayBuffer = new ArrayBuffer(1024);
      const imageData = new ImageData(100, 100);
      
      await memoryManager.store('string', stringData);
      await memoryManager.store('buffer', arrayBuffer);
      await memoryManager.store('image', imageData);
      
      expect(await memoryManager.get('string')).toBe(stringData);
      expect(await memoryManager.get('buffer')).toBe(arrayBuffer);
      expect(await memoryManager.get('image')).toBe(imageData);
    });

    it('should remove items correctly', async () => {
      await memoryManager.store('test-1', { data: 'test' });
      expect(memoryManager.has('test-1')).toBe(true);
      
      const removed = memoryManager.remove('test-1');
      expect(removed).toBe(true);
      expect(memoryManager.has('test-1')).toBe(false);
      
      const notRemoved = memoryManager.remove('non-existent');
      expect(notRemoved).toBe(false);
    });

    it('should clear all items', async () => {
      await memoryManager.store('test-1', { data: 'test1' });
      await memoryManager.store('test-2', { data: 'test2' });
      
      expect(memoryManager.getSize()).toBe(2);
      
      memoryManager.clear();
      
      expect(memoryManager.getSize()).toBe(0);
      expect(memoryManager.getMemoryUsage()).toBe(0);
    });

    it('should update access information', async () => {
      await memoryManager.store('test-1', { data: 'test' });
      
      // Access the item multiple times
      await memoryManager.get('test-1');
      await memoryManager.get('test-1');
      
      // Item should still be accessible
      expect(await memoryManager.get('test-1')).toEqual({ data: 'test' });
    });
  });

  describe('Memory Management', () => {
    it('should track memory usage', async () => {
      const largeData = new Array(1000).fill('x').join('');
      
      await memoryManager.store('large-1', largeData);
      const metrics1 = memoryManager.getMetrics();
      expect(metrics1.totalMemoryUsed).toBeGreaterThan(0);
      
      await memoryManager.store('large-2', largeData);
      const metrics2 = memoryManager.getMetrics();
      expect(metrics2.totalMemoryUsed).toBeGreaterThan(metrics1.totalMemoryUsed);
    });

    it('should enforce memory limits', async () => {
      const largeData = new Array(5000).fill('x').join(''); // Large data to exceed limit
      
      // Fill up memory
      for (let i = 0; i < 10; i++) {
        await memoryManager.store(`large-${i}`, largeData);
      }
      
      // Should not exceed memory limit significantly
      const metrics = memoryManager.getMetrics();
      const maxMemory = 10 * 1024 * 1024; // 10MB limit
      expect(metrics.totalMemoryUsed).toBeLessThan(maxMemory * 1.5); // Allow some overhead
    });

    it('should perform LRU eviction', async () => {
      // Fill cache to capacity
      for (let i = 0; i < 20; i++) {
        await memoryManager.store(`item-${i}`, { data: `test-${i}` });
      }
      
      // Access some items to make them recently used
      await memoryManager.get('item-15');
      await memoryManager.get('item-16');
      await memoryManager.get('item-17');
      
      // Add more items to trigger eviction
      for (let i = 20; i < 25; i++) {
        await memoryManager.store(`item-${i}`, { data: `test-${i}` });
      }
      
      // Recently accessed items should still be present
      expect(memoryManager.has('item-15')).toBe(true);
      expect(memoryManager.has('item-16')).toBe(true);
      expect(memoryManager.has('item-17')).toBe(true);
      
      // Some older items should be evicted
      expect(memoryManager.getSize()).toBeLessThan(25);
    });
  });

  describe('Compression', () => {
    it('should compress large items', async () => {
      const largeData = new Array(2000).fill('test data').join(' ');
      
      await memoryManager.store('large-item', largeData, { compress: true });
      
      const retrieved = await memoryManager.get('large-item');
      expect(retrieved).toBe(largeData);
    });

    it('should handle compression errors gracefully', async () => {
      // Mock compression worker to fail
      const originalWorker = global.Worker;
      global.Worker = class {
        constructor() {
          throw new Error('Worker creation failed');
        }
      } as any;
      
      const manager = new MemoryManager({ compressionEnabled: true });
      
      // Should still work without compression
      const stored = await manager.store('test', { data: 'test' });
      expect(stored).toBe(true);
      
      const retrieved = await manager.get('test');
      expect(retrieved).toEqual({ data: 'test' });
      
      global.Worker = originalWorker;
      manager.destroy();
    });
  });

  describe('Memory Pressure Detection', () => {
    it('should detect memory pressure', (done) => {
      const manager = new MemoryManager({
        maxMemoryMB: 1, // Very small limit
      });
      
      manager.addEventListener('memory-pressure', (event) => {
        expect(event.type).toBe('memory-pressure');
        expect(event.level).toMatch(/medium|high|critical/);
        expect(event.suggestedActions).toBeInstanceOf(Array);
        manager.destroy();
        done();
      });
      
      // Fill memory to trigger pressure
      const largeData = new Array(1000).fill('x').join('');
      Promise.all([
        manager.store('item-1', largeData),
        manager.store('item-2', largeData),
        manager.store('item-3', largeData),
      ]);
    });

    it('should provide appropriate suggestions for different pressure levels', () => {
      // This tests the internal logic for suggestion generation
      // In a real implementation, we'd test the actual suggestions
      expect(memoryManager).toBeDefined();
    });
  });

  describe('Cleanup and Garbage Collection', () => {
    it('should perform automatic cleanup', (done) => {
      const manager = new MemoryManager({
        cleanupInterval: 50, // Very fast cleanup
        maxMemoryMB: 1,
      });
      
      // Add items that will be cleaned up
      Promise.all([
        manager.store('temp-1', { data: 'temp1' }),
        manager.store('temp-2', { data: 'temp2' }),
        manager.store('temp-3', { data: 'temp3' }),
      ]).then(() => {
        const initialSize = manager.getSize();
        
        // Wait for cleanup to occur
        setTimeout(() => {
          const finalSize = manager.getSize();
          // Some cleanup should have occurred
          expect(finalSize).toBeLessThanOrEqual(initialSize);
          manager.destroy();
          done();
        }, 200);
      });
    });

    it('should force cleanup when needed', async () => {
      // Fill memory
      for (let i = 0; i < 10; i++) {
        await memoryManager.store(`item-${i}`, { data: `test-${i}` });
      }
      
      const initialSize = memoryManager.getSize();
      const initialMetrics = memoryManager.getMetrics();
      
      // Trigger memory pressure to force cleanup
      const largeData = new Array(5000).fill('x').join('');
      await memoryManager.store('large-item', largeData);
      
      const finalSize = memoryManager.getSize();
      const finalMetrics = memoryManager.getMetrics();
      
      // Should have performed cleanup
      expect(finalMetrics.gcCount).toBeGreaterThan(initialMetrics.gcCount);
    });
  });

  describe('Event System', () => {
    it('should add and remove event listeners', () => {
      const listener = vi.fn();
      
      memoryManager.addEventListener('memory-pressure', listener);
      memoryManager.removeEventListener('memory-pressure', listener);
      
      // Should not crash
      expect(memoryManager).toBeDefined();
    });

    it('should handle listener errors gracefully', () => {
      const errorListener = () => {
        throw new Error('Listener error');
      };
      
      memoryManager.addEventListener('memory-pressure', errorListener);
      
      // Should not crash when emitting events
      expect(memoryManager).toBeDefined();
    });
  });

  describe('Configuration Updates', () => {
    it('should update configuration', () => {
      const newConfig = {
        maxMemoryMB: 256,
        compressionEnabled: false,
      };
      
      memoryManager.updateConfig(newConfig);
      
      // Configuration should be updated (internal state)
      expect(memoryManager).toBeDefined();
    });
  });

  describe('Metrics and Statistics', () => {
    it('should calculate compression ratio correctly', async () => {
      // Add some compressed and uncompressed items
      await memoryManager.store('small', 'small data');
      await memoryManager.store('large', new Array(1000).fill('x').join(''), { compress: true });
      
      const metrics = memoryManager.getMetrics();
      expect(metrics.compressionRatio).toBeGreaterThanOrEqual(0);
      expect(metrics.compressionRatio).toBeLessThanOrEqual(1);
    });

    it('should track average item size', async () => {
      await memoryManager.store('item-1', 'small');
      await memoryManager.store('item-2', new Array(100).fill('x').join(''));
      
      const metrics = memoryManager.getMetrics();
      expect(metrics.averageItemSize).toBeGreaterThan(0);
      expect(metrics.itemCount).toBe(2);
    });
  });

  describe('Cleanup and Destruction', () => {
    it('should cleanup all resources on destroy', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      memoryManager.destroy();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      expect(memoryManager.getSize()).toBe(0);
      expect(memoryManager.getMemoryUsage()).toBe(0);
    });

    it('should terminate compression worker on destroy', () => {
      const manager = new MemoryManager({ compressionEnabled: true });
      
      // Worker should be created
      expect(manager).toBeDefined();
      
      manager.destroy();
      
      // Should not crash
      expect(manager.getSize()).toBe(0);
    });
  });
});
