"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Eye, 
  Ear, 
  Keyboard, 
  Volume2, 
  VolumeX, 
  Play, 
  Pause, 
  SkipForward, 
  SkipBack,
  Settings,
  Accessibility,
  Type,
  Contrast,
  MousePointer,
  Zap,
  Info,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AccessibilityManager, type AccessibilityConfig, type StructuralElement } from '@/lib/accessibility/screen-reader';

interface AccessibilityPanelProps {
  accessibilityManager: AccessibilityManager;
  onConfigChange?: (config: AccessibilityConfig) => void;
  className?: string;
}

export default function AccessibilityPanel({
  accessibilityManager,
  onConfigChange,
  className,
}: AccessibilityPanelProps) {
  const [config, setConfig] = useState<AccessibilityConfig>(accessibilityManager.getConfig());
  const [currentElement, setCurrentElement] = useState<StructuralElement | null>(null);
  const [structuralElements, setStructuralElements] = useState<StructuralElement[]>([]);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(false);

  // Check speech synthesis support
  useEffect(() => {
    setSpeechSupported('speechSynthesis' in window);
  }, []);

  // Update config when accessibility manager changes
  useEffect(() => {
    const updateConfig = () => {
      const newConfig = accessibilityManager.getConfig();
      setConfig(newConfig);
      onConfigChange?.(newConfig);
    };

    updateConfig();
  }, [accessibilityManager, onConfigChange]);

  // Monitor current element and structural elements
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentElement(accessibilityManager.getCurrentElement());
      setStructuralElements(accessibilityManager.getStructuralElements());
    }, 1000);

    return () => clearInterval(interval);
  }, [accessibilityManager]);

  // Monitor speech synthesis state
  useEffect(() => {
    if (!speechSupported) return;

    const checkSpeechState = () => {
      setIsSpeaking(window.speechSynthesis.speaking);
    };

    const interval = setInterval(checkSpeechState, 500);
    return () => clearInterval(interval);
  }, [speechSupported]);

  const updateConfig = useCallback((updates: Partial<AccessibilityConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    accessibilityManager.updateConfig(updates);
    onConfigChange?.(newConfig);
  }, [config, accessibilityManager, onConfigChange]);

  const handleSpeechToggle = useCallback(() => {
    if (isSpeaking) {
      window.speechSynthesis.cancel();
    } else if (currentElement) {
      accessibilityManager.announce(currentElement.text, 'assertive', true);
    }
  }, [isSpeaking, currentElement, accessibilityManager]);

  const getElementTypeIcon = (type: StructuralElement['type']) => {
    switch (type) {
      case 'heading': return <Type className="h-4 w-4" />;
      case 'paragraph': return <Type className="h-4 w-4" />;
      case 'list': return <Type className="h-4 w-4" />;
      case 'table': return <Type className="h-4 w-4" />;
      case 'image': return <Eye className="h-4 w-4" />;
      case 'link': return <MousePointer className="h-4 w-4" />;
      case 'form': return <Settings className="h-4 w-4" />;
      default: return <Type className="h-4 w-4" />;
    }
  };

  const getElementTypeBadgeColor = (type: StructuralElement['type']) => {
    switch (type) {
      case 'heading': return 'bg-blue-500';
      case 'paragraph': return 'bg-gray-500';
      case 'list': return 'bg-green-500';
      case 'table': return 'bg-purple-500';
      case 'image': return 'bg-orange-500';
      case 'link': return 'bg-cyan-500';
      case 'form': return 'bg-pink-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Accessibility className="h-5 w-5" />
            Accessibility Controls
          </CardTitle>
          <CardDescription>
            Configure accessibility features for screen readers and assistive technologies
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant={config.enableScreenReader ? "default" : "secondary"}>
                Screen Reader {config.enableScreenReader ? "On" : "Off"}
              </Badge>
              <Badge variant={config.enableKeyboardNavigation ? "default" : "secondary"}>
                Keyboard Navigation {config.enableKeyboardNavigation ? "On" : "Off"}
              </Badge>
              {speechSupported && (
                <Badge variant={config.enableTextToSpeech ? "default" : "secondary"}>
                  Text-to-Speech {config.enableTextToSpeech ? "On" : "Off"}
                </Badge>
              )}
            </div>
            
            {currentElement && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Current:</span>
                <Badge className={getElementTypeBadgeColor(currentElement.type)}>
                  {currentElement.type}
                  {currentElement.level && ` ${currentElement.level}`}
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="navigation">Navigation</TabsTrigger>
          <TabsTrigger value="speech">Speech</TabsTrigger>
          <TabsTrigger value="visual">Visual</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          {/* General Accessibility Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">General Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Screen Reader Support</label>
                    <p className="text-xs text-muted-foreground">Enable ARIA announcements</p>
                  </div>
                  <Switch
                    checked={config.enableScreenReader}
                    onCheckedChange={(checked) => updateConfig({ enableScreenReader: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Keyboard Navigation</label>
                    <p className="text-xs text-muted-foreground">Navigate with keyboard shortcuts</p>
                  </div>
                  <Switch
                    checked={config.enableKeyboardNavigation}
                    onCheckedChange={(checked) => updateConfig({ enableKeyboardNavigation: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Structural Navigation</label>
                    <p className="text-xs text-muted-foreground">Navigate by document structure</p>
                  </div>
                  <Switch
                    checked={config.enableStructuralNavigation}
                    onCheckedChange={(checked) => updateConfig({ enableStructuralNavigation: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Reduced Motion</label>
                    <p className="text-xs text-muted-foreground">Minimize animations</p>
                  </div>
                  <Switch
                    checked={config.enableReducedMotion}
                    onCheckedChange={(checked) => updateConfig({ enableReducedMotion: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Announcements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Announcements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Page Changes</label>
                    <p className="text-xs text-muted-foreground">Announce page navigation</p>
                  </div>
                  <Switch
                    checked={config.announcePageChanges}
                    onCheckedChange={(checked) => updateConfig({ announcePageChanges: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Zoom Changes</label>
                    <p className="text-xs text-muted-foreground">Announce zoom level</p>
                  </div>
                  <Switch
                    checked={config.announceZoomChanges}
                    onCheckedChange={(checked) => updateConfig({ announceZoomChanges: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Loading States</label>
                    <p className="text-xs text-muted-foreground">Announce loading progress</p>
                  </div>
                  <Switch
                    checked={config.announceLoadingStates}
                    onCheckedChange={(checked) => updateConfig({ announceLoadingStates: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="navigation" className="space-y-6">
          {/* Keyboard Shortcuts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Keyboard className="h-5 w-5" />
                Keyboard Shortcuts
              </CardTitle>
              <CardDescription>
                Navigate the document using keyboard shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Next Element</span>
                    <Badge variant="outline">→ or ↓</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Previous Element</span>
                    <Badge variant="outline">← or ↑</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>First Element</span>
                    <Badge variant="outline">Home</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Element</span>
                    <Badge variant="outline">End</Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Next Heading</span>
                    <Badge variant="outline">H</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Previous Heading</span>
                    <Badge variant="outline">Shift + H</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Next Paragraph</span>
                    <Badge variant="outline">P</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Toggle Speech</span>
                    <Badge variant="outline">Space</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Element */}
          {currentElement && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getElementTypeIcon(currentElement.type)}
                  Current Element
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge className={getElementTypeBadgeColor(currentElement.type)}>
                      {currentElement.type}
                      {currentElement.level && ` Level ${currentElement.level}`}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Page {currentElement.pageNumber}
                    </span>
                  </div>
                  
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">{currentElement.text}</p>
                  </div>

                  {speechSupported && config.enableTextToSpeech && (
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleSpeechToggle}
                      >
                        {isSpeaking ? (
                          <>
                            <Pause className="h-4 w-4 mr-2" />
                            Stop
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Read Aloud
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Document Structure */}
          <Card>
            <CardHeader>
              <CardTitle>Document Structure</CardTitle>
              <CardDescription>
                {structuralElements.length} structural elements found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {structuralElements.slice(0, 20).map((element, index) => (
                  <div
                    key={element.id}
                    className={cn(
                      "flex items-center gap-3 p-2 rounded-lg text-sm",
                      currentElement?.id === element.id ? "bg-primary/10 border border-primary/20" : "bg-muted/50"
                    )}
                  >
                    {getElementTypeIcon(element.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {element.type}
                          {element.level && ` ${element.level}`}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          Page {element.pageNumber}
                        </span>
                      </div>
                      <p className="truncate text-xs mt-1">
                        {element.text}
                      </p>
                    </div>
                  </div>
                ))}
                
                {structuralElements.length > 20 && (
                  <div className="text-center text-sm text-muted-foreground py-2">
                    ... and {structuralElements.length - 20} more elements
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="speech" className="space-y-6">
          {/* Text-to-Speech Settings */}
          {speechSupported ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Text-to-Speech
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <label className="text-sm font-medium">Enable Text-to-Speech</label>
                      <p className="text-xs text-muted-foreground">Read content aloud</p>
                    </div>
                    <Switch
                      checked={config.enableTextToSpeech}
                      onCheckedChange={(checked) => updateConfig({ enableTextToSpeech: checked })}
                    />
                  </div>

                  {config.enableTextToSpeech && (
                    <div className="space-y-6">
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Speech Rate: {config.speechRate}</label>
                          <Slider
                            value={[config.speechRate]}
                            onValueChange={([value]) => updateConfig({ speechRate: value })}
                            min={0.1}
                            max={3}
                            step={0.1}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>Slow</span>
                            <span>Fast</span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium">Speech Pitch: {config.speechPitch}</label>
                          <Slider
                            value={[config.speechPitch]}
                            onValueChange={([value]) => updateConfig({ speechPitch: value })}
                            min={0}
                            max={2}
                            step={0.1}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>Low</span>
                            <span>High</span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium">Speech Volume: {Math.round(config.speechVolume * 100)}%</label>
                          <Slider
                            value={[config.speechVolume]}
                            onValueChange={([value]) => updateConfig({ speechVolume: value })}
                            min={0}
                            max={1}
                            step={0.1}
                            className="mt-2"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>Quiet</span>
                            <span>Loud</span>
                          </div>
                        </div>
                      </div>

                      {/* Speech Controls */}
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleSpeechToggle}
                          disabled={!currentElement}
                        >
                          {isSpeaking ? (
                            <>
                              <Pause className="h-4 w-4 mr-2" />
                              Pause
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Play
                            </>
                          )}
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.speechSynthesis.cancel()}
                          disabled={!isSpeaking}
                        >
                          <VolumeX className="h-4 w-4 mr-2" />
                          Stop
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Text-to-speech is not supported in this browser. Please use a modern browser with speech synthesis support.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="visual" className="space-y-6">
          {/* Visual Accessibility Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Visual Accessibility
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">High Contrast</label>
                    <p className="text-xs text-muted-foreground">Increase visual contrast</p>
                  </div>
                  <Switch
                    checked={config.enableHighContrast}
                    onCheckedChange={(checked) => updateConfig({ enableHighContrast: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Large Text</label>
                    <p className="text-xs text-muted-foreground">Increase text size</p>
                  </div>
                  <Switch
                    checked={config.enableLargeText}
                    onCheckedChange={(checked) => updateConfig({ enableLargeText: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>System Preferences</CardTitle>
              <CardDescription>
                Detected accessibility preferences from your system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Prefers Reduced Motion</span>
                  <Badge variant={window.matchMedia('(prefers-reduced-motion: reduce)').matches ? "default" : "secondary"}>
                    {window.matchMedia('(prefers-reduced-motion: reduce)').matches ? "Yes" : "No"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Prefers High Contrast</span>
                  <Badge variant={window.matchMedia('(prefers-contrast: high)').matches ? "default" : "secondary"}>
                    {window.matchMedia('(prefers-contrast: high)').matches ? "Yes" : "No"}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Speech Synthesis Support</span>
                  <Badge variant={speechSupported ? "default" : "secondary"}>
                    {speechSupported ? "Supported" : "Not Supported"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
