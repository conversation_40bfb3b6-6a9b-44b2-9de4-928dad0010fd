import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AccessibilityManager, type StructuralElement } from '@/lib/accessibility/screen-reader';

// Mock DOM APIs
Object.defineProperty(window, 'speechSynthesis', {
  value: {
    speak: vi.fn(),
    cancel: vi.fn(),
    speaking: false,
  },
  writable: true,
});

Object.defineProperty(window, 'SpeechSynthesisUtterance', {
  value: vi.fn().mockImplementation((text) => ({
    text,
    rate: 1,
    pitch: 1,
    volume: 1,
    onend: null,
  })),
  writable: true,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
  writable: true,
});

// Mock MutationObserver
global.MutationObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn(),
}));

describe('AccessibilityManager', () => {
  let accessibilityManager: AccessibilityManager;
  let mockLiveRegion: HTMLElement;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock document.body.appendChild
    mockLiveRegion = document.createElement('div');
    vi.spyOn(document.body, 'appendChild').mockImplementation((element) => {
      if (element.getAttribute('aria-live')) {
        Object.assign(mockLiveRegion, element);
        return element;
      }
      return element;
    });

    accessibilityManager = new AccessibilityManager();
  });

  afterEach(() => {
    accessibilityManager.destroy();
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      const config = accessibilityManager.getConfig();
      expect(config.enableScreenReader).toBe(true);
      expect(config.enableKeyboardNavigation).toBe(true);
      expect(config.speechRate).toBe(1);
      expect(config.speechPitch).toBe(1);
      expect(config.speechVolume).toBe(0.8);
    });

    it('should initialize with custom config', () => {
      const customManager = new AccessibilityManager({
        enableScreenReader: false,
        speechRate: 1.5,
        enableTextToSpeech: true,
      });
      
      const config = customManager.getConfig();
      expect(config.enableScreenReader).toBe(false);
      expect(config.speechRate).toBe(1.5);
      expect(config.enableTextToSpeech).toBe(true);
      
      customManager.destroy();
    });

    it('should create ARIA live region', () => {
      expect(document.body.appendChild).toHaveBeenCalled();
    });
  });

  describe('Announcements', () => {
    it('should announce messages to screen readers', () => {
      const message = 'Test announcement';
      accessibilityManager.announce(message, 'polite');
      
      // Should update live region
      expect(mockLiveRegion.textContent).toBe(message);
    });

    it('should handle different announcement priorities', () => {
      accessibilityManager.announce('Polite message', 'polite');
      expect(mockLiveRegion.getAttribute('aria-live')).toBe('polite');
      
      accessibilityManager.announce('Assertive message', 'assertive');
      expect(mockLiveRegion.getAttribute('aria-live')).toBe('assertive');
    });

    it('should use speech synthesis when enabled', () => {
      accessibilityManager.updateConfig({ enableTextToSpeech: true });
      accessibilityManager.announce('Test speech', 'polite');
      
      expect(window.speechSynthesis.speak).toHaveBeenCalled();
    });

    it('should interrupt speech when requested', () => {
      accessibilityManager.updateConfig({ enableTextToSpeech: true });
      accessibilityManager.announce('Test speech', 'polite', true);
      
      expect(window.speechSynthesis.cancel).toHaveBeenCalled();
    });
  });

  describe('Structural Navigation', () => {
    const mockElements: StructuralElement[] = [
      {
        id: 'heading-1',
        type: 'heading',
        level: 1,
        text: 'Main Heading',
        bounds: { x: 0, y: 0, width: 100, height: 20 },
        pageNumber: 1,
      },
      {
        id: 'paragraph-1',
        type: 'paragraph',
        text: 'First paragraph content',
        bounds: { x: 0, y: 25, width: 100, height: 15 },
        pageNumber: 1,
      },
      {
        id: 'heading-2',
        type: 'heading',
        level: 2,
        text: 'Sub Heading',
        bounds: { x: 0, y: 45, width: 100, height: 18 },
        pageNumber: 1,
      },
    ];

    beforeEach(() => {
      accessibilityManager.updateStructuralElements(mockElements);
    });

    it('should update structural elements', () => {
      const elements = accessibilityManager.getStructuralElements();
      expect(elements).toHaveLength(3);
      expect(elements[0].type).toBe('heading');
      expect(elements[1].type).toBe('paragraph');
    });

    it('should navigate to next element', () => {
      // Mock DOM elements
      mockElements.forEach(element => {
        const domElement = document.createElement('div');
        domElement.setAttribute('data-element-id', element.id);
        domElement.focus = vi.fn();
        domElement.scrollIntoView = vi.fn();
        vi.spyOn(document, 'querySelector').mockImplementation((selector) => {
          if (selector.includes(element.id)) {
            return domElement;
          }
          return null;
        });
      });

      // Simulate keyboard navigation
      const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      document.dispatchEvent(keyEvent);

      // Should have current element
      const currentElement = accessibilityManager.getCurrentElement();
      expect(currentElement).toBeTruthy();
    });

    it('should navigate by element type', () => {
      // Test navigation to specific element types
      const elements = accessibilityManager.getStructuralElements();
      const headings = elements.filter(el => el.type === 'heading');
      expect(headings).toHaveLength(2);
    });
  });

  describe('Keyboard Navigation', () => {
    it('should handle keyboard events', () => {
      const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      
      // Mock PDF viewer container
      const pdfViewer = document.createElement('div');
      pdfViewer.setAttribute('data-pdf-viewer', 'true');
      document.body.appendChild(pdfViewer);
      
      // Set focus to PDF viewer
      Object.defineProperty(keyEvent, 'target', {
        value: pdfViewer,
        writable: false,
      });

      // Should handle the event
      document.dispatchEvent(keyEvent);
      
      // Cleanup
      document.body.removeChild(pdfViewer);
    });

    it('should ignore keyboard events outside PDF viewer', () => {
      const keyEvent = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      
      // Mock non-PDF element
      const otherElement = document.createElement('div');
      document.body.appendChild(otherElement);
      
      Object.defineProperty(keyEvent, 'target', {
        value: otherElement,
        writable: false,
      });

      // Should not handle the event
      document.dispatchEvent(keyEvent);
      
      // Cleanup
      document.body.removeChild(otherElement);
    });
  });

  describe('Speech Synthesis', () => {
    beforeEach(() => {
      accessibilityManager.updateConfig({ enableTextToSpeech: true });
    });

    it('should configure speech synthesis parameters', () => {
      accessibilityManager.updateConfig({
        speechRate: 1.5,
        speechPitch: 1.2,
        speechVolume: 0.9,
      });

      accessibilityManager.announce('Test speech', 'polite');
      
      const utteranceCall = (window.SpeechSynthesisUtterance as any).mock.calls[0];
      expect(utteranceCall[0]).toBe('Test speech');
    });

    it('should handle speech synthesis errors gracefully', () => {
      // Mock speech synthesis to throw error
      window.speechSynthesis.speak = vi.fn().mockImplementation(() => {
        throw new Error('Speech synthesis error');
      });

      // Should not crash
      expect(() => {
        accessibilityManager.announce('Test speech', 'polite');
      }).not.toThrow();
    });
  });

  describe('User Preference Detection', () => {
    it('should detect reduced motion preference', () => {
      // Mock reduced motion preference
      (window.matchMedia as any).mockImplementation((query: string) => ({
        matches: query.includes('prefers-reduced-motion'),
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      const manager = new AccessibilityManager();
      const config = manager.getConfig();
      expect(config.enableReducedMotion).toBe(true);
      
      manager.destroy();
    });

    it('should detect high contrast preference', () => {
      // Mock high contrast preference
      (window.matchMedia as any).mockImplementation((query: string) => ({
        matches: query.includes('prefers-contrast'),
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      const manager = new AccessibilityManager();
      const config = manager.getConfig();
      expect(config.enableHighContrast).toBe(true);
      
      manager.destroy();
    });
  });

  describe('Page and Zoom Announcements', () => {
    it('should announce page changes', () => {
      accessibilityManager.announcePageChange(2, 10);
      expect(mockLiveRegion.textContent).toBe('Page 2 of 10');
    });

    it('should announce zoom changes', () => {
      accessibilityManager.announceZoomChange(1.5);
      expect(mockLiveRegion.textContent).toBe('Zoom level 150%');
    });

    it('should announce loading states', () => {
      accessibilityManager.announceLoadingState('loading');
      expect(mockLiveRegion.textContent).toBe('Loading document...');
      
      accessibilityManager.announceLoadingState('loaded');
      expect(mockLiveRegion.textContent).toBe('Document loaded successfully');
      
      accessibilityManager.announceLoadingState('error', 'Network error');
      expect(mockLiveRegion.textContent).toBe('Error loading document: Network error');
    });

    it('should respect announcement settings', () => {
      accessibilityManager.updateConfig({ announcePageChanges: false });
      accessibilityManager.announcePageChange(2, 10);
      expect(mockLiveRegion.textContent).not.toBe('Page 2 of 10');
    });
  });

  describe('Configuration Updates', () => {
    it('should update configuration', () => {
      const newConfig = {
        enableScreenReader: false,
        speechRate: 2.0,
        enableTextToSpeech: true,
      };

      accessibilityManager.updateConfig(newConfig);
      const config = accessibilityManager.getConfig();
      
      expect(config.enableScreenReader).toBe(false);
      expect(config.speechRate).toBe(2.0);
      expect(config.enableTextToSpeech).toBe(true);
    });

    it('should preserve existing config when updating', () => {
      const originalConfig = accessibilityManager.getConfig();
      
      accessibilityManager.updateConfig({ speechRate: 1.5 });
      const updatedConfig = accessibilityManager.getConfig();
      
      expect(updatedConfig.speechRate).toBe(1.5);
      expect(updatedConfig.enableScreenReader).toBe(originalConfig.enableScreenReader);
    });
  });

  describe('ARIA Support', () => {
    it('should generate appropriate ARIA roles', () => {
      const elements: StructuralElement[] = [
        {
          id: 'test-heading',
          type: 'heading',
          level: 1,
          text: 'Test Heading',
          bounds: { x: 0, y: 0, width: 100, height: 20 },
          pageNumber: 1,
        },
      ];

      // Mock DOM element
      const domElement = document.createElement('div');
      domElement.setAttribute('data-element-id', 'test-heading');
      vi.spyOn(document, 'querySelector').mockReturnValue(domElement);

      accessibilityManager.updateStructuralElements(elements);

      expect(domElement.getAttribute('role')).toBe('heading');
      expect(domElement.getAttribute('aria-level')).toBe('1');
      expect(domElement.getAttribute('tabindex')).toBe('0');
    });

    it('should generate appropriate ARIA labels', () => {
      const element: StructuralElement = {
        id: 'test-paragraph',
        type: 'paragraph',
        text: 'This is a test paragraph with some content',
        bounds: { x: 0, y: 0, width: 100, height: 15 },
        pageNumber: 1,
      };

      const domElement = document.createElement('div');
      domElement.setAttribute('data-element-id', 'test-paragraph');
      vi.spyOn(document, 'querySelector').mockReturnValue(domElement);

      accessibilityManager.updateStructuralElements([element]);

      const ariaLabel = domElement.getAttribute('aria-label');
      expect(ariaLabel).toContain('paragraph');
      expect(ariaLabel).toContain('This is a test paragraph');
    });
  });

  describe('Cleanup and Destruction', () => {
    it('should cleanup resources on destroy', () => {
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');
      
      accessibilityManager.destroy();
      
      expect(removeEventListenerSpy).toHaveBeenCalled();
      expect(window.speechSynthesis.cancel).toHaveBeenCalled();
    });

    it('should remove live region on destroy', () => {
      const removeChildSpy = vi.spyOn(document.body, 'removeChild');
      
      accessibilityManager.destroy();
      
      // Should attempt to remove live region
      expect(removeChildSpy).toHaveBeenCalled();
    });

    it('should clear structural elements on destroy', () => {
      accessibilityManager.updateStructuralElements([
        {
          id: 'test',
          type: 'paragraph',
          text: 'Test',
          bounds: { x: 0, y: 0, width: 100, height: 15 },
          pageNumber: 1,
        },
      ]);

      expect(accessibilityManager.getStructuralElements()).toHaveLength(1);
      
      accessibilityManager.destroy();
      
      expect(accessibilityManager.getStructuralElements()).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing DOM elements gracefully', () => {
      vi.spyOn(document, 'querySelector').mockReturnValue(null);
      
      const elements: StructuralElement[] = [
        {
          id: 'missing-element',
          type: 'heading',
          text: 'Missing Element',
          bounds: { x: 0, y: 0, width: 100, height: 20 },
          pageNumber: 1,
        },
      ];

      // Should not crash
      expect(() => {
        accessibilityManager.updateStructuralElements(elements);
      }).not.toThrow();
    });

    it('should handle speech synthesis unavailability', () => {
      // Remove speech synthesis
      delete (window as any).speechSynthesis;
      
      const manager = new AccessibilityManager({ enableTextToSpeech: true });
      
      // Should not crash
      expect(() => {
        manager.announce('Test message', 'polite');
      }).not.toThrow();
      
      manager.destroy();
    });
  });
});
