"use client";

import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ChevronRight,
  ChevronDown,
  FileText,
  Loader2,
  AlertCircle,
} from "lucide-react";
import type { OutlineItem } from "@/lib/types/pdf";

interface PDFOutlineProps {
  outline: OutlineItem[];
  currentPage: number;
  onPageSelect: (page: number) => void;
  isLoading?: boolean;
}

export default function PDFOutline({
  outline,
  isLoading = false,
}: Omit<PDFOutlineProps, 'currentPage' | 'onPageSelect'>) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: OutlineItem) => {
    // For now, outline navigation is disabled since we need to resolve destinations
    // TODO: Implement destination resolution to page numbers
    console.log("Outline item clicked:", item.title, item.dest);
  };

  const renderOutlineItem = (item: OutlineItem, index: number, level = 0) => {
    const itemId = `${level}-${index}`;
    const hasChildren = item.items && item.items.length > 0;
    const isExpanded = expandedItems.has(itemId);
    const isCurrentPage = false; // TODO: Implement page detection from dest

    const textStyle = {
      fontWeight: item.bold ? "bold" : "normal",
      fontStyle: item.italic ? "italic" : "normal",
      color: item.color ? `rgb(${item.color.join(",")})` : undefined,
    };

    return (
      <div key={itemId} className="select-none">
        <div
          className={`flex items-center gap-2 py-2 px-3 rounded-md cursor-pointer hover:bg-muted/50 transition-colors ${
            isCurrentPage
              ? "bg-primary/10 text-primary border border-primary/20"
              : ""
          }`}
          style={{ paddingLeft: `${level * 16 + 12}px` }}
          onClick={() => handleItemClick(item)}
        >
          {hasChildren ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-muted"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(itemId);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          ) : (
            <div className="w-4 flex justify-center">
              <div className="w-1 h-1 bg-muted-foreground/40 rounded-full" />
            </div>
          )}

          <FileText className="h-3 w-3 flex-shrink-0 text-muted-foreground" />

          <div className="flex-1 min-w-0">
            <span
              className="text-sm truncate block"
              style={textStyle}
              title={item.title}
            >
              {item.title}
            </span>
          </div>


        </div>

        {hasChildren && isExpanded && (
          <div className="ml-2">
            {item.items!.map((childItem, childIndex) =>
              renderOutlineItem(childItem, childIndex, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        <span className="ml-2 text-sm text-muted-foreground">
          Loading outline...
        </span>
      </div>
    );
  }

  if (!outline || outline.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="font-medium text-lg mb-2">No Outline Available</h3>
        <p className="text-sm text-muted-foreground mb-4">
          This PDF doesn&apos;t contain a table of contents or outline structure.
        </p>
        <div className="text-xs text-muted-foreground bg-muted/50 rounded-lg p-3 max-w-sm">
          <p className="font-medium mb-1">What you can do:</p>
          <p>• Use the search function to find content</p>
          <p>• Create bookmarks for important pages</p>
          <p>• Navigate using page numbers</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col" data-testid="pdf-outline">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-sm">Document Outline</h3>
          <Badge variant="secondary" className="text-xs">
            {outline.length} {outline.length === 1 ? "section" : "sections"}
          </Badge>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {outline.map((item, index) => renderOutlineItem(item, index))}
        </div>
      </ScrollArea>
    </div>
  );
}
