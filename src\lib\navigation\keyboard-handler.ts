/**
 * Keyboard Navigation Handler
 * Comprehensive keyboard shortcuts and navigation for PDF viewer
 */

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  description: string;
  category: 'navigation' | 'zoom' | 'view' | 'accessibility' | 'general';
  action: (event: KeyboardEvent) => void | Promise<void>;
  preventDefault?: boolean;
  enabled?: boolean;
}

export interface KeyboardConfig {
  enableGlobalShortcuts: boolean;
  enableAccessibilityShortcuts: boolean;
  enableNavigationShortcuts: boolean;
  enableZoomShortcuts: boolean;
  enableViewShortcuts: boolean;
  customShortcuts: KeyboardShortcut[];
  preventDefaultBehavior: boolean;
}

export interface NavigationCallbacks {
  onNextPage: () => void;
  onPreviousPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomReset: () => void;
  onZoomFit: () => void;
  onZoomFitWidth: () => void;
  onRotateClockwise: () => void;
  onRotateCounterClockwise: () => void;
  onToggleFullscreen: () => void;
  onToggleSidebar: () => void;
  onToggleToolbar: () => void;
  onSearch: () => void;
  onPrint: () => void;
  onDownload: () => void;
  onGoToPage: (page: number) => void;
  onFocusSearch: () => void;
  onEscape: () => void;
}

const DEFAULT_CONFIG: KeyboardConfig = {
  enableGlobalShortcuts: true,
  enableAccessibilityShortcuts: true,
  enableNavigationShortcuts: true,
  enableZoomShortcuts: true,
  enableViewShortcuts: true,
  customShortcuts: [],
  preventDefaultBehavior: true,
};

export class KeyboardNavigationHandler {
  private config: KeyboardConfig;
  private callbacks: Partial<NavigationCallbacks>;
  private shortcuts: Map<string, KeyboardShortcut> = new Map();
  private isEnabled = true;
  private focusedElement: HTMLElement | null = null;
  private eventListeners: Map<string, EventListener> = new Map();

  constructor(
    callbacks: Partial<NavigationCallbacks> = {},
    config: Partial<KeyboardConfig> = {}
  ) {
    this.callbacks = callbacks;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupDefaultShortcuts();
    this.setupEventListeners();
  }

  private setupDefaultShortcuts(): void {
    const shortcuts: KeyboardShortcut[] = [
      // Navigation shortcuts
      {
        key: 'ArrowRight',
        description: 'Next page',
        category: 'navigation',
        action: () => this.callbacks.onNextPage?.(),
      },
      {
        key: 'ArrowLeft',
        description: 'Previous page',
        category: 'navigation',
        action: () => this.callbacks.onPreviousPage?.(),
      },
      {
        key: 'ArrowDown',
        description: 'Next page',
        category: 'navigation',
        action: () => this.callbacks.onNextPage?.(),
      },
      {
        key: 'ArrowUp',
        description: 'Previous page',
        category: 'navigation',
        action: () => this.callbacks.onPreviousPage?.(),
      },
      {
        key: 'Home',
        description: 'First page',
        category: 'navigation',
        action: () => this.callbacks.onFirstPage?.(),
      },
      {
        key: 'End',
        description: 'Last page',
        category: 'navigation',
        action: () => this.callbacks.onLastPage?.(),
      },
      {
        key: 'PageDown',
        description: 'Next page',
        category: 'navigation',
        action: () => this.callbacks.onNextPage?.(),
      },
      {
        key: 'PageUp',
        description: 'Previous page',
        category: 'navigation',
        action: () => this.callbacks.onPreviousPage?.(),
      },
      {
        key: ' ',
        description: 'Next page',
        category: 'navigation',
        action: () => this.callbacks.onNextPage?.(),
      },
      {
        key: ' ',
        shiftKey: true,
        description: 'Previous page',
        category: 'navigation',
        action: () => this.callbacks.onPreviousPage?.(),
      },

      // Zoom shortcuts
      {
        key: '=',
        ctrlKey: true,
        description: 'Zoom in',
        category: 'zoom',
        action: () => this.callbacks.onZoomIn?.(),
      },
      {
        key: '+',
        ctrlKey: true,
        description: 'Zoom in',
        category: 'zoom',
        action: () => this.callbacks.onZoomIn?.(),
      },
      {
        key: '-',
        ctrlKey: true,
        description: 'Zoom out',
        category: 'zoom',
        action: () => this.callbacks.onZoomOut?.(),
      },
      {
        key: '0',
        ctrlKey: true,
        description: 'Reset zoom',
        category: 'zoom',
        action: () => this.callbacks.onZoomReset?.(),
      },
      {
        key: '1',
        ctrlKey: true,
        description: 'Fit to page',
        category: 'zoom',
        action: () => this.callbacks.onZoomFit?.(),
      },
      {
        key: '2',
        ctrlKey: true,
        description: 'Fit to width',
        category: 'zoom',
        action: () => this.callbacks.onZoomFitWidth?.(),
      },

      // View shortcuts
      {
        key: 'r',
        ctrlKey: true,
        description: 'Rotate clockwise',
        category: 'view',
        action: () => this.callbacks.onRotateClockwise?.(),
      },
      {
        key: 'r',
        ctrlKey: true,
        shiftKey: true,
        description: 'Rotate counter-clockwise',
        category: 'view',
        action: () => this.callbacks.onRotateCounterClockwise?.(),
      },
      {
        key: 'F11',
        description: 'Toggle fullscreen',
        category: 'view',
        action: () => this.callbacks.onToggleFullscreen?.(),
      },
      {
        key: 'F9',
        description: 'Toggle sidebar',
        category: 'view',
        action: () => this.callbacks.onToggleSidebar?.(),
      },
      {
        key: 'F8',
        description: 'Toggle toolbar',
        category: 'view',
        action: () => this.callbacks.onToggleToolbar?.(),
      },

      // General shortcuts
      {
        key: 'f',
        ctrlKey: true,
        description: 'Search',
        category: 'general',
        action: () => this.callbacks.onSearch?.(),
      },
      {
        key: '/',
        description: 'Focus search',
        category: 'general',
        action: () => this.callbacks.onFocusSearch?.(),
      },
      {
        key: 'p',
        ctrlKey: true,
        description: 'Print',
        category: 'general',
        action: () => this.callbacks.onPrint?.(),
      },
      {
        key: 's',
        ctrlKey: true,
        description: 'Download',
        category: 'general',
        action: () => this.callbacks.onDownload?.(),
      },
      {
        key: 'g',
        ctrlKey: true,
        description: 'Go to page',
        category: 'navigation',
        action: () => this.promptGoToPage(),
      },
      {
        key: 'Escape',
        description: 'Escape/Cancel',
        category: 'general',
        action: () => this.callbacks.onEscape?.(),
      },

      // Number shortcuts for direct page navigation
      ...Array.from({ length: 9 }, (_, i) => ({
        key: (i + 1).toString(),
        ctrlKey: true,
        description: `Go to page ${i + 1}`,
        category: 'navigation' as const,
        action: () => this.callbacks.onGoToPage?.(i + 1),
      })),

      // Accessibility shortcuts
      {
        key: 'h',
        description: 'Next heading (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToNextHeading(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'h',
        shiftKey: true,
        description: 'Previous heading (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToPreviousHeading(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'p',
        description: 'Next paragraph (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToNextParagraph(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'p',
        shiftKey: true,
        description: 'Previous paragraph (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToPreviousParagraph(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'l',
        description: 'Next list (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToNextList(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'l',
        shiftKey: true,
        description: 'Previous list (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToPreviousList(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 't',
        description: 'Next table (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToNextTable(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 't',
        shiftKey: true,
        description: 'Previous table (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToPreviousTable(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'g',
        description: 'Next graphic (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToNextGraphic(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
      {
        key: 'g',
        shiftKey: true,
        description: 'Previous graphic (accessibility)',
        category: 'accessibility',
        action: () => this.navigateToPreviousGraphic(),
        enabled: this.config.enableAccessibilityShortcuts,
      },
    ];

    // Add default shortcuts
    shortcuts.forEach(shortcut => this.addShortcut(shortcut));

    // Add custom shortcuts
    this.config.customShortcuts.forEach(shortcut => this.addShortcut(shortcut));
  }

  private setupEventListeners(): void {
    const keydownHandler = this.handleKeyDown.bind(this);
    const focusHandler = this.handleFocus.bind(this);
    const blurHandler = this.handleBlur.bind(this);

    document.addEventListener('keydown', keydownHandler);
    document.addEventListener('focusin', focusHandler);
    document.addEventListener('focusout', blurHandler);

    this.eventListeners.set('keydown', keydownHandler);
    this.eventListeners.set('focusin', focusHandler);
    this.eventListeners.set('focusout', blurHandler);
  }

  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.isEnabled) return;

    // Check if focus is within PDF viewer or if global shortcuts are enabled
    const target = event.target as HTMLElement;
    const isInPDFViewer = target.closest('[data-pdf-viewer]') !== null;
    const isInputElement = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

    if (!isInPDFViewer && !this.config.enableGlobalShortcuts) return;
    if (isInputElement && !this.shouldHandleInInput(event)) return;

    const shortcutKey = this.getShortcutKey(event);
    const shortcut = this.shortcuts.get(shortcutKey);

    if (shortcut && this.isShortcutEnabled(shortcut)) {
      if (shortcut.preventDefault !== false && this.config.preventDefaultBehavior) {
        event.preventDefault();
        event.stopPropagation();
      }

      try {
        shortcut.action(event);
      } catch (error) {
        console.error('Error executing keyboard shortcut:', error);
      }
    }
  }

  private shouldHandleInInput(event: KeyboardEvent): boolean {
    // Allow certain shortcuts even in input elements
    const allowedKeys = ['Escape', 'F11', 'F9', 'F8'];
    return allowedKeys.includes(event.key) || 
           (event.ctrlKey && ['f', 'p', 's'].includes(event.key));
  }

  private handleFocus(event: FocusEvent): void {
    const target = event.target as HTMLElement;
    if (target.closest('[data-pdf-viewer]')) {
      this.focusedElement = target;
    }
  }

  private handleBlur(event: FocusEvent): void {
    const target = event.target as HTMLElement;
    if (target === this.focusedElement) {
      this.focusedElement = null;
    }
  }

  private getShortcutKey(event: KeyboardEvent): string {
    const modifiers = [];
    if (event.ctrlKey) modifiers.push('ctrl');
    if (event.shiftKey) modifiers.push('shift');
    if (event.altKey) modifiers.push('alt');
    if (event.metaKey) modifiers.push('meta');
    
    return [...modifiers, event.key.toLowerCase()].join('+');
  }

  private isShortcutEnabled(shortcut: KeyboardShortcut): boolean {
    if (shortcut.enabled === false) return false;

    switch (shortcut.category) {
      case 'navigation':
        return this.config.enableNavigationShortcuts;
      case 'zoom':
        return this.config.enableZoomShortcuts;
      case 'view':
        return this.config.enableViewShortcuts;
      case 'accessibility':
        return this.config.enableAccessibilityShortcuts;
      case 'general':
        return true;
      default:
        return true;
    }
  }

  private promptGoToPage(): void {
    const pageInput = prompt('Go to page:');
    if (pageInput) {
      const pageNumber = parseInt(pageInput, 10);
      if (!isNaN(pageNumber) && pageNumber > 0) {
        this.callbacks.onGoToPage?.(pageNumber);
      }
    }
  }

  // Accessibility navigation methods (would integrate with AccessibilityManager)
  private navigateToNextHeading(): void {
    this.dispatchAccessibilityEvent('navigate-next-heading');
  }

  private navigateToPreviousHeading(): void {
    this.dispatchAccessibilityEvent('navigate-previous-heading');
  }

  private navigateToNextParagraph(): void {
    this.dispatchAccessibilityEvent('navigate-next-paragraph');
  }

  private navigateToPreviousParagraph(): void {
    this.dispatchAccessibilityEvent('navigate-previous-paragraph');
  }

  private navigateToNextList(): void {
    this.dispatchAccessibilityEvent('navigate-next-list');
  }

  private navigateToPreviousList(): void {
    this.dispatchAccessibilityEvent('navigate-previous-list');
  }

  private navigateToNextTable(): void {
    this.dispatchAccessibilityEvent('navigate-next-table');
  }

  private navigateToPreviousTable(): void {
    this.dispatchAccessibilityEvent('navigate-previous-table');
  }

  private navigateToNextGraphic(): void {
    this.dispatchAccessibilityEvent('navigate-next-graphic');
  }

  private navigateToPreviousGraphic(): void {
    this.dispatchAccessibilityEvent('navigate-previous-graphic');
  }

  private dispatchAccessibilityEvent(type: string): void {
    const event = new CustomEvent('pdf-accessibility-navigate', {
      detail: { type },
      bubbles: true,
    });
    document.dispatchEvent(event);
  }

  public addShortcut(shortcut: KeyboardShortcut): void {
    const key = this.createShortcutKey(shortcut);
    this.shortcuts.set(key, shortcut);
  }

  public removeShortcut(shortcut: KeyboardShortcut): void {
    const key = this.createShortcutKey(shortcut);
    this.shortcuts.delete(key);
  }

  private createShortcutKey(shortcut: KeyboardShortcut): string {
    const modifiers = [];
    if (shortcut.ctrlKey) modifiers.push('ctrl');
    if (shortcut.shiftKey) modifiers.push('shift');
    if (shortcut.altKey) modifiers.push('alt');
    if (shortcut.metaKey) modifiers.push('meta');
    
    return [...modifiers, shortcut.key.toLowerCase()].join('+');
  }

  public updateCallbacks(callbacks: Partial<NavigationCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  public updateConfig(config: Partial<KeyboardConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public getShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values());
  }

  public getShortcutsByCategory(category: KeyboardShortcut['category']): KeyboardShortcut[] {
    return this.getShortcuts().filter(shortcut => shortcut.category === category);
  }

  public getConfig(): KeyboardConfig {
    return { ...this.config };
  }

  public destroy(): void {
    // Remove event listeners
    this.eventListeners.forEach((listener, event) => {
      document.removeEventListener(event, listener);
    });

    // Clear data
    this.shortcuts.clear();
    this.eventListeners.clear();
    this.focusedElement = null;
  }
}
