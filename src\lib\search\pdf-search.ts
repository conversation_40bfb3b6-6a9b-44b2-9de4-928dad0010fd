/**
 * PDF Search Engine
 * Advanced search functionality with highlighting, regex support, and performance optimization
 */

import * as pdfjsLib from 'pdfjs-dist';

export interface SearchOptions {
  caseSensitive: boolean;
  wholeWords: boolean;
  useRegex: boolean;
  highlightAll: boolean;
  maxResults: number;
  searchInAnnotations: boolean;
  searchInMetadata: boolean;
  fuzzySearch: boolean;
  fuzzyThreshold: number; // 0-1, lower = more strict
}

export interface SearchResult {
  id: string;
  pageNumber: number;
  textContent: string;
  matchText: string;
  startIndex: number;
  endIndex: number;
  context: string;
  contextStart: number;
  contextEnd: number;
  confidence: number; // For fuzzy search
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface SearchProgress {
  currentPage: number;
  totalPages: number;
  resultsFound: number;
  isComplete: boolean;
  timeElapsed: number;
}

export interface SearchStats {
  totalSearches: number;
  averageSearchTime: number;
  mostSearchedTerms: Map<string, number>;
  lastSearchTime: number;
}

const DEFAULT_OPTIONS: SearchOptions = {
  caseSensitive: false,
  wholeWords: false,
  useRegex: false,
  highlightAll: true,
  maxResults: 1000,
  searchInAnnotations: true,
  searchInMetadata: true,
  fuzzySearch: false,
  fuzzyThreshold: 0.8,
};

export class PDFSearchEngine {
  private document: pdfjsLib.PDFDocumentProxy | null = null;
  private pageTexts: Map<number, string> = new Map();
  private pageTextItems: Map<number, any[]> = new Map();
  private currentSearch: AbortController | null = null;
  private searchCache: Map<string, SearchResult[]> = new Map();
  private stats: SearchStats = {
    totalSearches: 0,
    averageSearchTime: 0,
    mostSearchedTerms: new Map(),
    lastSearchTime: 0,
  };
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(document?: pdfjsLib.PDFDocumentProxy) {
    if (document) {
      this.setDocument(document);
    }
  }

  public async setDocument(document: pdfjsLib.PDFDocumentProxy): Promise<void> {
    this.document = document;
    this.pageTexts.clear();
    this.pageTextItems.clear();
    this.searchCache.clear();
    
    // Pre-extract text from all pages for better performance
    await this.preExtractText();
  }

  private async preExtractText(): Promise<void> {
    if (!this.document) return;

    const totalPages = this.document.numPages;
    
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      try {
        const page = await this.document.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        // Store full text
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        this.pageTexts.set(pageNum, pageText);
        
        // Store text items with positioning
        this.pageTextItems.set(pageNum, textContent.items);
        
      } catch (error) {
        console.warn(`Failed to extract text from page ${pageNum}:`, error);
      }
    }
  }

  public async search(
    query: string,
    options: Partial<SearchOptions> = {}
  ): Promise<SearchResult[]> {
    if (!this.document || !query.trim()) {
      return [];
    }

    const searchOptions = { ...DEFAULT_OPTIONS, ...options };
    const startTime = Date.now();
    
    // Cancel any ongoing search
    if (this.currentSearch) {
      this.currentSearch.abort();
    }
    
    this.currentSearch = new AbortController();
    
    // Check cache first
    const cacheKey = this.getCacheKey(query, searchOptions);
    if (this.searchCache.has(cacheKey)) {
      return this.searchCache.get(cacheKey)!;
    }

    try {
      const results = await this.performSearch(query, searchOptions);
      
      // Cache results
      this.searchCache.set(cacheKey, results);
      
      // Update stats
      this.updateStats(query, Date.now() - startTime);
      
      this.emit('search-complete', {
        query,
        results,
        timeElapsed: Date.now() - startTime,
      });
      
      return results;
      
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        this.emit('search-cancelled', { query });
        return [];
      }
      
      this.emit('search-error', { query, error });
      throw error;
    } finally {
      this.currentSearch = null;
    }
  }

  private async performSearch(
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const totalPages = this.document!.numPages;
    
    this.emit('search-progress', {
      currentPage: 0,
      totalPages,
      resultsFound: 0,
      isComplete: false,
      timeElapsed: 0,
    });

    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      if (this.currentSearch?.signal.aborted) {
        throw new Error('Search cancelled');
      }

      const pageResults = await this.searchPage(pageNum, query, options);
      results.push(...pageResults);
      
      this.emit('search-progress', {
        currentPage: pageNum,
        totalPages,
        resultsFound: results.length,
        isComplete: pageNum === totalPages,
        timeElapsed: Date.now(),
      });

      // Limit results
      if (results.length >= options.maxResults) {
        break;
      }
    }

    return results.slice(0, options.maxResults);
  }

  private async searchPage(
    pageNum: number,
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const pageText = this.pageTexts.get(pageNum);
    const textItems = this.pageTextItems.get(pageNum);
    
    if (!pageText || !textItems) {
      return [];
    }

    const results: SearchResult[] = [];
    let searchPattern: RegExp;

    try {
      if (options.useRegex) {
        const flags = options.caseSensitive ? 'g' : 'gi';
        searchPattern = new RegExp(query, flags);
      } else if (options.fuzzySearch) {
        return this.fuzzySearchPage(pageNum, query, options);
      } else {
        const escapedQuery = this.escapeRegExp(query);
        const pattern = options.wholeWords 
          ? `\\b${escapedQuery}\\b`
          : escapedQuery;
        const flags = options.caseSensitive ? 'g' : 'gi';
        searchPattern = new RegExp(pattern, flags);
      }
    } catch (error) {
      // Invalid regex
      return [];
    }

    let match;
    while ((match = searchPattern.exec(pageText)) !== null) {
      const result = await this.createSearchResult(
        pageNum,
        match,
        pageText,
        textItems,
        options
      );
      
      if (result) {
        results.push(result);
      }

      // Prevent infinite loop with zero-width matches
      if (match.index === searchPattern.lastIndex) {
        searchPattern.lastIndex++;
      }
    }

    return results;
  }

  private async fuzzySearchPage(
    pageNum: number,
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const pageText = this.pageTexts.get(pageNum);
    const textItems = this.pageTextItems.get(pageNum);
    
    if (!pageText || !textItems) {
      return [];
    }

    const results: SearchResult[] = [];
    const words = pageText.split(/\s+/);
    const queryLower = query.toLowerCase();

    for (let i = 0; i < words.length; i++) {
      const word = options.caseSensitive ? words[i] : words[i].toLowerCase();
      const similarity = this.calculateSimilarity(queryLower, word);
      
      if (similarity >= options.fuzzyThreshold) {
        const wordStart = pageText.indexOf(words[i], i > 0 ? pageText.indexOf(words[i - 1]) + words[i - 1].length : 0);
        const wordEnd = wordStart + words[i].length;
        
        const match = {
          0: words[i],
          index: wordStart,
          input: pageText,
        };

        const result = await this.createSearchResult(
          pageNum,
          match as RegExpExecArray,
          pageText,
          textItems,
          options
        );
        
        if (result) {
          result.confidence = similarity;
          results.push(result);
        }
      }
    }

    return results.sort((a, b) => b.confidence - a.confidence);
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Levenshtein distance-based similarity
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }

  private async createSearchResult(
    pageNum: number,
    match: RegExpExecArray,
    pageText: string,
    textItems: any[],
    options: SearchOptions
  ): Promise<SearchResult | null> {
    const matchStart = match.index!;
    const matchEnd = matchStart + match[0].length;
    
    // Get context
    const contextRadius = 50;
    const contextStart = Math.max(0, matchStart - contextRadius);
    const contextEnd = Math.min(pageText.length, matchEnd + contextRadius);
    const context = pageText.substring(contextStart, contextEnd);
    
    // Find bounding box
    const bounds = this.findTextBounds(matchStart, matchEnd, textItems);
    
    return {
      id: `${pageNum}-${matchStart}-${matchEnd}`,
      pageNumber: pageNum,
      textContent: pageText,
      matchText: match[0],
      startIndex: matchStart,
      endIndex: matchEnd,
      context,
      contextStart,
      contextEnd,
      confidence: 1.0,
      bounds,
    };
  }

  private findTextBounds(
    startIndex: number,
    endIndex: number,
    textItems: any[]
  ): SearchResult['bounds'] {
    let currentIndex = 0;
    let startBounds: any = null;
    let endBounds: any = null;
    
    for (const item of textItems) {
      const itemLength = item.str.length;
      const itemStart = currentIndex;
      const itemEnd = currentIndex + itemLength;
      
      if (!startBounds && itemEnd > startIndex) {
        startBounds = item;
      }
      
      if (itemStart < endIndex) {
        endBounds = item;
      }
      
      if (itemStart >= endIndex) {
        break;
      }
      
      currentIndex = itemEnd + 1; // +1 for space
    }
    
    if (!startBounds || !endBounds) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }
    
    return {
      x: Math.min(startBounds.transform[4], endBounds.transform[4]),
      y: Math.min(startBounds.transform[5], endBounds.transform[5]),
      width: Math.abs(endBounds.transform[4] - startBounds.transform[4]) + endBounds.width,
      height: Math.max(startBounds.height, endBounds.height),
    };
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private getCacheKey(query: string, options: SearchOptions): string {
    return JSON.stringify({ query, options });
  }

  private updateStats(query: string, searchTime: number): void {
    this.stats.totalSearches++;
    this.stats.averageSearchTime = 
      (this.stats.averageSearchTime * (this.stats.totalSearches - 1) + searchTime) / this.stats.totalSearches;
    this.stats.lastSearchTime = Date.now();
    
    const normalizedQuery = query.toLowerCase().trim();
    const currentCount = this.stats.mostSearchedTerms.get(normalizedQuery) || 0;
    this.stats.mostSearchedTerms.set(normalizedQuery, currentCount + 1);
  }

  public cancelSearch(): void {
    if (this.currentSearch) {
      this.currentSearch.abort();
    }
  }

  public clearCache(): void {
    this.searchCache.clear();
  }

  public getStats(): SearchStats {
    return {
      ...this.stats,
      mostSearchedTerms: new Map(this.stats.mostSearchedTerms),
    };
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Error in search event listener:', error);
      }
    });
  }

  public highlightResults(results: SearchResult[], pageNumber: number): HTMLElement[] {
    // This would be implemented to create highlight overlays
    // Returns array of highlight elements for the specified page
    return [];
  }

  public clearHighlights(): void {
    // Remove all highlight overlays
    const highlights = document.querySelectorAll('[data-search-highlight]');
    highlights.forEach(highlight => highlight.remove());
  }

  public destroy(): void {
    this.cancelSearch();
    this.clearCache();
    this.pageTexts.clear();
    this.pageTextItems.clear();
    this.eventListeners.clear();
  }
}
