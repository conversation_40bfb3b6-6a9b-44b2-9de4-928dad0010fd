"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Keyboard, 
  Search, 
  Navigation, 
  ZoomIn, 
  Eye, 
  Settings, 
  Accessibility,
  HelpCircle,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { KeyboardNavigationHandler, type KeyboardShortcut } from '@/lib/navigation/keyboard-handler';

interface KeyboardShortcutsHelpProps {
  keyboardHandler: KeyboardNavigationHandler;
  trigger?: React.ReactNode;
  className?: string;
}

export default function KeyboardShortcutsHelp({
  keyboardHandler,
  trigger,
  className,
}: KeyboardShortcutsHelpProps) {
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setShortcuts(keyboardHandler.getShortcuts());
  }, [keyboardHandler]);

  const filteredShortcuts = shortcuts.filter(shortcut =>
    shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    shortcut.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
    shortcut.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const shortcutsByCategory = filteredShortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  const formatShortcutKey = (shortcut: KeyboardShortcut): string => {
    const parts = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.metaKey) parts.push('Cmd');
    
    // Format special keys
    let key = shortcut.key;
    switch (key.toLowerCase()) {
      case ' ':
        key = 'Space';
        break;
      case 'arrowup':
        key = '↑';
        break;
      case 'arrowdown':
        key = '↓';
        break;
      case 'arrowleft':
        key = '←';
        break;
      case 'arrowright':
        key = '→';
        break;
      case 'pageup':
        key = 'Page Up';
        break;
      case 'pagedown':
        key = 'Page Down';
        break;
      case 'escape':
        key = 'Esc';
        break;
      default:
        key = key.charAt(0).toUpperCase() + key.slice(1);
    }
    
    parts.push(key);
    return parts.join(' + ');
  };

  const getCategoryIcon = (category: KeyboardShortcut['category']) => {
    switch (category) {
      case 'navigation':
        return <Navigation className="h-4 w-4" />;
      case 'zoom':
        return <ZoomIn className="h-4 w-4" />;
      case 'view':
        return <Eye className="h-4 w-4" />;
      case 'accessibility':
        return <Accessibility className="h-4 w-4" />;
      case 'general':
        return <Settings className="h-4 w-4" />;
      default:
        return <Keyboard className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: KeyboardShortcut['category']) => {
    switch (category) {
      case 'navigation':
        return 'text-blue-500';
      case 'zoom':
        return 'text-green-500';
      case 'view':
        return 'text-purple-500';
      case 'accessibility':
        return 'text-orange-500';
      case 'general':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const getCategoryTitle = (category: KeyboardShortcut['category']) => {
    switch (category) {
      case 'navigation':
        return 'Navigation';
      case 'zoom':
        return 'Zoom & Scale';
      case 'view':
        return 'View Controls';
      case 'accessibility':
        return 'Accessibility';
      case 'general':
        return 'General';
      default:
        return category;
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Keyboard className="h-4 w-4 mr-2" />
      Keyboard Shortcuts
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className={cn("max-w-4xl max-h-[80vh] overflow-hidden", className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Navigate and control the PDF viewer using keyboard shortcuts
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search shortcuts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Shortcuts by Category */}
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="navigation">Navigation</TabsTrigger>
              <TabsTrigger value="zoom">Zoom</TabsTrigger>
              <TabsTrigger value="view">View</TabsTrigger>
              <TabsTrigger value="accessibility">A11y</TabsTrigger>
              <TabsTrigger value="general">General</TabsTrigger>
            </TabsList>

            <div className="max-h-[50vh] overflow-y-auto">
              <TabsContent value="all" className="space-y-6">
                {Object.entries(shortcutsByCategory).map(([category, categoryShortcuts]) => (
                  <Card key={category}>
                    <CardHeader className="pb-3">
                      <CardTitle className={cn("text-lg flex items-center gap-2", getCategoryColor(category as any))}>
                        {getCategoryIcon(category as any)}
                        {getCategoryTitle(category as any)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {categoryShortcuts.map((shortcut, index) => (
                          <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                            <span className="text-sm">{shortcut.description}</span>
                            <Badge variant="outline" className="font-mono text-xs">
                              {formatShortcutKey(shortcut)}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              {Object.entries(shortcutsByCategory).map(([category, categoryShortcuts]) => (
                <TabsContent key={category} value={category} className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className={cn("flex items-center gap-2", getCategoryColor(category as any))}>
                        {getCategoryIcon(category as any)}
                        {getCategoryTitle(category as any)} Shortcuts
                      </CardTitle>
                      <CardDescription>
                        {categoryShortcuts.length} shortcuts available
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {categoryShortcuts.map((shortcut, index) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                            <div className="space-y-1">
                              <div className="font-medium text-sm">{shortcut.description}</div>
                              {shortcut.enabled === false && (
                                <Badge variant="secondary" className="text-xs">Disabled</Badge>
                              )}
                            </div>
                            <Badge variant="outline" className="font-mono">
                              {formatShortcutKey(shortcut)}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </div>
          </Tabs>

          {/* Quick Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sm">
                <HelpCircle className="h-4 w-4" />
                Quick Tips
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">?</Badge>
                    <span>Show this help dialog</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">Esc</Badge>
                    <span>Close dialogs and cancel actions</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">F11</Badge>
                    <span>Toggle fullscreen mode</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">Ctrl + F</Badge>
                    <span>Search within document</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">Ctrl + G</Badge>
                    <span>Go to specific page</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="font-mono text-xs">Space</Badge>
                    <span>Next page (or read aloud if accessibility enabled)</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Platform-specific Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Platform Notes</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-muted-foreground">
              <div className="space-y-2">
                <div>
                  <strong>Windows/Linux:</strong> Use <Badge variant="outline" className="font-mono text-xs mx-1">Ctrl</Badge> for shortcuts
                </div>
                <div>
                  <strong>macOS:</strong> Use <Badge variant="outline" className="font-mono text-xs mx-1">Cmd</Badge> instead of Ctrl for most shortcuts
                </div>
                <div>
                  <strong>Accessibility:</strong> Enable screen reader mode for additional navigation shortcuts
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Close Button */}
        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            <X className="h-4 w-4 mr-2" />
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easy integration
export function useKeyboardShortcuts() {
  const [isHelpOpen, setIsHelpOpen] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Show help with ? key
      if (event.key === '?' && !event.ctrlKey && !event.altKey && !event.metaKey) {
        const target = event.target as HTMLElement;
        const isInputElement = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;
        
        if (!isInputElement) {
          event.preventDefault();
          setIsHelpOpen(true);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isHelpOpen,
    setIsHelpOpen,
    showHelp: () => setIsHelpOpen(true),
    hideHelp: () => setIsHelpOpen(false),
  };
}
