/**
 * PDF Viewer Plugin System
 * Extensible architecture for adding custom functionality
 */

export interface PluginContext {
  document: any;
  currentPage: number;
  totalPages: number;
  zoomLevel: number;
  rotation: number;
  canvas: HTMLCanvasElement | null;
  container: HTMLElement | null;
  emit: (event: string, data: any) => void;
  on: (event: string, handler: (data: any) => void) => void;
  off: (event: string, handler: (data: any) => void) => void;
}

export interface PluginConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies?: string[];
  settings?: Record<string, any>;
}

export interface Plugin {
  config: PluginConfig;
  initialize: (context: PluginContext) => Promise<void> | void;
  destroy: () => Promise<void> | void;
  onDocumentLoad?: (document: any) => Promise<void> | void;
  onPageChange?: (page: number) => Promise<void> | void;
  onZoomChange?: (zoom: number) => Promise<void> | void;
  onRotationChange?: (rotation: number) => Promise<void> | void;
  onRender?: (canvas: HTMLCanvasElement, page: any) => Promise<void> | void;
  getMenuItems?: () => PluginMenuItem[];
  getToolbarItems?: () => PluginToolbarItem[];
  getSettingsPanel?: () => React.ComponentType<any>;
}

export interface PluginMenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<any>;
  action: () => void;
  separator?: boolean;
  submenu?: PluginMenuItem[];
}

export interface PluginToolbarItem {
  id: string;
  component: React.ComponentType<any>;
  position: 'left' | 'center' | 'right';
  order: number;
}

export class PluginManager {
  private plugins: Map<string, Plugin> = new Map();
  private context: PluginContext;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(context: PluginContext) {
    this.context = context;
  }

  public async registerPlugin(plugin: Plugin): Promise<void> {
    const { name } = plugin.config;
    
    if (this.plugins.has(name)) {
      throw new Error(`Plugin "${name}" is already registered`);
    }

    // Check dependencies
    if (plugin.config.dependencies) {
      for (const dep of plugin.config.dependencies) {
        if (!this.plugins.has(dep)) {
          throw new Error(`Plugin "${name}" requires dependency "${dep}"`);
        }
      }
    }

    try {
      await plugin.initialize(this.context);
      this.plugins.set(name, plugin);
      
      this.emit('plugin-registered', { plugin: name });
    } catch (error) {
      throw new Error(`Failed to initialize plugin "${name}": ${error}`);
    }
  }

  public async unregisterPlugin(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new Error(`Plugin "${name}" is not registered`);
    }

    // Check if other plugins depend on this one
    for (const [pluginName, p] of this.plugins) {
      if (p.config.dependencies?.includes(name)) {
        throw new Error(`Cannot unregister "${name}" - plugin "${pluginName}" depends on it`);
      }
    }

    try {
      await plugin.destroy();
      this.plugins.delete(name);
      
      this.emit('plugin-unregistered', { plugin: name });
    } catch (error) {
      console.error(`Error destroying plugin "${name}":`, error);
    }
  }

  public getPlugin(name: string): Plugin | undefined {
    return this.plugins.get(name);
  }

  public getPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  public getMenuItems(): PluginMenuItem[] {
    const items: PluginMenuItem[] = [];
    
    for (const plugin of this.plugins.values()) {
      if (plugin.getMenuItems) {
        items.push(...plugin.getMenuItems());
      }
    }
    
    return items;
  }

  public getToolbarItems(): PluginToolbarItem[] {
    const items: PluginToolbarItem[] = [];
    
    for (const plugin of this.plugins.values()) {
      if (plugin.getToolbarItems) {
        items.push(...plugin.getToolbarItems());
      }
    }
    
    return items.sort((a, b) => a.order - b.order);
  }

  public async notifyDocumentLoad(document: any): Promise<void> {
    for (const plugin of this.plugins.values()) {
      if (plugin.onDocumentLoad) {
        try {
          await plugin.onDocumentLoad(document);
        } catch (error) {
          console.error(`Plugin "${plugin.config.name}" document load error:`, error);
        }
      }
    }
  }

  public async notifyPageChange(page: number): Promise<void> {
    for (const plugin of this.plugins.values()) {
      if (plugin.onPageChange) {
        try {
          await plugin.onPageChange(page);
        } catch (error) {
          console.error(`Plugin "${plugin.config.name}" page change error:`, error);
        }
      }
    }
  }

  public async notifyZoomChange(zoom: number): Promise<void> {
    for (const plugin of this.plugins.values()) {
      if (plugin.onZoomChange) {
        try {
          await plugin.onZoomChange(zoom);
        } catch (error) {
          console.error(`Plugin "${plugin.config.name}" zoom change error:`, error);
        }
      }
    }
  }

  public async notifyRotationChange(rotation: number): Promise<void> {
    for (const plugin of this.plugins.values()) {
      if (plugin.onRotationChange) {
        try {
          await plugin.onRotationChange(rotation);
        } catch (error) {
          console.error(`Plugin "${plugin.config.name}" rotation change error:`, error);
        }
      }
    }
  }

  public async notifyRender(canvas: HTMLCanvasElement, page: any): Promise<void> {
    for (const plugin of this.plugins.values()) {
      if (plugin.onRender) {
        try {
          await plugin.onRender(canvas, page);
        } catch (error) {
          console.error(`Plugin "${plugin.config.name}" render error:`, error);
        }
      }
    }
  }

  public emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Event listener error for "${event}":`, error);
        }
      });
    }
  }

  public on(event: string, handler: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(handler);
  }

  public off(event: string, handler: (data: any) => void): void {
    this.eventListeners.get(event)?.delete(handler);
  }

  public async destroy(): Promise<void> {
    const pluginNames = Array.from(this.plugins.keys());
    
    for (const name of pluginNames) {
      try {
        await this.unregisterPlugin(name);
      } catch (error) {
        console.error(`Error unregistering plugin "${name}":`, error);
      }
    }
    
    this.eventListeners.clear();
  }
}

// Example Plugin: Annotations
export class AnnotationPlugin implements Plugin {
  config: PluginConfig = {
    name: 'annotations',
    version: '1.0.0',
    description: 'Add annotation support to PDF viewer',
    author: 'Cobalt Team',
  };

  private annotations: Map<number, any[]> = new Map();
  private context: PluginContext | null = null;

  async initialize(context: PluginContext): Promise<void> {
    this.context = context;
    
    // Setup annotation layer
    this.setupAnnotationLayer();
    
    // Listen for events
    context.on('annotation-add', this.addAnnotation.bind(this));
    context.on('annotation-remove', this.removeAnnotation.bind(this));
  }

  async destroy(): Promise<void> {
    this.annotations.clear();
    this.removeAnnotationLayer();
  }

  onPageChange(page: number): void {
    this.renderAnnotations(page);
  }

  getMenuItems(): PluginMenuItem[] {
    return [
      {
        id: 'add-annotation',
        label: 'Add Annotation',
        action: () => this.startAnnotationMode(),
      },
      {
        id: 'export-annotations',
        label: 'Export Annotations',
        action: () => this.exportAnnotations(),
      },
    ];
  }

  getToolbarItems(): PluginToolbarItem[] {
    return [
      {
        id: 'annotation-tool',
        component: AnnotationToolbar,
        position: 'right',
        order: 100,
      },
    ];
  }

  private setupAnnotationLayer(): void {
    if (!this.context?.container) return;
    
    const layer = document.createElement('div');
    layer.className = 'annotation-layer';
    layer.style.position = 'absolute';
    layer.style.top = '0';
    layer.style.left = '0';
    layer.style.width = '100%';
    layer.style.height = '100%';
    layer.style.pointerEvents = 'none';
    
    this.context.container.appendChild(layer);
  }

  private removeAnnotationLayer(): void {
    if (!this.context?.container) return;
    
    const layer = this.context.container.querySelector('.annotation-layer');
    if (layer) {
      layer.remove();
    }
  }

  private addAnnotation(data: any): void {
    const { page, annotation } = data;
    
    if (!this.annotations.has(page)) {
      this.annotations.set(page, []);
    }
    
    this.annotations.get(page)!.push(annotation);
    this.renderAnnotations(page);
  }

  private removeAnnotation(data: any): void {
    const { page, annotationId } = data;
    const pageAnnotations = this.annotations.get(page);
    
    if (pageAnnotations) {
      const index = pageAnnotations.findIndex(a => a.id === annotationId);
      if (index !== -1) {
        pageAnnotations.splice(index, 1);
        this.renderAnnotations(page);
      }
    }
  }

  private renderAnnotations(page: number): void {
    // Implementation for rendering annotations on the page
    const annotations = this.annotations.get(page) || [];
    // Render each annotation...
  }

  private startAnnotationMode(): void {
    // Implementation for starting annotation mode
  }

  private exportAnnotations(): void {
    // Implementation for exporting annotations
    const allAnnotations = Object.fromEntries(this.annotations);
    const blob = new Blob([JSON.stringify(allAnnotations, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'annotations.json';
    a.click();
    
    URL.revokeObjectURL(url);
  }
}

// Annotation Toolbar Component
const AnnotationToolbar: React.FC = () => {
  return (
    <div className="flex items-center gap-2">
      <button className="p-2 rounded hover:bg-gray-200">
        📝
      </button>
      <button className="p-2 rounded hover:bg-gray-200">
        🖍️
      </button>
      <button className="p-2 rounded hover:bg-gray-200">
        📌
      </button>
    </div>
  );
};
