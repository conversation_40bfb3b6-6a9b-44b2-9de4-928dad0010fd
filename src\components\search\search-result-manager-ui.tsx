"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  History, 
  Bookmark, 
  Plus, 
  Search, 
  Download, 
  Upload,
  Trash2,
  Edit,
  Share,
  Tag,
  Clock,
  BarChart3,
  FileText,
  Star,
  Filter,
  X,
  CheckSquare,
  Square,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  SearchResultManager, 
  type SearchSession, 
  type SearchCollection,
  type SearchAnalytics,
} from '@/lib/search/search-result-manager';

interface SearchResultManagerUIProps {
  resultManager: SearchResultManager;
  onNavigateToResult?: (documentId: string, pageNumber: number) => void;
  className?: string;
}

export default function SearchResultManagerUI({
  resultManager,
  onNavigateToResult,
  className,
}: SearchResultManagerUIProps) {
  const [searchHistory, setSearchHistory] = useState<SearchSession[]>([]);
  const [collections, setCollections] = useState<SearchCollection[]>([]);
  const [analytics, setAnalytics] = useState<SearchAnalytics | null>(null);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());
  const [historyFilter, setHistoryFilter] = useState('');
  const [showCreateCollection, setShowCreateCollection] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [newCollectionTags, setNewCollectionTags] = useState('');

  // Load data on mount
  useEffect(() => {
    updateData();
  }, []);

  // Listen for result manager events
  useEffect(() => {
    const handleUpdate = () => updateData();

    resultManager.addEventListener('session-ended', handleUpdate);
    resultManager.addEventListener('collection-created', handleUpdate);
    resultManager.addEventListener('collection-updated', handleUpdate);
    resultManager.addEventListener('collection-deleted', handleUpdate);

    return () => {
      resultManager.removeEventListener('session-ended', handleUpdate);
      resultManager.removeEventListener('collection-created', handleUpdate);
      resultManager.removeEventListener('collection-updated', handleUpdate);
      resultManager.removeEventListener('collection-deleted', handleUpdate);
    };
  }, [resultManager]);

  const updateData = useCallback(() => {
    setSearchHistory(resultManager.getSearchHistory(50));
    setCollections(resultManager.getCollections());
    setAnalytics(resultManager.getAnalytics());
  }, [resultManager]);

  const handleResultSelect = useCallback((resultId: string) => {
    const newSelected = new Set(selectedResults);
    if (newSelected.has(resultId)) {
      newSelected.delete(resultId);
    } else {
      newSelected.add(resultId);
    }
    setSelectedResults(newSelected);
  }, [selectedResults]);

  const handleCreateCollection = useCallback(() => {
    if (!newCollectionName.trim() || selectedResults.size === 0) return;

    const tags = newCollectionTags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    resultManager.createCollection(
      newCollectionName,
      newCollectionDescription,
      Array.from(selectedResults),
      tags
    );

    setNewCollectionName('');
    setNewCollectionDescription('');
    setNewCollectionTags('');
    setSelectedResults(new Set());
    setShowCreateCollection(false);
  }, [resultManager, newCollectionName, newCollectionDescription, newCollectionTags, selectedResults]);

  const handleExportCollection = useCallback((collectionId: string) => {
    const data = resultManager.exportCollection(collectionId);
    if (data) {
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: 'application/json',
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `search-collection-${data.name.replace(/\s+/g, '-')}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  }, [resultManager]);

  const handleImportCollection = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        resultManager.importCollection(data);
      } catch (error) {
        console.error('Failed to import collection:', error);
      }
    };
    reader.readAsText(file);
  }, [resultManager]);

  const filteredHistory = searchHistory.filter(session =>
    session.query.toLowerCase().includes(historyFilter.toLowerCase()) ||
    session.notes.toLowerCase().includes(historyFilter.toLowerCase()) ||
    session.tags.some(tag => tag.toLowerCase().includes(historyFilter.toLowerCase()))
  );

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Search Result Manager
          </CardTitle>
          <CardDescription>
            Manage your search history, create collections, and analyze search patterns
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="history" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          {/* Search History Controls */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Input
                placeholder="Filter search history..."
                value={historyFilter}
                onChange={(e) => setHistoryFilter(e.target.value)}
                className="max-w-sm"
              />
            </div>
            
            <div className="flex items-center gap-2">
              {selectedResults.size > 0 && (
                <>
                  <Badge variant="outline">
                    {selectedResults.size} selected
                  </Badge>
                  
                  <Dialog open={showCreateCollection} onOpenChange={setShowCreateCollection}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Collection
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create Collection</DialogTitle>
                        <DialogDescription>
                          Create a new collection from {selectedResults.size} selected results
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="collection-name">Name</Label>
                          <Input
                            id="collection-name"
                            value={newCollectionName}
                            onChange={(e) => setNewCollectionName(e.target.value)}
                            placeholder="Enter collection name"
                          />
                        </div>
                        
                        <div>
                          <Label htmlFor="collection-description">Description</Label>
                          <Textarea
                            id="collection-description"
                            value={newCollectionDescription}
                            onChange={(e) => setNewCollectionDescription(e.target.value)}
                            placeholder="Enter collection description"
                          />
                        </div>
                        
                        <div>
                          <Label htmlFor="collection-tags">Tags (comma-separated)</Label>
                          <Input
                            id="collection-tags"
                            value={newCollectionTags}
                            onChange={(e) => setNewCollectionTags(e.target.value)}
                            placeholder="research, important, reference"
                          />
                        </div>
                        
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" onClick={() => setShowCreateCollection(false)}>
                            Cancel
                          </Button>
                          <Button onClick={handleCreateCollection}>
                            Create Collection
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => resultManager.clearHistory()}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear History
              </Button>
            </div>
          </div>

          {/* Search History List */}
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {filteredHistory.map((session) => (
                <Card key={session.id} className="hover:bg-muted/50">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Session Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{session.query}</h4>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                            <span className="flex items-center gap-1">
                              <Search className="h-3 w-3" />
                              {session.searchType}
                            </span>
                            <span className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              {session.resultCount} results
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatDuration(session.duration)}
                            </span>
                            <span>{session.timestamp.toLocaleDateString()}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {session.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Session Notes */}
                      {session.notes && (
                        <div className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                          {session.notes}
                        </div>
                      )}

                      {/* Results */}
                      {session.results.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Results:</Label>
                          <div className="space-y-1">
                            {session.results.slice(0, 3).map((result) => (
                              <div
                                key={result.id}
                                className="flex items-center justify-between p-2 rounded border hover:bg-muted/50 cursor-pointer"
                                onClick={() => onNavigateToResult?.(result.documentId, result.pageNumber)}
                              >
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleResultSelect(result.id);
                                    }}
                                    className="p-1"
                                  >
                                    {selectedResults.has(result.id) ? (
                                      <CheckSquare className="h-4 w-4 text-primary" />
                                    ) : (
                                      <Square className="h-4 w-4" />
                                    )}
                                  </button>
                                  
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">{result.documentTitle}</div>
                                    <div className="text-xs text-muted-foreground">
                                      Page {result.pageNumber} • {Math.round(result.relevanceScore * 100)}% relevance
                                    </div>
                                  </div>
                                </div>
                                
                                <Badge variant="outline" className="text-xs">
                                  {result.metadata.searchType}
                                </Badge>
                              </div>
                            ))}
                            
                            {session.results.length > 3 && (
                              <div className="text-xs text-muted-foreground text-center py-1">
                                +{session.results.length - 3} more results
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {filteredHistory.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  {historyFilter ? 'No matching search sessions found' : 'No search history yet'}
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="collections" className="space-y-4">
          {/* Collections Controls */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Collections ({collections.length})
            </h3>
            
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept=".json"
                onChange={handleImportCollection}
                className="hidden"
                id="import-collection"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('import-collection')?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
            </div>
          </div>

          {/* Collections List */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {collections.map((collection) => (
              <Card key={collection.id} className="hover:bg-muted/50">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{collection.name}</CardTitle>
                      <CardDescription>{collection.description}</CardDescription>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleExportCollection(collection.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => resultManager.deleteCollection(collection.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Results:</span> {collection.metadata.totalResults}
                    </div>
                    <div>
                      <span className="font-medium">Documents:</span> {collection.metadata.documentsCount}
                    </div>
                    <div>
                      <span className="font-medium">Avg Relevance:</span> {Math.round(collection.metadata.averageRelevance * 100)}%
                    </div>
                    <div>
                      <span className="font-medium">Updated:</span> {collection.updatedAt.toLocaleDateString()}
                    </div>
                  </div>
                  
                  {collection.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {collection.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <div className="flex flex-wrap gap-1">
                    {collection.metadata.searchTypes.map((type) => (
                      <Badge key={type} variant="secondary" className="text-xs">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {collections.length === 0 && (
              <div className="col-span-full text-center text-muted-foreground py-8">
                No collections yet. Select search results and create your first collection.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && (
            <>
              {/* Analytics Overview */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {analytics.totalSearches}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Searches</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {analytics.uniqueQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">Unique Queries</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {Math.round(analytics.averageResultsPerSearch)}
                    </div>
                    <div className="text-sm text-muted-foreground">Avg Results</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {Math.round(analytics.successRate)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </CardContent>
                </Card>
              </div>

              {/* Most Searched Terms */}
              <Card>
                <CardHeader>
                  <CardTitle>Most Searched Terms</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analytics.mostSearchedTerms.slice(0, 10).map((term, index) => (
                      <div key={term.term} className="flex items-center justify-between">
                        <span className="text-sm">{term.term}</span>
                        <Badge variant="outline">{term.count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Search Type Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Search Type Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(analytics.searchTypeDistribution).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{type}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
