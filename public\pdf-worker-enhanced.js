/**
 * Enhanced PDF.js Worker
 * Optimized worker with memory management and parallel processing
 */

// Import PDF.js worker
importScripts('/pdf.worker.min.js');

class EnhancedPDFWorker {
  constructor() {
    this.config = {
      enableMemoryOptimization: true,
      memoryLimit: 512 * 1024 * 1024, // 512MB
    };
    
    this.memoryUsage = 0;
    this.taskCount = 0;
    this.cache = new Map();
    this.maxCacheSize = 50;
    
    this.setupMessageHandler();
    this.startMemoryMonitoring();
  }

  setupMessageHandler() {
    self.onmessage = (event) => {
      const { type, taskId, taskType, data, config } = event.data;

      try {
        switch (type) {
          case 'init':
            this.handleInit(config);
            break;
          case 'execute-task':
            this.handleTask(taskId, taskType, data);
            break;
          case 'update-config':
            this.updateConfig(config);
            break;
          case 'optimize-memory':
            this.optimizeMemory();
            break;
          default:
            console.warn('Unknown message type:', type);
        }
      } catch (error) {
        this.sendMessage({
          type: 'task-complete',
          taskId,
          error: error.message,
        });
      }
    };
  }

  handleInit(config) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    console.log('Enhanced PDF Worker initialized with config:', this.config);
  }

  async handleTask(taskId, taskType, data) {
    const startTime = performance.now();
    this.taskCount++;

    try {
      let result;

      switch (taskType) {
        case 'render':
          result = await this.renderPage(data);
          break;
        case 'extract':
          result = await this.extractText(data);
          break;
        case 'parse':
          result = await this.parsePDF(data);
          break;
        default:
          throw new Error(`Unknown task type: ${taskType}`);
      }

      const duration = performance.now() - startTime;
      
      this.sendMessage({
        type: 'task-complete',
        taskId,
        data: result,
        duration,
      });

    } catch (error) {
      this.sendMessage({
        type: 'task-complete',
        taskId,
        error: error.message,
      });
    }
  }

  async renderPage(data) {
    const { pageData, viewport, canvasData, options } = data;
    
    // Check cache first
    const cacheKey = `render-${pageData.pageNumber}-${viewport.width}x${viewport.height}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Create offscreen canvas
    const canvas = new OffscreenCanvas(canvasData.width, canvasData.height);
    const context = canvas.getContext('2d');

    // Configure rendering context
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
      enableWebGL: options.enableWebGL || false,
      renderInteractiveForms: options.renderInteractiveForms || false,
      optionalContentConfigPromise: options.optionalContentConfigPromise,
    };

    // Progressive rendering support
    if (options.enableProgressiveRendering) {
      renderContext.continueCallback = (cont) => {
        // Send progress update
        this.sendMessage({
          type: 'render-progress',
          progress: cont.getProgress(),
        });
        cont();
      };
    }

    // Render the page
    const renderTask = pageData.render(renderContext);
    await renderTask.promise;

    // Get image data
    const imageData = context.getImageData(0, 0, canvasData.width, canvasData.height);
    
    const result = {
      imageData: imageData,
      width: canvasData.width,
      height: canvasData.height,
    };

    // Cache the result
    this.cacheResult(cacheKey, result);

    return result;
  }

  async extractText(data) {
    const { pageData } = data;
    
    // Check cache first
    const cacheKey = `text-${pageData.pageNumber}`;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Extract text content
    const textContent = await pageData.getTextContent({
      normalizeWhitespace: true,
      disableCombineTextItems: false,
    });

    // Process text items
    const text = textContent.items
      .map(item => item.str)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    const result = {
      text,
      items: textContent.items,
      styles: textContent.styles,
    };

    // Cache the result
    this.cacheResult(cacheKey, result);

    return result;
  }

  async parsePDF(data) {
    const { pdfData } = data;
    
    // This would typically be handled by the main thread
    // but included here for completeness
    const loadingTask = pdfjsLib.getDocument({
      data: pdfData,
      isEvalSupported: false,
      disableAutoFetch: true,
      disableStream: false,
    });

    const pdf = await loadingTask.promise;
    
    return {
      numPages: pdf.numPages,
      fingerprint: pdf.fingerprint,
      info: await pdf.getMetadata(),
    };
  }

  cacheResult(key, result) {
    // Implement LRU cache
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, result);
  }

  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('Worker config updated:', this.config);
  }

  optimizeMemory() {
    // Clear cache
    this.cache.clear();
    
    // Force garbage collection if available
    if (self.gc) {
      self.gc();
    }
    
    // Report memory usage
    this.reportMemoryUsage();
  }

  startMemoryMonitoring() {
    // Monitor memory usage every 5 seconds
    setInterval(() => {
      this.reportMemoryUsage();
      
      // Auto-optimize if memory usage is high
      if (this.memoryUsage > this.config.memoryLimit * 0.8) {
        this.optimizeMemory();
      }
    }, 5000);
  }

  reportMemoryUsage() {
    // Estimate memory usage
    let estimatedUsage = 0;
    
    // Cache memory usage
    for (const [key, value] of this.cache) {
      if (value.imageData) {
        estimatedUsage += value.imageData.data.length * 4; // RGBA
      }
      if (value.text) {
        estimatedUsage += value.text.length * 2; // UTF-16
      }
    }

    // Add base worker memory
    estimatedUsage += 10 * 1024 * 1024; // 10MB base

    this.memoryUsage = estimatedUsage;

    this.sendMessage({
      type: 'memory-usage',
      data: {
        memoryUsage: this.memoryUsage,
        cacheSize: this.cache.size,
        taskCount: this.taskCount,
      },
    });
  }

  sendMessage(message) {
    self.postMessage(message);
  }
}

// Initialize the enhanced worker
const enhancedWorker = new EnhancedPDFWorker();

// Handle unhandled errors
self.onerror = (error) => {
  console.error('Worker error:', error);
  enhancedWorker.sendMessage({
    type: 'error',
    error: error.message,
  });
};

// Handle unhandled promise rejections
self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason);
  enhancedWorker.sendMessage({
    type: 'error',
    error: event.reason?.message || 'Unhandled promise rejection',
  });
};
