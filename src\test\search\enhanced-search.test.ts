import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useEnhancedSearch } from '@/hooks/use-enhanced-search';
import { SearchSuggestionsEngine } from '@/lib/search/search-suggestions-engine';
import type { AdvancedSearchQuery, SearchResult } from '@/components/search/advanced-search-interface';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock search function
const mockSearchFunction = vi.fn().mockImplementation(async (query: AdvancedSearchQuery): Promise<SearchResult[]> => {
  await new Promise(resolve => setTimeout(resolve, 100)); // Simulate async delay
  
  if (query.text === 'error') {
    throw new Error('Search failed');
  }
  
  if (!query.text.trim()) {
    return [];
  }

  return [
    {
      id: '1',
      documentId: 'doc-1',
      title: 'Test Document',
      pageNumber: 1,
      content: `This is a test document containing ${query.text}`,
      highlightedContent: `This is a test document containing <mark>${query.text}</mark>`,
      context: 'Test context',
      type: 'text',
      relevanceScore: 0.9,
      position: { x: 0, y: 0, width: 100, height: 20 },
      tags: ['test'],
    },
    {
      id: '2',
      documentId: 'doc-1',
      title: 'Test Document',
      pageNumber: 2,
      content: `Another section with ${query.text} content`,
      highlightedContent: `Another section with <mark>${query.text}</mark> content`,
      context: 'Another context',
      type: 'annotation',
      relevanceScore: 0.8,
      position: { x: 0, y: 100, width: 100, height: 20 },
      tags: ['test', 'annotation'],
    },
  ];
});

describe('useEnhancedSearch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Basic Search Functionality', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      expect(result.current.query.text).toBe('');
      expect(result.current.results).toEqual([]);
      expect(result.current.currentIndex).toBe(-1);
      expect(result.current.isSearching).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should update search text and trigger search', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.updateSearchText('test query');
      });

      expect(result.current.query.text).toBe('test query');
      expect(result.current.isSearching).toBe(false); // Not searching yet due to debounce

      // Fast-forward debounce delay
      act(() => {
        vi.advanceTimersByTime(300);
      });

      expect(result.current.isSearching).toBe(true);

      // Wait for search to complete
      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.isSearching).toBe(false);
      expect(result.current.results).toHaveLength(2);
      expect(result.current.currentIndex).toBe(0);
      expect(mockSearchFunction).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'test query',
        })
      );

      vi.useRealTimers();
    });

    it('should handle empty search text', async () => {
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      await act(async () => {
        result.current.updateSearchText('');
        // Wait for any async operations to complete
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      expect(result.current.results).toEqual([]);
      expect(result.current.currentIndex).toBe(-1);
      expect(mockSearchFunction).not.toHaveBeenCalled();
    });

    it('should handle search errors', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.updateSearchText('error');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.error).toBe('Search failed');
      expect(result.current.results).toEqual([]);
      expect(result.current.isSearching).toBe(false);

      vi.useRealTimers();
    });
  });

  describe('Filter and Options Management', () => {
    it('should update filters and trigger search', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      // Set initial search text
      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      // Clear mock calls from initial search
      mockSearchFunction.mockClear();

      // Update filters
      act(() => {
        result.current.updateFilters({
          contentTypes: ['text', 'annotation'],
          authors: ['John Doe'],
        });
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockSearchFunction).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'test',
          filters: expect.objectContaining({
            contentTypes: ['text', 'annotation'],
            authors: ['John Doe'],
          }),
        })
      );

      vi.useRealTimers();
    });

    it('should update options and trigger search', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      mockSearchFunction.mockClear();

      act(() => {
        result.current.updateOptions({
          caseSensitive: true,
          sortBy: 'date',
          maxResults: 50,
        });
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockSearchFunction).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'test',
          options: expect.objectContaining({
            caseSensitive: true,
            sortBy: 'date',
            maxResults: 50,
          }),
        })
      );

      vi.useRealTimers();
    });
  });

  describe('Result Navigation', () => {
    it('should navigate through results', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.currentIndex).toBe(0);

      act(() => {
        result.current.navigateResults('next');
      });

      expect(result.current.currentIndex).toBe(1);

      act(() => {
        result.current.navigateResults('next');
      });

      expect(result.current.currentIndex).toBe(0); // Should wrap around

      act(() => {
        result.current.navigateResults('prev');
      });

      expect(result.current.currentIndex).toBe(1); // Should wrap around backwards

      vi.useRealTimers();
    });

    it('should handle navigation with no results', () => {
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.navigateResults('next');
      });

      expect(result.current.currentIndex).toBe(-1);

      act(() => {
        result.current.navigateResults('prev');
      });

      expect(result.current.currentIndex).toBe(-1);
    });
  });

  describe('Caching', () => {
    it('should cache search results', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => 
        useEnhancedSearch(mockSearchFunction, { enableCaching: true })
      );

      // First search
      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(mockSearchFunction).toHaveBeenCalledTimes(1);
      expect(result.current.cacheSize).toBe(1);

      // Same search should use cache
      act(() => {
        result.current.updateSearchText('');
      });

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      // Should still be called only once (cached result used)
      expect(mockSearchFunction).toHaveBeenCalledTimes(1);

      vi.useRealTimers();
    });

    it('should clear cache', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() =>
        useEnhancedSearch(mockSearchFunction, { enableCaching: true })
      );

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.cacheSize).toBe(1);

      act(() => {
        result.current.clearCache();
      });

      // Force a re-render to get updated cache size
      act(() => {
        // Trigger a state update to force re-render
        result.current.updateSearchText('test2');
      });

      act(() => {
        result.current.updateSearchText('');
      });

      expect(result.current.cacheSize).toBe(0);

      vi.useRealTimers();
    });
  });

  describe('Analytics', () => {
    it('should track search analytics', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => 
        useEnhancedSearch(mockSearchFunction, { enableAnalytics: true })
      );

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.analytics.totalSearches).toBe(1);
      expect(result.current.analytics.averageResultsPerSearch).toBe(2);
      expect(result.current.analytics.searchSuccessRate).toBe(1);
      expect(result.current.analytics.recentQueries).toHaveLength(1);
      expect(result.current.analytics.recentQueries[0].query).toBe('test');

      vi.useRealTimers();
    });

    it('should clear analytics', () => {
      const { result } = renderHook(() => 
        useEnhancedSearch(mockSearchFunction, { enableAnalytics: true })
      );

      act(() => {
        result.current.clearAnalytics();
      });

      expect(result.current.analytics.totalSearches).toBe(0);
      expect(result.current.analytics.recentQueries).toHaveLength(0);
      expect(result.current.metricsCount).toBe(0);
    });
  });

  describe('Clear Search', () => {
    it('should clear search state', async () => {
      vi.useFakeTimers();
      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction));

      act(() => {
        result.current.updateSearchText('test');
      });

      act(() => {
        vi.advanceTimersByTime(300);
      });

      await act(async () => {
        await vi.runAllTimersAsync();
      });

      expect(result.current.query.text).toBe('test');
      expect(result.current.results).toHaveLength(2);

      act(() => {
        result.current.clearSearch();
      });

      expect(result.current.query.text).toBe('');
      expect(result.current.results).toEqual([]);
      expect(result.current.currentIndex).toBe(-1);
      expect(result.current.error).toBeNull();

      vi.useRealTimers();
    });
  });

  describe('Configuration', () => {
    it('should respect configuration options', () => {
      const config = {
        enableSuggestions: false,
        enableAnalytics: false,
        enableCaching: false,
        maxResults: 25,
        debounceDelay: 500,
      };

      const { result } = renderHook(() => useEnhancedSearch(mockSearchFunction, config));

      expect(result.current.config.enableSuggestions).toBe(false);
      expect(result.current.config.enableAnalytics).toBe(false);
      expect(result.current.config.enableCaching).toBe(false);
      expect(result.current.config.maxResults).toBe(25);
      expect(result.current.config.debounceDelay).toBe(500);
    });
  });
});

describe('SearchSuggestionsEngine', () => {
  let engine: SearchSuggestionsEngine;

  beforeEach(() => {
    engine = new SearchSuggestionsEngine();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('Suggestion Generation', () => {
    it('should generate suggestions based on query', async () => {
      // Add some data to the engine first
      engine.recordSearch('test query', 5);
      engine.recordSearch('test document', 3);
      engine.indexDocumentTerms('doc-1', ['test', 'document', 'content']);

      const context = {
        recentSearches: ['test query', 'another search'],
        userPreferences: {
          preferredContentTypes: ['text'],
          frequentAuthors: <AUTHORS>
          commonTags: ['important'],
        },
        documentMetadata: {
          availableAuthors: <AUTHORS>
          availableTags: ['important', 'draft'],
          availableLanguages: ['en'],
          contentTypes: ['text', 'annotation'],
        },
      };

      const suggestions = await engine.generateSuggestions('test', context);

      expect(suggestions).toBeInstanceOf(Array);
      expect(suggestions.length).toBeGreaterThan(0);
      // Check that at least some suggestions contain 'test' (allowing for smart suggestions that might not)
      expect(suggestions.some(s => s.text.toLowerCase().includes('test'))).toBe(true);
    });

    it('should return default suggestions for empty query', async () => {
      const context = {
        recentSearches: ['recent search'],
        userPreferences: {
          preferredContentTypes: [],
          frequentAuthors: <AUTHORS>
          commonTags: [],
        },
        documentMetadata: {
          availableAuthors: <AUTHORS>
          availableTags: [],
          availableLanguages: [],
          contentTypes: [],
        },
      };

      const suggestions = await engine.generateSuggestions('', context);

      expect(suggestions).toBeInstanceOf(Array);
    });
  });

  describe('Search Recording', () => {
    it('should record search queries', () => {
      engine.recordSearch('test query', 5);
      engine.recordSearch('test query', 3);
      engine.recordSearch('another query', 2);

      const stats = engine.getStats();

      expect(stats.totalSearches).toBe(3);
      expect(stats.uniqueQueries).toBe(2);
      expect(stats.topQueries).toContainEqual(
        expect.objectContaining({ query: 'test query', count: 2 })
      );
      expect(stats.recentQueries).toContain('another query');
    });
  });

  describe('Document Indexing', () => {
    it('should index document terms', () => {
      engine.indexDocumentTerms('doc-1', ['important', 'document', 'terms']);
      engine.indexDocumentTerms('doc-2', ['another', 'document', 'content']);

      // Terms should be indexed for contextual suggestions
      expect(true).toBe(true); // Basic test that indexing doesn't throw
    });
  });

  describe('Data Management', () => {
    it('should clear all data', () => {
      engine.recordSearch('test', 1);
      engine.indexDocumentTerms('doc-1', ['term1', 'term2']);

      const statsBefore = engine.getStats();
      expect(statsBefore.totalSearches).toBeGreaterThan(0);

      engine.clearData();

      const statsAfter = engine.getStats();
      expect(statsAfter.totalSearches).toBe(0);
      expect(statsAfter.uniqueQueries).toBe(0);
    });
  });

  describe('Storage Integration', () => {
    it('should attempt to load from localStorage', () => {
      // Constructor should call loadFromStorage
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('search-suggestions-data');
    });

    it('should save to localStorage when recording searches', () => {
      engine.recordSearch('test', 1);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'search-suggestions-data',
        expect.any(String)
      );
    });
  });
});
