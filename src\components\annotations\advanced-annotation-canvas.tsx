"use client";

import React, { useRef, useCallback, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import type { AdvancedAnnotation, DrawingPath, AnnotationStyle } from '@/lib/annotations/annotation-engine';

interface AdvancedAnnotationCanvasProps {
  pageNumber: number;
  annotations: AdvancedAnnotation[];
  selectedTool: string;
  currentStyle: AnnotationStyle;
  scale: number;
  onAnnotationAdd: (annotation: Partial<AdvancedAnnotation>) => void;
  onAnnotationUpdate: (id: string, updates: Partial<AdvancedAnnotation>) => void;
  onAnnotationSelect: (annotation: AdvancedAnnotation | null) => void;
  selectedAnnotation?: AdvancedAnnotation | null;
  showGrid?: boolean;
  showRulers?: boolean;
  isReadOnly?: boolean;
  className?: string;
}

interface Point {
  x: number;
  y: number;
  pressure?: number;
  timestamp?: number;
}

export default function AdvancedAnnotationCanvas({
  pageNumber,
  annotations,
  selectedTool,
  currentStyle,
  scale,
  onAnnotationAdd,
  onAnnotationUpdate,
  onAnnotationSelect,
  selectedAnnotation,
  showGrid = false,
  showRulers = false,
  isReadOnly = false,
  className,
}: AdvancedAnnotationCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<Point[]>([]);
  const [startPoint, setStartPoint] = useState<Point | null>(null);
  const [dragOffset, setDragOffset] = useState<{ x: number; y: number } | null>(null);

  // Get relative position from mouse event
  const getRelativePosition = useCallback((e: React.MouseEvent): Point => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left) / scale,
      y: (e.clientY - rect.top) / scale,
      pressure: (e as any).pressure || 1,
      timestamp: Date.now(),
    };
  }, [scale]);

  // Handle mouse down
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isReadOnly) return;

    const point = getRelativePosition(e);
    setStartPoint(point);

    // Check if clicking on existing annotation
    const clickedAnnotation = annotations.find(annotation => {
      return (
        point.x >= annotation.x &&
        point.x <= annotation.x + (annotation.width || 0) &&
        point.y >= annotation.y &&
        point.y <= annotation.y + (annotation.height || 0)
      );
    });

    if (clickedAnnotation) {
      onAnnotationSelect(clickedAnnotation);
      if (selectedTool === 'select') {
        setDragOffset({
          x: point.x - clickedAnnotation.x,
          y: point.y - clickedAnnotation.y,
        });
      }
      return;
    }

    // Deselect if clicking empty space
    if (selectedTool === 'select') {
      onAnnotationSelect(null);
      return;
    }

    // Start drawing/creating annotation
    if (['pen', 'pencil', 'brush', 'marker'].includes(selectedTool)) {
      setIsDrawing(true);
      setCurrentPath([point]);
    } else if (['rectangle', 'circle', 'line', 'arrow'].includes(selectedTool)) {
      setIsDrawing(true);
    } else if (['text', 'note'].includes(selectedTool)) {
      // Create text/note annotation immediately
      const content = selectedTool === 'note' ? 'New note' : 'Text annotation';
      onAnnotationAdd({
        type: selectedTool as any,
        pageNumber,
        x: point.x,
        y: point.y,
        width: selectedTool === 'text' ? 200 : 20,
        height: selectedTool === 'text' ? 30 : 20,
        content,
        color: currentStyle.stroke?.color || '#000000',
        author: 'Current User',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }, [
    isReadOnly,
    getRelativePosition,
    annotations,
    onAnnotationSelect,
    selectedTool,
    pageNumber,
    currentStyle,
    onAnnotationAdd,
  ]);

  // Handle mouse move
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isReadOnly || !isDrawing || !startPoint) return;

    const point = getRelativePosition(e);

    // Handle dragging selected annotation
    if (selectedAnnotation && dragOffset && selectedTool === 'select') {
      onAnnotationUpdate(selectedAnnotation.id, {
        x: point.x - dragOffset.x,
        y: point.y - dragOffset.y,
      });
      return;
    }

    // Handle drawing tools
    if (['pen', 'pencil', 'brush', 'marker'].includes(selectedTool)) {
      setCurrentPath(prev => [...prev, point]);
    }

    // Redraw canvas for preview
    redrawCanvas();
  }, [
    isReadOnly,
    isDrawing,
    startPoint,
    selectedAnnotation,
    dragOffset,
    selectedTool,
    getRelativePosition,
    onAnnotationUpdate,
  ]);

  // Handle mouse up
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    if (isReadOnly || !isDrawing || !startPoint) return;

    const point = getRelativePosition(e);
    setIsDrawing(false);
    setDragOffset(null);

    // Create annotation based on tool
    if (['pen', 'pencil', 'brush', 'marker'].includes(selectedTool) && currentPath.length > 1) {
      onAnnotationAdd({
        type: 'freehand',
        pageNumber,
        x: Math.min(...currentPath.map(p => p.x)),
        y: Math.min(...currentPath.map(p => p.y)),
        points: currentPath.map(p => ({ x: p.x, y: p.y })),
        color: currentStyle.stroke?.color || '#000000',
        strokeWidth: currentStyle.stroke?.width || 2,
        opacity: currentStyle.effects?.opacity || 1,
        author: 'Current User',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    } else if (['rectangle', 'circle', 'line', 'arrow'].includes(selectedTool)) {
      const width = Math.abs(point.x - startPoint.x);
      const height = Math.abs(point.y - startPoint.y);

      if (width > 5 || height > 5) {
        onAnnotationAdd({
          type: selectedTool as any,
          pageNumber,
          x: Math.min(startPoint.x, point.x),
          y: Math.min(startPoint.y, point.y),
          width,
          height,
          color: currentStyle.stroke?.color || '#000000',
          strokeColor: currentStyle.stroke?.color,
          strokeWidth: currentStyle.stroke?.width || 2,
          fillColor: currentStyle.fill?.color,
          opacity: currentStyle.effects?.opacity || 1,
          author: 'Current User',
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    }

    setCurrentPath([]);
    setStartPoint(null);
  }, [
    isReadOnly,
    isDrawing,
    startPoint,
    getRelativePosition,
    selectedTool,
    currentPath,
    pageNumber,
    currentStyle,
    onAnnotationAdd,
  ]);

  // Redraw canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw grid if enabled
    if (showGrid) {
      drawGrid(ctx, canvas.width, canvas.height);
    }

    // Draw current path preview
    if (isDrawing && currentPath.length > 1) {
      drawPath(ctx, currentPath, currentStyle);
    }

    // Draw shape preview
    if (isDrawing && startPoint && ['rectangle', 'circle', 'line', 'arrow'].includes(selectedTool)) {
      // This would be handled by mouse move events
    }
  }, [showGrid, isDrawing, currentPath, currentStyle, startPoint, selectedTool]);

  // Draw grid
  const drawGrid = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const gridSize = 20 * scale;
    
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 0.5;
    ctx.setLineDash([]);

    // Vertical lines
    for (let x = 0; x <= width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    // Horizontal lines
    for (let y = 0; y <= height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  }, [scale]);

  // Draw path
  const drawPath = useCallback((ctx: CanvasRenderingContext2D, path: Point[], style: AnnotationStyle) => {
    if (path.length < 2) return;

    ctx.strokeStyle = style.stroke?.color || '#000000';
    ctx.lineWidth = (style.stroke?.width || 2) * scale;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.globalAlpha = style.effects?.opacity || 1;

    ctx.beginPath();
    ctx.moveTo(path[0].x * scale, path[0].y * scale);

    for (let i = 1; i < path.length; i++) {
      ctx.lineTo(path[i].x * scale, path[i].y * scale);
    }

    ctx.stroke();
    ctx.globalAlpha = 1;
  }, [scale]);

  // Render annotation on overlay
  const renderAnnotation = useCallback((annotation: AdvancedAnnotation) => {
    const style: React.CSSProperties = {
      position: 'absolute',
      left: `${annotation.x * scale}px`,
      top: `${annotation.y * scale}px`,
      pointerEvents: 'none',
      zIndex: annotation.zIndex || 0,
    };

    const isSelected = selectedAnnotation?.id === annotation.id;

    switch (annotation.type) {
      case 'highlight':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 100) * scale}px`,
              height: `${(annotation.height || 20) * scale}px`,
              backgroundColor: annotation.color,
              opacity: 0.3,
              borderRadius: '2px',
              border: isSelected ? '2px solid #3b82f6' : 'none',
            }}
          />
        );

      case 'rectangle':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: `${(annotation.height || 50) * scale}px`,
              border: `${(annotation.strokeWidth || 2) * scale}px solid ${annotation.color}`,
              backgroundColor: annotation.fillColor || 'transparent',
              opacity: annotation.opacity || 1,
              borderRadius: annotation.borderRadius ? `${annotation.borderRadius * scale}px` : '0',
              boxShadow: annotation.shadow ? 
                `${annotation.shadow.offsetX * scale}px ${annotation.shadow.offsetY * scale}px ${annotation.shadow.blur * scale}px ${annotation.shadow.color}` : 
                isSelected ? '0 0 0 2px #3b82f6' : 'none',
            }}
          />
        );

      case 'circle':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: `${(annotation.height || 50) * scale}px`,
              border: `${(annotation.strokeWidth || 2) * scale}px solid ${annotation.color}`,
              backgroundColor: annotation.fillColor || 'transparent',
              borderRadius: '50%',
              opacity: annotation.opacity || 1,
              boxShadow: annotation.shadow ? 
                `${annotation.shadow.offsetX * scale}px ${annotation.shadow.offsetY * scale}px ${annotation.shadow.blur * scale}px ${annotation.shadow.color}` : 
                isSelected ? '0 0 0 2px #3b82f6' : 'none',
            }}
          />
        );

      case 'text':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              minWidth: `${(annotation.width || 200) * scale}px`,
              minHeight: `${(annotation.height || 30) * scale}px`,
              backgroundColor: annotation.fillColor || 'rgba(255, 255, 255, 0.9)',
              border: `1px solid ${annotation.color}`,
              borderRadius: '4px',
              padding: `${4 * scale}px`,
              fontSize: `${(annotation.fontSize || 12) * scale}px`,
              fontFamily: annotation.fontFamily || 'inherit',
              color: annotation.color,
              boxShadow: annotation.shadow ? 
                `${annotation.shadow.offsetX * scale}px ${annotation.shadow.offsetY * scale}px ${annotation.shadow.blur * scale}px ${annotation.shadow.color}` : 
                isSelected ? '0 0 0 2px #3b82f6' : '0 2px 4px rgba(0,0,0,0.1)',
              opacity: annotation.opacity || 1,
            }}
          >
            {annotation.content}
          </div>
        );

      case 'stamp':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 120) * scale}px`,
              height: `${(annotation.height || 60) * scale}px`,
              backgroundColor: annotation.color + '20',
              border: `2px solid ${annotation.color}`,
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: `${12 * scale}px`,
              fontWeight: 'bold',
              color: annotation.color,
              textAlign: 'center',
              opacity: annotation.opacity || 1,
              boxShadow: isSelected ? '0 0 0 2px #3b82f6' : '0 2px 4px rgba(0,0,0,0.2)',
            }}
          >
            {annotation.content}
          </div>
        );

      case 'signature':
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 200) * scale}px`,
              height: `${(annotation.height || 60) * scale}px`,
              border: '1px solid #ccc',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
              opacity: annotation.opacity || 1,
              boxShadow: isSelected ? '0 0 0 2px #3b82f6' : '0 2px 4px rgba(0,0,0,0.1)',
            }}
          >
            {annotation.signature?.type === 'drawn' || annotation.signature?.type === 'image' ? (
              <img
                src={annotation.signature.data || annotation.imageUrl}
                alt="Signature"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <span
                style={{
                  fontFamily: annotation.fontFamily || 'cursive',
                  fontSize: `${(annotation.fontSize || 18) * scale}px`,
                  color: annotation.color || '#000',
                }}
              >
                {annotation.content || annotation.signature?.data}
              </span>
            )}
          </div>
        );

      case 'freehand':
        // Freehand drawings are rendered on canvas, not as DOM elements
        return null;

      default:
        return (
          <div
            key={annotation.id}
            style={{
              ...style,
              width: `${(annotation.width || 50) * scale}px`,
              height: `${(annotation.height || 50) * scale}px`,
              backgroundColor: annotation.color + '40',
              border: `1px solid ${annotation.color}`,
              borderRadius: '2px',
              opacity: annotation.opacity || 1,
              boxShadow: isSelected ? '0 0 0 2px #3b82f6' : 'none',
            }}
          />
        );
    }
  }, [scale, selectedAnnotation]);

  // Update canvas size and redraw when scale changes
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    redrawCanvas();
  }, [scale, redrawCanvas]);

  return (
    <div className={cn("relative w-full h-full", className)}>
      {/* Rulers */}
      {showRulers && (
        <>
          {/* Horizontal ruler */}
          <div className="absolute top-0 left-6 right-0 h-6 bg-gray-100 border-b border-gray-300 z-10">
            {/* Ruler marks would go here */}
          </div>
          {/* Vertical ruler */}
          <div className="absolute top-6 left-0 bottom-0 w-6 bg-gray-100 border-r border-gray-300 z-10">
            {/* Ruler marks would go here */}
          </div>
        </>
      )}

      {/* Drawing canvas */}
      <canvas
        ref={canvasRef}
        className={cn(
          "absolute inset-0 w-full h-full",
          showRulers && "top-6 left-6",
          selectedTool === 'select' ? 'cursor-default' : 'cursor-crosshair'
        )}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      />

      {/* Annotation overlay */}
      <div
        ref={overlayRef}
        className={cn(
          "absolute inset-0 w-full h-full pointer-events-none",
          showRulers && "top-6 left-6"
        )}
      >
        {annotations
          .filter(annotation => annotation.pageNumber === pageNumber)
          .map(renderAnnotation)}
      </div>
    </div>
  );
}
