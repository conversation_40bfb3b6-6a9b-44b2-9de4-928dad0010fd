/**
 * Mobile Optimization Utilities
 * Collection of utility functions for mobile device detection, optimization, and enhancement
 */

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  hasHover: boolean;
  hasPointer: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: { width: number; height: number };
  devicePixelRatio: number;
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';
  browser: 'safari' | 'chrome' | 'firefox' | 'edge' | 'samsung' | 'unknown';
  version: string;
}

export interface PerformanceMetrics {
  memory: number | null;
  connection: 'slow-2g' | '2g' | '3g' | '4g' | 'unknown';
  batteryLevel: number | null;
  isLowPowerMode: boolean;
  deviceMemory: number | null;
  hardwareConcurrency: number;
}

/**
 * Comprehensive device detection
 */
export function getDeviceInfo(): DeviceInfo {
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouch: false,
      hasHover: true,
      hasPointer: true,
      orientation: 'landscape',
      screenSize: { width: 1024, height: 768 },
      devicePixelRatio: 1,
      platform: 'unknown',
      browser: 'unknown',
      version: '',
    };
  }

  const userAgent = navigator.userAgent;
  const width = window.innerWidth;
  const height = window.innerHeight;

  // Device type detection
  const isMobile = width < 768;
  const isTablet = width >= 768 && width < 1024;
  const isDesktop = width >= 1024;

  // Touch capability
  const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const hasHover = window.matchMedia('(hover: hover)').matches;
  const hasPointer = window.matchMedia('(pointer: fine)').matches;

  // Orientation
  const orientation = height > width ? 'portrait' : 'landscape';

  // Platform detection
  let platform: DeviceInfo['platform'] = 'unknown';
  if (/iPad|iPhone|iPod/.test(userAgent)) platform = 'ios';
  else if (/Android/.test(userAgent)) platform = 'android';
  else if (/Windows/.test(userAgent)) platform = 'windows';
  else if (/Mac/.test(userAgent)) platform = 'macos';
  else if (/Linux/.test(userAgent)) platform = 'linux';

  // Browser detection
  let browser: DeviceInfo['browser'] = 'unknown';
  let version = '';
  if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    browser = 'safari';
    version = userAgent.match(/Version\/([\d.]+)/)?.[1] || '';
  } else if (/Chrome/.test(userAgent)) {
    if (/SamsungBrowser/.test(userAgent)) {
      browser = 'samsung';
      version = userAgent.match(/SamsungBrowser\/([\d.]+)/)?.[1] || '';
    } else {
      browser = 'chrome';
      version = userAgent.match(/Chrome\/([\d.]+)/)?.[1] || '';
    }
  } else if (/Firefox/.test(userAgent)) {
    browser = 'firefox';
    version = userAgent.match(/Firefox\/([\d.]+)/)?.[1] || '';
  } else if (/Edge/.test(userAgent)) {
    browser = 'edge';
    version = userAgent.match(/Edge\/([\d.]+)/)?.[1] || '';
  }

  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
    hasHover,
    hasPointer,
    orientation,
    screenSize: { width, height },
    devicePixelRatio: window.devicePixelRatio || 1,
    platform,
    browser,
    version,
  };
}

/**
 * Get performance metrics for optimization decisions
 */
export function getPerformanceMetrics(): PerformanceMetrics {
  const metrics: PerformanceMetrics = {
    memory: null,
    connection: 'unknown',
    batteryLevel: null,
    isLowPowerMode: false,
    deviceMemory: null,
    hardwareConcurrency: navigator.hardwareConcurrency || 4,
  };

  if (typeof window === 'undefined') return metrics;

  // Memory information
  if ('memory' in performance) {
    metrics.memory = (performance as any).memory?.usedJSHeapSize || null;
  }

  // Device memory
  if ('deviceMemory' in navigator) {
    metrics.deviceMemory = (navigator as any).deviceMemory || null;
  }

  // Network connection
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    if (connection?.effectiveType) {
      metrics.connection = connection.effectiveType;
    }
  }

  // Battery information
  if ('getBattery' in navigator) {
    (navigator as any).getBattery().then((battery: any) => {
      metrics.batteryLevel = battery.level;
      metrics.isLowPowerMode = battery.level < 0.2;
    }).catch(() => {
      // Battery API not available
    });
  }

  return metrics;
}

/**
 * Check if device supports specific features
 */
export function getFeatureSupport() {
  if (typeof window === 'undefined') {
    return {
      vibration: false,
      fullscreen: false,
      orientation: false,
      deviceMotion: false,
      webgl: false,
      webassembly: false,
      serviceWorker: false,
      pushNotifications: false,
    };
  }

  return {
    vibration: 'vibrate' in navigator,
    fullscreen: 'requestFullscreen' in document.documentElement,
    orientation: 'orientation' in screen,
    deviceMotion: 'DeviceMotionEvent' in window,
    webgl: !!document.createElement('canvas').getContext('webgl'),
    webassembly: 'WebAssembly' in window,
    serviceWorker: 'serviceWorker' in navigator,
    pushNotifications: 'PushManager' in window,
  };
}

/**
 * Optimize touch targets for accessibility
 */
export function optimizeTouchTargets(element: HTMLElement, minSize = 44) {
  const elements = element.querySelectorAll('button, a, input, [role="button"]');
  
  elements.forEach((el) => {
    const htmlEl = el as HTMLElement;
    const rect = htmlEl.getBoundingClientRect();
    
    if (rect.width < minSize || rect.height < minSize) {
      htmlEl.style.minWidth = `${minSize}px`;
      htmlEl.style.minHeight = `${minSize}px`;
      htmlEl.style.display = 'inline-flex';
      htmlEl.style.alignItems = 'center';
      htmlEl.style.justifyContent = 'center';
    }
  });
}

/**
 * Apply mobile-specific optimizations
 */
export function applyMobileOptimizations(element: HTMLElement) {
  const deviceInfo = getDeviceInfo();
  
  if (deviceInfo.isMobile) {
    // Prevent zoom on input focus (iOS)
    const inputs = element.querySelectorAll('input, textarea, select');
    inputs.forEach((input) => {
      const htmlInput = input as HTMLInputElement;
      if (htmlInput.style.fontSize === '' || parseFloat(htmlInput.style.fontSize) < 16) {
        htmlInput.style.fontSize = '16px';
      }
    });

    // Add touch-friendly classes
    element.classList.add('mobile-optimized');
    
    // Optimize touch targets
    optimizeTouchTargets(element);
    
    // Prevent text selection on UI elements
    const uiElements = element.querySelectorAll('button, .ui-element, [role="button"]');
    uiElements.forEach((el) => {
      (el as HTMLElement).style.userSelect = 'none';
      (el as HTMLElement).style.webkitUserSelect = 'none';
    });
  }

  if (deviceInfo.isTouch) {
    // Add touch-specific optimizations
    element.classList.add('touch-device');
    
    // Disable tap highlight
    element.style.webkitTapHighlightColor = 'transparent';
    
    // Enable momentum scrolling
    const scrollableElements = element.querySelectorAll('.scrollable, [data-scrollable]');
    scrollableElements.forEach((el) => {
      (el as HTMLElement).style.webkitOverflowScrolling = 'touch';
      (el as HTMLElement).style.overscrollBehavior = 'contain';
    });
  }
}

/**
 * Trigger haptic feedback if available
 */
export function triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' = 'light') {
  if (typeof navigator === 'undefined' || !('vibrate' in navigator)) return;

  const patterns = {
    light: [10],
    medium: [20],
    heavy: [30],
  };

  navigator.vibrate(patterns[type]);
}

/**
 * Check if device is in low power mode
 */
export function isLowPowerMode(): Promise<boolean> {
  return new Promise((resolve) => {
    if (typeof navigator === 'undefined' || !('getBattery' in navigator)) {
      resolve(false);
      return;
    }

    (navigator as any).getBattery()
      .then((battery: any) => {
        resolve(battery.level < 0.2);
      })
      .catch(() => resolve(false));
  });
}

/**
 * Get safe area insets for modern devices
 */
export function getSafeAreaInsets() {
  if (typeof window === 'undefined') {
    return { top: 0, right: 0, bottom: 0, left: 0 };
  }

  const style = getComputedStyle(document.documentElement);
  
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0'),
  };
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Check if element is in viewport
 */
export function isInViewport(element: HTMLElement, threshold = 0): boolean {
  if (typeof window === 'undefined') return false;

  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;

  return (
    rect.top >= -threshold &&
    rect.left >= -threshold &&
    rect.bottom <= windowHeight + threshold &&
    rect.right <= windowWidth + threshold
  );
}

/**
 * Mobile-optimized event listener with passive support
 */
export function addMobileEventListener(
  element: HTMLElement,
  event: string,
  handler: EventListener,
  options: { passive?: boolean; capture?: boolean } = {}
) {
  const deviceInfo = getDeviceInfo();
  
  // Use passive listeners for touch events on mobile for better performance
  if (deviceInfo.isMobile && ['touchstart', 'touchmove', 'wheel'].includes(event)) {
    options.passive = options.passive !== false;
  }

  element.addEventListener(event, handler, options);
  
  return () => element.removeEventListener(event, handler, options);
}

/**
 * Export all utilities as a single object for convenience
 */
export const MobileUtils = {
  getDeviceInfo,
  getPerformanceMetrics,
  getFeatureSupport,
  optimizeTouchTargets,
  applyMobileOptimizations,
  triggerHapticFeedback,
  isLowPowerMode,
  getSafeAreaInsets,
  debounce,
  throttle,
  isInViewport,
  addMobileEventListener,
};
