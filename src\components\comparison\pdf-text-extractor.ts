import { pdfjs } from 'react-pdf';

export interface TextItem {
  str: string;
  dir: string;
  width: number;
  height: number;
  transform: number[];
  fontName: string;
  hasEOL: boolean;
}

export interface ExtractedText {
  pageNumber: number;
  text: string;
  items: TextItem[];
  lines: string[];
  paragraphs: string[];
}

export interface TextPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  pageNumber: number;
}

export class PDFTextExtractor {
  private static instance: PDFTextExtractor;

  static getInstance(): PDFTextExtractor {
    if (!PDFTextExtractor.instance) {
      PDFTextExtractor.instance = new PDFTextExtractor();
    }
    return PDFTextExtractor.instance;
  }

  /**
   * Extract text from a specific page of a PDF document
   */
  async extractTextFromPage(
    pdfDocument: any,
    pageNumber: number,
    options: {
      includePositions?: boolean;
      normalizeWhitespace?: boolean;
      combineTextItems?: boolean;
    } = {}
  ): Promise<ExtractedText> {
    try {
      const page = await pdfDocument.getPage(pageNumber);
      const textContent = await page.getTextContent();
      const viewport = page.getViewport({ scale: 1.0 });

      const items: TextItem[] = textContent.items.map((item: any) => ({
        str: item.str,
        dir: item.dir,
        width: item.width,
        height: item.height,
        transform: item.transform,
        fontName: item.fontName,
        hasEOL: item.hasEOL
      }));

      // Extract plain text
      let text = items.map(item => item.str).join('');
      
      if (options.normalizeWhitespace) {
        text = text.replace(/\s+/g, ' ').trim();
      }

      // Group text into lines
      const lines = this.groupTextIntoLines(items, viewport);
      
      // Group lines into paragraphs
      const paragraphs = this.groupLinesIntoParagraphs(lines);

      return {
        pageNumber,
        text,
        items,
        lines,
        paragraphs
      };
    } catch (error) {
      console.error(`Error extracting text from page ${pageNumber}:`, error);
      return {
        pageNumber,
        text: '',
        items: [],
        lines: [],
        paragraphs: []
      };
    }
  }

  /**
   * Extract text from all pages of a PDF document
   */
  async extractTextFromDocument(
    pdfDocument: any,
    options: {
      includePositions?: boolean;
      normalizeWhitespace?: boolean;
      combineTextItems?: boolean;
      progressCallback?: (progress: number) => void;
    } = {}
  ): Promise<ExtractedText[]> {
    const numPages = pdfDocument.numPages;
    const results: ExtractedText[] = [];

    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const extractedText = await this.extractTextFromPage(pdfDocument, pageNum, options);
      results.push(extractedText);

      if (options.progressCallback) {
        options.progressCallback((pageNum / numPages) * 100);
      }
    }

    return results;
  }

  /**
   * Find text positions for search highlighting
   */
  findTextPositions(
    extractedText: ExtractedText,
    searchText: string,
    options: {
      caseSensitive?: boolean;
      wholeWords?: boolean;
      useRegex?: boolean;
    } = {}
  ): TextPosition[] {
    const positions: TextPosition[] = [];
    const { caseSensitive = false, wholeWords = false, useRegex = false } = options;

    let searchRegex: RegExp;
    
    if (useRegex) {
      try {
        searchRegex = new RegExp(searchText, caseSensitive ? 'g' : 'gi');
      } catch {
        return positions; // Invalid regex
      }
    } else if (wholeWords) {
      const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      searchRegex = new RegExp(`\\b${escapedText}\\b`, caseSensitive ? 'g' : 'gi');
    } else {
      const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      searchRegex = new RegExp(escapedText, caseSensitive ? 'g' : 'gi');
    }

    // Search through text items and calculate positions
    let currentIndex = 0;
    for (const item of extractedText.items) {
      const itemText = item.str;
      const matches = Array.from(itemText.matchAll(searchRegex));
      
      for (const match of matches) {
        if (match.index !== undefined) {
          const startIndex = match.index;
          const endIndex = startIndex + match[0].length;
          
          // Calculate position based on transform matrix
          const [a, b, c, d, e, f] = item.transform;
          const x = e;
          const y = f;
          
          // Estimate width based on character position
          const charWidth = item.width / itemText.length;
          const matchWidth = charWidth * match[0].length;
          
          positions.push({
            x,
            y,
            width: matchWidth,
            height: item.height,
            pageNumber: extractedText.pageNumber
          });
        }
      }
      
      currentIndex += itemText.length;
    }

    return positions;
  }

  /**
   * Group text items into lines based on vertical position
   */
  private groupTextIntoLines(items: TextItem[], viewport: any): string[] {
    const lines: { y: number; text: string }[] = [];
    const tolerance = 2; // Vertical tolerance for grouping items into lines

    for (const item of items) {
      const [, , , , , y] = item.transform;
      
      // Find existing line or create new one
      let line = lines.find(l => Math.abs(l.y - y) <= tolerance);
      if (!line) {
        line = { y, text: '' };
        lines.push(line);
      }
      
      line.text += item.str;
    }

    // Sort lines by vertical position (top to bottom)
    lines.sort((a, b) => b.y - a.y);
    
    return lines.map(line => line.text.trim()).filter(text => text.length > 0);
  }

  /**
   * Group lines into paragraphs based on spacing
   */
  private groupLinesIntoParagraphs(lines: string[]): string[] {
    const paragraphs: string[] = [];
    let currentParagraph = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (currentParagraph === '') {
        currentParagraph = line;
      } else {
        // Simple heuristic: if line starts with capital letter or is very short,
        // it might be a new paragraph
        if (line.length > 0 && (
          /^[A-Z]/.test(line) && currentParagraph.endsWith('.') ||
          line.length < 10
        )) {
          paragraphs.push(currentParagraph.trim());
          currentParagraph = line;
        } else {
          currentParagraph += ' ' + line;
        }
      }
    }

    if (currentParagraph.trim()) {
      paragraphs.push(currentParagraph.trim());
    }

    return paragraphs;
  }

  /**
   * Compare two extracted texts and return similarity score
   */
  calculateSimilarity(text1: ExtractedText, text2: ExtractedText): number {
    const str1 = text1.text.toLowerCase().replace(/\s+/g, ' ').trim();
    const str2 = text2.text.toLowerCase().replace(/\s+/g, ' ').trim();

    if (str1 === str2) return 1.0;
    if (str1.length === 0 && str2.length === 0) return 1.0;
    if (str1.length === 0 || str2.length === 0) return 0.0;

    // Use Levenshtein distance for similarity calculation
    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    
    return 1 - (distance / maxLength);
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}

export const pdfTextExtractor = PDFTextExtractor.getInstance();
