"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  MessageSquare, 
  Bookmark, 
  FormInput, 
  Database,
  Eye,
  Download,
  Share,
  Star,
  Clock,
  User,
  Tag,
  Hash,
  ChevronRight,
  ChevronDown,
  Search,
  Zap,
  Target,
  TrendingUp,
  Filter,
  Grid,
  List,
  Map
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { SearchResult } from './advanced-search-interface';

interface SearchResultsDisplayProps {
  results: SearchResult[];
  query: string;
  onResultSelect: (result: SearchResult) => void;
  onResultBookmark: (result: SearchResult) => void;
  onResultShare: (result: SearchResult) => void;
  onResultDownload?: (result: SearchResult) => void;
  groupBy?: 'none' | 'document' | 'page' | 'type' | 'author' | 'date';
  viewMode?: 'list' | 'grid' | 'compact';
  showPreviews?: boolean;
  className?: string;
}

interface GroupedResults {
  [key: string]: {
    results: SearchResult[];
    metadata: {
      title: string;
      count: number;
      icon: React.ComponentType<{ className?: string }>;
      description?: string;
    };
  };
}

const getContentTypeIcon = (type: string) => {
  switch (type) {
    case 'text': return FileText;
    case 'annotation': return MessageSquare;
    case 'bookmark': return Bookmark;
    case 'form': return FormInput;
    case 'metadata': return Database;
    case 'ocr': return Search;
    default: return FileText;
  }
};

const getRelevanceColor = (score: number) => {
  if (score >= 0.8) return 'bg-green-500';
  if (score >= 0.6) return 'bg-yellow-500';
  if (score >= 0.4) return 'bg-orange-500';
  return 'bg-red-500';
};

const highlightText = (text: string, query: string): string => {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>');
};

export default function SearchResultsDisplay({
  results,
  query,
  onResultSelect,
  onResultBookmark,
  onResultShare,
  onResultDownload,
  groupBy = 'none',
  viewMode = 'list',
  showPreviews = true,
  className,
}: SearchResultsDisplayProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [selectedResult, setSelectedResult] = useState<string | null>(null);

  // Group results based on groupBy option
  const groupedResults = useMemo((): GroupedResults => {
    if (groupBy === 'none') {
      return {
        all: {
          results,
          metadata: {
            title: 'All Results',
            count: results.length,
            icon: Search,
            description: `${results.length} search results`,
          },
        },
      };
    }

    const groups: GroupedResults = {};

    results.forEach((result) => {
      let groupKey: string;
      let groupTitle: string;
      let groupIcon: React.ComponentType<{ className?: string }>;
      let groupDescription: string;

      switch (groupBy) {
        case 'document':
          groupKey = result.documentId;
          groupTitle = result.title || `Document ${result.documentId}`;
          groupIcon = FileText;
          groupDescription = `Results from ${result.title}`;
          break;
        case 'page':
          groupKey = `page-${result.pageNumber}`;
          groupTitle = `Page ${result.pageNumber}`;
          groupIcon = Hash;
          groupDescription = `Results from page ${result.pageNumber}`;
          break;
        case 'type':
          groupKey = result.type;
          groupTitle = result.type.charAt(0).toUpperCase() + result.type.slice(1);
          groupIcon = getContentTypeIcon(result.type);
          groupDescription = `${result.type} content`;
          break;
        case 'author':
          groupKey = result.author || 'unknown';
          groupTitle = result.author || 'Unknown Author';
          groupIcon = User;
          groupDescription = `Results by ${result.author || 'unknown author'}`;
          break;
        case 'date':
          const date = result.createdDate || result.modifiedDate;
          if (date) {
            const dateKey = date.toISOString().split('T')[0];
            groupKey = dateKey;
            groupTitle = date.toLocaleDateString();
            groupIcon = Clock;
            groupDescription = `Results from ${date.toLocaleDateString()}`;
          } else {
            groupKey = 'no-date';
            groupTitle = 'No Date';
            groupIcon = Clock;
            groupDescription = 'Results without date information';
          }
          break;
        default:
          groupKey = 'all';
          groupTitle = 'All Results';
          groupIcon = Search;
          groupDescription = 'All search results';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = {
          results: [],
          metadata: {
            title: groupTitle,
            count: 0,
            icon: groupIcon,
            description: groupDescription,
          },
        };
      }

      groups[groupKey].results.push(result);
      groups[groupKey].metadata.count = groups[groupKey].results.length;
    });

    return groups;
  }, [results, groupBy]);

  // Toggle group expansion
  const toggleGroup = useCallback((groupKey: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupKey)) {
        newSet.delete(groupKey);
      } else {
        newSet.add(groupKey);
      }
      return newSet;
    });
  }, []);

  // Handle result selection
  const handleResultSelect = useCallback((result: SearchResult) => {
    setSelectedResult(result.id);
    onResultSelect(result);
  }, [onResultSelect]);

  // Render individual result
  const renderResult = useCallback((result: SearchResult, index: number) => {
    const ContentTypeIcon = getContentTypeIcon(result.type);
    const isSelected = selectedResult === result.id;

    if (viewMode === 'compact') {
      return (
        <div
          key={result.id}
          className={cn(
            "flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors",
            isSelected ? "bg-primary/10 border border-primary/20" : "hover:bg-muted"
          )}
          onClick={() => handleResultSelect(result)}
        >
          <ContentTypeIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium truncate">{result.title}</span>
              <Badge variant="outline" className="text-xs">
                Page {result.pageNumber}
              </Badge>
            </div>
            <div 
              className="text-xs text-muted-foreground truncate"
              dangerouslySetInnerHTML={{ 
                __html: highlightText(result.content.slice(0, 100) + '...', query) 
              }}
            />
          </div>
          <div className="flex items-center gap-1">
            <div 
              className={cn("w-2 h-2 rounded-full", getRelevanceColor(result.relevanceScore))}
              title={`Relevance: ${Math.round(result.relevanceScore * 100)}%`}
            />
            <span className="text-xs text-muted-foreground">
              {Math.round(result.relevanceScore * 100)}%
            </span>
          </div>
        </div>
      );
    }

    if (viewMode === 'grid') {
      return (
        <Card
          key={result.id}
          className={cn(
            "cursor-pointer transition-all hover:shadow-md",
            isSelected ? "ring-2 ring-primary" : ""
          )}
          onClick={() => handleResultSelect(result)}
        >
          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <ContentTypeIcon className="h-4 w-4 text-muted-foreground" />
                <Badge variant="outline" className="text-xs">
                  Page {result.pageNumber}
                </Badge>
              </div>
              <div className="flex items-center gap-1">
                <div 
                  className={cn("w-2 h-2 rounded-full", getRelevanceColor(result.relevanceScore))}
                />
                <span className="text-xs text-muted-foreground">
                  {Math.round(result.relevanceScore * 100)}%
                </span>
              </div>
            </div>
            <CardTitle className="text-sm line-clamp-2">{result.title}</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div 
              className="text-xs text-muted-foreground line-clamp-3"
              dangerouslySetInnerHTML={{ 
                __html: highlightText(result.content, query) 
              }}
            />
            {result.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {result.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {result.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{result.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      );
    }

    // List view (default)
    return (
      <Card
        key={result.id}
        className={cn(
          "cursor-pointer transition-all hover:shadow-sm",
          isSelected ? "ring-2 ring-primary" : ""
        )}
        onClick={() => handleResultSelect(result)}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <ContentTypeIcon className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
            
            <div className="flex-1 min-w-0 space-y-2">
              {/* Header */}
              <div className="flex items-center justify-between gap-2">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-sm truncate">{result.title}</h3>
                  <Badge variant="outline" className="text-xs">
                    Page {result.pageNumber}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {result.type}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    <div 
                      className={cn("w-2 h-2 rounded-full", getRelevanceColor(result.relevanceScore))}
                      title={`Relevance: ${Math.round(result.relevanceScore * 100)}%`}
                    />
                    <span className="text-xs text-muted-foreground">
                      {Math.round(result.relevanceScore * 100)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div 
                className="text-sm text-muted-foreground line-clamp-2"
                dangerouslySetInnerHTML={{ 
                  __html: highlightText(result.content, query) 
                }}
              />

              {/* Context */}
              {result.context && result.context !== result.content && (
                <div 
                  className="text-xs text-muted-foreground/80 line-clamp-1"
                  dangerouslySetInnerHTML={{ 
                    __html: highlightText(result.context, query) 
                  }}
                />
              )}

              {/* Metadata */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  {result.author && (
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {result.author}
                    </div>
                  )}
                  {result.createdDate && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {result.createdDate.toLocaleDateString()}
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onResultBookmark(result);
                    }}
                    className="h-6 w-6 p-0"
                  >
                    <Bookmark className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onResultShare(result);
                    }}
                    className="h-6 w-6 p-0"
                  >
                    <Share className="h-3 w-3" />
                  </Button>
                  {onResultDownload && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onResultDownload(result);
                      }}
                      className="h-6 w-6 p-0"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Tags */}
              {result.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {result.tags.slice(0, 5).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      <Tag className="h-2 w-2 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                  {result.tags.length > 5 && (
                    <Badge variant="outline" className="text-xs">
                      +{result.tags.length - 5} more
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }, [viewMode, selectedResult, query, handleResultSelect, onResultBookmark, onResultShare, onResultDownload]);

  // Render group
  const renderGroup = useCallback((groupKey: string, group: GroupedResults[string]) => {
    const isExpanded = expandedGroups.has(groupKey) || groupBy === 'none';
    const GroupIcon = group.metadata.icon;

    return (
      <div key={groupKey} className="space-y-2">
        {groupBy !== 'none' && (
          <div
            className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer"
            onClick={() => toggleGroup(groupKey)}
          >
            <div className="flex items-center gap-2">
              <GroupIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium text-sm">{group.metadata.title}</span>
              <Badge variant="outline" className="text-xs">
                {group.metadata.count}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                {group.metadata.description}
              </span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          </div>
        )}

        {isExpanded && (
          <div className={cn(
            "space-y-2",
            viewMode === 'grid' && "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          )}>
            {group.results.map(renderResult)}
          </div>
        )}
      </div>
    );
  }, [expandedGroups, groupBy, viewMode, toggleGroup, renderResult]);

  if (results.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center p-8 text-center">
          <Search className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No results found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search terms or filters
          </p>
          <div className="text-sm text-muted-foreground">
            <p>Suggestions:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Check spelling and try different keywords</li>
              <li>Use fewer or more general terms</li>
              <li>Remove some filters to broaden your search</li>
              <li>Try using fuzzy search or semantic search</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Results Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">
                Search Results
              </CardTitle>
              <CardDescription>
                Found {results.length} results for "{query}"
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {/* Handle view mode change */}}
              >
                <List className="h-3 w-3" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {/* Handle view mode change */}}
              >
                <Grid className="h-3 w-3" />
              </Button>
              <Button
                variant={viewMode === 'compact' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {/* Handle view mode change */}}
              >
                <Filter className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Results */}
      <ScrollArea className="h-[600px]">
        <div className="space-y-4">
          {Object.entries(groupedResults).map(([groupKey, group]) =>
            renderGroup(groupKey, group)
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
