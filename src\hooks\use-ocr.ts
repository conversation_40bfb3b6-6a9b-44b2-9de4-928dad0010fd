"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { OCRManager, type OCRDocumentResult, type OCRProgress, type OCRJobConfig } from '@/lib/ocr/ocr-manager';
import type { OCRResult, ImagePreprocessingOptions } from '@/lib/ocr/tesseract-engine';

export interface UseOCRConfig {
  autoInitialize?: boolean;
  enableCaching?: boolean;
  maxWorkers?: number;
  defaultLanguage?: string;
  enableBackgroundProcessing?: boolean;
}

export interface OCRState {
  isInitialized: boolean;
  isInitializing: boolean;
  isProcessing: boolean;
  progress: OCRProgress | null;
  results: Map<string, OCRDocumentResult>;
  error: string | null;
  stats: {
    activeJobs: number;
    queuedJobs: number;
    totalProcessed: number;
    averageProcessingTime: number;
  };
}

const DEFAULT_CONFIG: UseOCRConfig = {
  autoInitialize: true,
  enableCaching: true,
  maxWorkers: 2,
  defaultLanguage: 'eng',
  enableBackgroundProcessing: true,
};

export function useOCR(config: UseOCRConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  const [state, setState] = useState<OCRState>({
    isInitialized: false,
    isInitializing: false,
    isProcessing: false,
    progress: null,
    results: new Map(),
    error: null,
    stats: {
      activeJobs: 0,
      queuedJobs: 0,
      totalProcessed: 0,
      averageProcessingTime: 0,
    },
  });

  const ocrManagerRef = useRef<OCRManager | null>(null);
  const processingTimesRef = useRef<number[]>([]);

  // Initialize OCR manager
  const initialize = useCallback(async () => {
    if (state.isInitialized || state.isInitializing) return;

    setState(prev => ({ ...prev, isInitializing: true, error: null }));

    try {
      ocrManagerRef.current = OCRManager.getInstance({
        maxWorkers: finalConfig.maxWorkers,
        defaultLanguage: finalConfig.defaultLanguage,
        enableCaching: finalConfig.enableCaching,
        enableBackgroundProcessing: finalConfig.enableBackgroundProcessing,
      });

      await ocrManagerRef.current.initialize((progress) => {
        setState(prev => ({ ...prev, progress }));
      });

      setState(prev => ({
        ...prev,
        isInitialized: true,
        isInitializing: false,
        progress: null,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isInitializing: false,
        error: error instanceof Error ? error.message : 'Failed to initialize OCR',
      }));
    }
  }, [finalConfig, state.isInitialized, state.isInitializing]);

  // Process entire document
  const processDocument = useCallback(async (
    pdfDocument: any,
    options: {
      documentId: string;
      pages?: number[];
      language?: string;
      preprocessingOptions?: ImagePreprocessingOptions;
      priority?: 'low' | 'normal' | 'high';
      backgroundProcessing?: boolean;
    }
  ): Promise<OCRDocumentResult | null> => {
    if (!ocrManagerRef.current || !state.isInitialized) {
      setState(prev => ({ ...prev, error: 'OCR not initialized' }));
      return null;
    }

    const {
      documentId,
      pages,
      language,
      preprocessingOptions,
      priority = 'normal',
      backgroundProcessing = false,
    } = options;

    setState(prev => ({ ...prev, isProcessing: true, error: null, progress: null }));

    try {
      const startTime = Date.now();

      // Determine pages to process
      const pagesToProcess = pages || Array.from({ length: pdfDocument.numPages }, (_, i) => i + 1);

      const jobConfig: OCRJobConfig = {
        documentId,
        pages: pagesToProcess,
        language,
        preprocessingOptions,
        priority,
        enableCaching: finalConfig.enableCaching,
        backgroundProcessing,
      };

      let result: OCRDocumentResult;

      if (backgroundProcessing) {
        // Queue for background processing
        await ocrManagerRef.current.queueOCRJob(jobConfig);
        
        // Return placeholder result
        result = {
          documentId,
          totalPages: pagesToProcess.length,
          processedPages: 0,
          results: new Map(),
          metadata: {
            startTime: new Date(),
            averageConfidence: 0,
            detectedLanguages: [],
            status: 'pending',
          },
        };
      } else {
        // Process immediately
        result = await ocrManagerRef.current.processDocument(
          pdfDocument,
          jobConfig,
          (progress) => {
            setState(prev => ({ ...prev, progress }));
          },
          (pageResult) => {
            // Update results as pages complete
            setState(prev => {
              const newResults = new Map(prev.results);
              const existingResult = newResults.get(documentId);
              if (existingResult) {
                existingResult.results.set(pageResult.pageNumber, pageResult);
                existingResult.processedPages++;
              }
              return { ...prev, results: newResults };
            });
          }
        );
      }

      const processingTime = Date.now() - startTime;
      processingTimesRef.current.push(processingTime);

      // Update state
      setState(prev => {
        const newResults = new Map(prev.results);
        newResults.set(documentId, result);

        const averageProcessingTime = processingTimesRef.current.reduce((a, b) => a + b, 0) / processingTimesRef.current.length;

        return {
          ...prev,
          isProcessing: false,
          progress: null,
          results: newResults,
          stats: {
            ...prev.stats,
            totalProcessed: prev.stats.totalProcessed + 1,
            averageProcessingTime,
          },
        };
      });

      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: null,
        error: error instanceof Error ? error.message : 'OCR processing failed',
      }));
      return null;
    }
  }, [state.isInitialized, finalConfig.enableCaching]);

  // Process single page
  const processPage = useCallback(async (
    pdfDocument: any,
    pageNumber: number,
    options: {
      documentId?: string;
      language?: string;
      preprocessingOptions?: ImagePreprocessingOptions;
    } = {}
  ): Promise<OCRResult | null> => {
    if (!ocrManagerRef.current || !state.isInitialized) {
      setState(prev => ({ ...prev, error: 'OCR not initialized' }));
      return null;
    }

    setState(prev => ({ ...prev, isProcessing: true, error: null, progress: null }));

    try {
      const result = await ocrManagerRef.current.processPage(
        pdfDocument,
        pageNumber,
        {
          ...options,
          enableCaching: finalConfig.enableCaching,
          onProgress: (progress) => {
            setState(prev => ({ ...prev, progress }));
          },
        }
      );

      setState(prev => ({ ...prev, isProcessing: false, progress: null }));
      return result;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: null,
        error: error instanceof Error ? error.message : 'Page OCR failed',
      }));
      return null;
    }
  }, [state.isInitialized, finalConfig.enableCaching]);

  // Get OCR result for document
  const getDocumentResult = useCallback((documentId: string): OCRDocumentResult | null => {
    return state.results.get(documentId) || null;
  }, [state.results]);

  // Get OCR result for specific page
  const getPageResult = useCallback((documentId: string, pageNumber: number): OCRResult | null => {
    const documentResult = state.results.get(documentId);
    return documentResult?.results.get(pageNumber) || null;
  }, [state.results]);

  // Search OCR text
  const searchOCRText = useCallback((
    documentId: string,
    query: string,
    options: {
      caseSensitive?: boolean;
      wholeWords?: boolean;
      useRegex?: boolean;
    } = {}
  ): Array<{ pageNumber: number; matches: Array<{ text: string; confidence: number }> }> => {
    const documentResult = state.results.get(documentId);
    if (!documentResult) return [];

    const { caseSensitive = false, wholeWords = false, useRegex = false } = options;
    const results: Array<{ pageNumber: number; matches: Array<{ text: string; confidence: number }> }> = [];

    for (const [pageNumber, ocrResult] of documentResult.results) {
      const matches: Array<{ text: string; confidence: number }> = [];

      let searchRegex: RegExp;
      if (useRegex) {
        try {
          searchRegex = new RegExp(query, caseSensitive ? 'g' : 'gi');
        } catch {
          continue; // Invalid regex
        }
      } else {
        const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const pattern = wholeWords ? `\\b${escapedQuery}\\b` : escapedQuery;
        searchRegex = new RegExp(pattern, caseSensitive ? 'g' : 'gi');
      }

      // Search in words
      for (const word of ocrResult.words) {
        if (searchRegex.test(word.text)) {
          matches.push({
            text: word.text,
            confidence: word.confidence,
          });
        }
      }

      if (matches.length > 0) {
        results.push({ pageNumber, matches });
      }
    }

    return results;
  }, [state.results]);

  // Clear cache
  const clearCache = useCallback(async (documentId?: string) => {
    if (!ocrManagerRef.current) return;

    try {
      await ocrManagerRef.current.clearCache(documentId);
      
      if (documentId) {
        setState(prev => {
          const newResults = new Map(prev.results);
          newResults.delete(documentId);
          return { ...prev, results: newResults };
        });
      } else {
        setState(prev => ({ ...prev, results: new Map() }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to clear cache',
      }));
    }
  }, []);

  // Cancel job
  const cancelJob = useCallback((documentId: string): boolean => {
    if (!ocrManagerRef.current) return false;

    const cancelled = ocrManagerRef.current.cancelJob(documentId);
    
    if (cancelled) {
      setState(prev => {
        const newResults = new Map(prev.results);
        const result = newResults.get(documentId);
        if (result) {
          result.metadata.status = 'cancelled';
        }
        return { ...prev, results: newResults };
      });
    }

    return cancelled;
  }, []);

  // Update stats periodically
  useEffect(() => {
    if (!ocrManagerRef.current || !state.isInitialized) return;

    const updateStats = () => {
      const managerStats = ocrManagerRef.current!.getStats();
      setState(prev => ({
        ...prev,
        stats: {
          ...prev.stats,
          activeJobs: managerStats.activeJobs,
          queuedJobs: managerStats.queuedJobs,
        },
      }));
    };

    const interval = setInterval(updateStats, 1000);
    return () => clearInterval(interval);
  }, [state.isInitialized]);

  // Auto-initialize if enabled
  useEffect(() => {
    if (finalConfig.autoInitialize && !state.isInitialized && !state.isInitializing) {
      initialize();
    }
  }, [finalConfig.autoInitialize, state.isInitialized, state.isInitializing, initialize]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (ocrManagerRef.current) {
        ocrManagerRef.current.terminate();
      }
    };
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    initialize,
    processDocument,
    processPage,
    getDocumentResult,
    getPageResult,
    searchOCRText,
    clearCache,
    cancelJob,
    
    // Utilities
    isReady: state.isInitialized && !state.isInitializing,
    hasError: !!state.error,
    clearError: () => setState(prev => ({ ...prev, error: null })),
  };
}
