"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Search, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Settings, 
  Loader2,
  FileText,
  MapPin,
  Clock,
  TrendingUp,
  Zap,
  Eye,
  EyeOff,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { PDFSearchEngine, type SearchResult, type SearchOptions, type SearchProgress } from '@/lib/search/pdf-search';

interface SearchPanelProps {
  searchEngine: PDFSearchEngine;
  onResultSelect?: (result: SearchResult) => void;
  onHighlightToggle?: (enabled: boolean) => void;
  className?: string;
}

export default function SearchPanel({
  searchEngine,
  onResultSelect,
  onHighlightToggle,
  className,
}: SearchPanelProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchProgress, setSearchProgress] = useState<SearchProgress | null>(null);
  const [selectedResultIndex, setSelectedResultIndex] = useState(-1);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [highlightEnabled, setHighlightEnabled] = useState(true);
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    caseSensitive: false,
    wholeWords: false,
    useRegex: false,
    highlightAll: true,
    maxResults: 100,
    searchInAnnotations: true,
    searchInMetadata: true,
    fuzzySearch: false,
    fuzzyThreshold: 0.8,
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Setup search engine event listeners
  useEffect(() => {
    const handleProgress = (progress: SearchProgress) => {
      setSearchProgress(progress);
    };

    const handleComplete = (data: { results: SearchResult[] }) => {
      setResults(data.results);
      setIsSearching(false);
      setSearchProgress(null);
      setSelectedResultIndex(data.results.length > 0 ? 0 : -1);
    };

    const handleError = (data: { error: Error }) => {
      console.error('Search error:', data.error);
      setIsSearching(false);
      setSearchProgress(null);
    };

    const handleCancelled = () => {
      setIsSearching(false);
      setSearchProgress(null);
    };

    searchEngine.addEventListener('search-progress', handleProgress);
    searchEngine.addEventListener('search-complete', handleComplete);
    searchEngine.addEventListener('search-error', handleError);
    searchEngine.addEventListener('search-cancelled', handleCancelled);

    return () => {
      searchEngine.removeEventListener('search-progress', handleProgress);
      searchEngine.removeEventListener('search-complete', handleComplete);
      searchEngine.removeEventListener('search-error', handleError);
      searchEngine.removeEventListener('search-cancelled', handleCancelled);
    };
  }, [searchEngine]);

  const handleSearch = useCallback(async () => {
    if (!query.trim() || isSearching) return;

    setIsSearching(true);
    setResults([]);
    setSelectedResultIndex(-1);

    try {
      await searchEngine.search(query, searchOptions);
    } catch (error) {
      console.error('Search failed:', error);
    }
  }, [query, searchOptions, isSearching, searchEngine]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleSearch();
    } else if (event.key === 'Escape') {
      setQuery('');
      setResults([]);
      setSelectedResultIndex(-1);
      searchEngine.cancelSearch();
    } else if (event.key === 'ArrowDown' && results.length > 0) {
      event.preventDefault();
      setSelectedResultIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (event.key === 'ArrowUp' && results.length > 0) {
      event.preventDefault();
      setSelectedResultIndex(prev => Math.max(prev - 1, 0));
    } else if (event.key === 'Enter' && selectedResultIndex >= 0) {
      event.preventDefault();
      onResultSelect?.(results[selectedResultIndex]);
    }
  }, [handleSearch, results, selectedResultIndex, onResultSelect, searchEngine]);

  const handleResultClick = useCallback((result: SearchResult, index: number) => {
    setSelectedResultIndex(index);
    onResultSelect?.(result);
  }, [onResultSelect]);

  const handleClearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setSelectedResultIndex(-1);
    searchEngine.cancelSearch();
    searchInputRef.current?.focus();
  }, [searchEngine]);

  const handleHighlightToggle = useCallback((enabled: boolean) => {
    setHighlightEnabled(enabled);
    onHighlightToggle?.(enabled);
  }, [onHighlightToggle]);

  const updateSearchOption = useCallback((key: keyof SearchOptions, value: any) => {
    setSearchOptions(prev => ({ ...prev, [key]: value }));
  }, []);

  const formatResultContext = (result: SearchResult): string => {
    const { context, matchText } = result;
    const matchIndex = context.toLowerCase().indexOf(matchText.toLowerCase());
    
    if (matchIndex === -1) return context;
    
    const before = context.substring(0, matchIndex);
    const after = context.substring(matchIndex + matchText.length);
    
    return `${before}${matchText}${after}`;
  };

  const getSearchStats = () => {
    const stats = searchEngine.getStats();
    return {
      totalSearches: stats.totalSearches,
      averageTime: Math.round(stats.averageSearchTime),
      topTerms: Array.from(stats.mostSearchedTerms.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3),
    };
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Input */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Document
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              ref={searchInputRef}
              placeholder="Search in document..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 pr-20"
              disabled={isSearching}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
              {query && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSearch}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              <Button
                onClick={handleSearch}
                disabled={!query.trim() || isSearching}
                size="sm"
              >
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Search Progress */}
          {searchProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Searching page {searchProgress.currentPage} of {searchProgress.totalPages}</span>
                <span>{searchProgress.resultsFound} results found</span>
              </div>
              <Progress 
                value={(searchProgress.currentPage / searchProgress.totalPages) * 100} 
                className="h-2"
              />
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Advanced
                {showAdvanced ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
              </Button>
              
              <div className="flex items-center gap-2">
                <Switch
                  checked={highlightEnabled}
                  onCheckedChange={handleHighlightToggle}
                  id="highlight-toggle"
                />
                <label htmlFor="highlight-toggle" className="text-sm">
                  {highlightEnabled ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                </label>
              </div>
            </div>

            {results.length > 0 && (
              <Badge variant="outline">
                {results.length} result{results.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>

          {/* Advanced Options */}
          <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
            <CollapsibleContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm">Case sensitive</label>
                  <Switch
                    checked={searchOptions.caseSensitive}
                    onCheckedChange={(checked) => updateSearchOption('caseSensitive', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm">Whole words</label>
                  <Switch
                    checked={searchOptions.wholeWords}
                    onCheckedChange={(checked) => updateSearchOption('wholeWords', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm">Use regex</label>
                  <Switch
                    checked={searchOptions.useRegex}
                    onCheckedChange={(checked) => updateSearchOption('useRegex', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm">Fuzzy search</label>
                  <Switch
                    checked={searchOptions.fuzzySearch}
                    onCheckedChange={(checked) => updateSearchOption('fuzzySearch', checked)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Search Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Search Results
              </span>
              <Badge variant="outline">
                {selectedResultIndex + 1} of {results.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={resultsRef} className="space-y-2 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={result.id}
                  className={cn(
                    "p-3 rounded-lg border cursor-pointer transition-colors",
                    index === selectedResultIndex 
                      ? "bg-primary/10 border-primary/20" 
                      : "bg-muted/50 hover:bg-muted"
                  )}
                  onClick={() => handleResultClick(result, index)}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          Page {result.pageNumber}
                        </Badge>
                        {result.confidence < 1 && (
                          <Badge variant="secondary" className="text-xs">
                            {Math.round(result.confidence * 100)}% match
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm leading-relaxed">
                        {formatResultContext(result)}
                      </p>
                    </div>
                    
                    <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Statistics */}
      {(() => {
        const stats = getSearchStats();
        return stats.totalSearches > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <TrendingUp className="h-4 w-4" />
                Search Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold">{stats.totalSearches}</div>
                  <div className="text-xs text-muted-foreground">Total Searches</div>
                </div>
                <div>
                  <div className="text-lg font-bold">{stats.averageTime}ms</div>
                  <div className="text-xs text-muted-foreground">Avg Time</div>
                </div>
                <div>
                  <div className="text-lg font-bold">{stats.topTerms.length}</div>
                  <div className="text-xs text-muted-foreground">Popular Terms</div>
                </div>
              </div>
              
              {stats.topTerms.length > 0 && (
                <div className="mt-4">
                  <div className="text-sm font-medium mb-2">Most Searched:</div>
                  <div className="flex flex-wrap gap-1">
                    {stats.topTerms.map(([term, count]) => (
                      <Badge key={term} variant="outline" className="text-xs">
                        {term} ({count})
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })()}

      {/* No Results */}
      {!isSearching && query && results.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Search className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-muted-foreground">No results found for "{query}"</p>
            <p className="text-sm text-muted-foreground mt-1">
              Try adjusting your search terms or using advanced options
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Hook for search functionality
export function useSearch(searchEngine: PDFSearchEngine) {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [currentResults, setCurrentResults] = useState<SearchResult[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);

  const openSearch = useCallback(() => {
    setIsSearchOpen(true);
  }, []);

  const closeSearch = useCallback(() => {
    setIsSearchOpen(false);
    searchEngine.clearHighlights();
  }, [searchEngine]);

  const nextResult = useCallback(() => {
    if (currentResults.length > 0) {
      setCurrentIndex(prev => (prev + 1) % currentResults.length);
    }
  }, [currentResults.length]);

  const previousResult = useCallback(() => {
    if (currentResults.length > 0) {
      setCurrentIndex(prev => prev <= 0 ? currentResults.length - 1 : prev - 1);
    }
  }, [currentResults.length]);

  return {
    isSearchOpen,
    currentResults,
    currentIndex,
    openSearch,
    closeSearch,
    nextResult,
    previousResult,
    setCurrentResults,
    setCurrentIndex,
  };
}
