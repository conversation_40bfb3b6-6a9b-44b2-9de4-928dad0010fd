/**
 * Error Handler Utility
 * Comprehensive error handling and logging system
 */

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  timestamp?: Date;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  error: Error;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'ui' | 'api' | 'storage' | 'accessibility' | 'performance' | 'security' | 'unknown';
  timestamp: Date;
  stack?: string;
  handled: boolean;
}

export type ErrorHandler = (report: ErrorReport) => void;

class ErrorHandlerService {
  private handlers: Set<ErrorHandler> = new Set();
  private errorReports: ErrorReport[] = [];
  private maxReports = 100;
  private isEnabled = true;

  constructor() {
    this.setupGlobalErrorHandlers();
  }

  private setupGlobalErrorHandlers(): void {
    if (typeof window === 'undefined') return;

    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message), {
        component: 'global',
        action: 'uncaught-error',
        url: event.filename,
        additionalData: {
          line: event.lineno,
          column: event.colno,
        },
      }, 'high', 'unknown');
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        {
          component: 'global',
          action: 'unhandled-rejection',
        },
        'high',
        'unknown'
      );
    });

    // Handle console errors (optional)
    if (console.error) {
      const originalError = console.error;
      console.error = (...args: any[]) => {
        originalError.apply(console, args);
        
        if (args.length > 0 && args[0] instanceof Error) {
          this.handleError(args[0], {
            component: 'console',
            action: 'console-error',
            additionalData: { args: args.slice(1) },
          }, 'medium', 'unknown');
        }
      };
    }
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public addHandler(handler: ErrorHandler): void {
    this.handlers.add(handler);
  }

  public removeHandler(handler: ErrorHandler): void {
    this.handlers.delete(handler);
  }

  public handleError(
    error: Error,
    context: ErrorContext = {},
    severity: ErrorReport['severity'] = 'medium',
    category: ErrorReport['category'] = 'unknown'
  ): string {
    if (!this.isEnabled) return '';

    const report: ErrorReport = {
      id: this.generateErrorId(),
      error,
      context: {
        ...context,
        timestamp: context.timestamp || new Date(),
        userAgent: context.userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : ''),
        url: context.url || (typeof window !== 'undefined' ? window.location.href : ''),
      },
      severity,
      category,
      timestamp: new Date(),
      stack: error.stack,
      handled: true,
    };

    this.storeErrorReport(report);
    this.notifyHandlers(report);

    return report.id;
  }

  public handleAsyncError<T>(
    promise: Promise<T>,
    context: ErrorContext = {},
    severity: ErrorReport['severity'] = 'medium',
    category: ErrorReport['category'] = 'api'
  ): Promise<T> {
    return promise.catch((error) => {
      this.handleError(
        error instanceof Error ? error : new Error(String(error)),
        context,
        severity,
        category
      );
      throw error; // Re-throw to maintain promise chain behavior
    });
  }

  public wrapFunction<T extends (...args: any[]) => any>(
    fn: T,
    context: ErrorContext = {},
    severity: ErrorReport['severity'] = 'medium',
    category: ErrorReport['category'] = 'ui'
  ): T {
    return ((...args: Parameters<T>) => {
      try {
        const result = fn(...args);
        
        // Handle async functions
        if (result instanceof Promise) {
          return this.handleAsyncError(result, context, severity, category);
        }
        
        return result;
      } catch (error) {
        this.handleError(
          error instanceof Error ? error : new Error(String(error)),
          context,
          severity,
          category
        );
        throw error; // Re-throw to maintain function behavior
      }
    }) as T;
  }

  public createErrorBoundary(
    component: string,
    fallbackRender?: (error: Error, errorId: string) => React.ReactNode
  ) {
    return class ErrorBoundary extends React.Component<
      { children: React.ReactNode },
      { hasError: boolean; error?: Error; errorId?: string }
    > {
      constructor(props: { children: React.ReactNode }) {
        super(props);
        this.state = { hasError: false };
      }

      static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
      }

      componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        const errorId = ErrorHandlerService.getInstance().handleError(error, {
          component,
          action: 'react-error-boundary',
          additionalData: {
            componentStack: errorInfo.componentStack,
            errorBoundary: component,
          },
        }, 'high', 'ui');

        this.setState({ errorId });
      }

      render() {
        if (this.state.hasError && this.state.error) {
          if (fallbackRender) {
            return fallbackRender(this.state.error, this.state.errorId || '');
          }

          return (
            <div className="error-boundary">
              <h2>Something went wrong</h2>
              <p>An error occurred in the {component} component.</p>
              {this.state.errorId && (
                <p className="text-sm text-muted-foreground">
                  Error ID: {this.state.errorId}
                </p>
              )}
            </div>
          );
        }

        return this.props.children;
      }
    };
  }

  public getErrorReports(
    filter?: {
      severity?: ErrorReport['severity'];
      category?: ErrorReport['category'];
      component?: string;
      since?: Date;
    }
  ): ErrorReport[] {
    let reports = [...this.errorReports];

    if (filter) {
      if (filter.severity) {
        reports = reports.filter(r => r.severity === filter.severity);
      }
      if (filter.category) {
        reports = reports.filter(r => r.category === filter.category);
      }
      if (filter.component) {
        reports = reports.filter(r => r.context.component === filter.component);
      }
      if (filter.since) {
        reports = reports.filter(r => r.timestamp >= filter.since!);
      }
    }

    return reports.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public clearErrorReports(): void {
    this.errorReports = [];
  }

  public exportErrorReports(): string {
    return JSON.stringify(this.errorReports, null, 2);
  }

  private generateErrorId(): string {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private storeErrorReport(report: ErrorReport): void {
    this.errorReports.push(report);
    
    // Keep only the most recent reports
    if (this.errorReports.length > this.maxReports) {
      this.errorReports = this.errorReports.slice(-this.maxReports);
    }

    // Store in localStorage for persistence (optional)
    try {
      if (typeof localStorage !== 'undefined') {
        const recentReports = this.errorReports.slice(-10); // Store only last 10
        localStorage.setItem('error-reports', JSON.stringify(recentReports));
      }
    } catch {
      // Ignore storage errors
    }
  }

  private notifyHandlers(report: ErrorReport): void {
    this.handlers.forEach(handler => {
      try {
        handler(report);
      } catch (error) {
        console.error('Error in error handler:', error);
      }
    });
  }

  // Singleton pattern
  private static instance: ErrorHandlerService;

  public static getInstance(): ErrorHandlerService {
    if (!ErrorHandlerService.instance) {
      ErrorHandlerService.instance = new ErrorHandlerService();
    }
    return ErrorHandlerService.instance;
  }
}

// Export singleton instance
export const errorHandler = ErrorHandlerService.getInstance();

// Convenience functions
export const handleError = (
  error: Error,
  context?: ErrorContext,
  severity?: ErrorReport['severity'],
  category?: ErrorReport['category']
): string => {
  return errorHandler.handleError(error, context, severity, category);
};

export const handleAsyncError = <T>(
  promise: Promise<T>,
  context?: ErrorContext,
  severity?: ErrorReport['severity'],
  category?: ErrorReport['category']
): Promise<T> => {
  return errorHandler.handleAsyncError(promise, context, severity, category);
};

export const wrapFunction = <T extends (...args: any[]) => any>(
  fn: T,
  context?: ErrorContext,
  severity?: ErrorReport['severity'],
  category?: ErrorReport['category']
): T => {
  return errorHandler.wrapFunction(fn, context, severity, category);
};

export const createErrorBoundary = (
  component: string,
  fallbackRender?: (error: Error, errorId: string) => React.ReactNode
) => {
  return errorHandler.createErrorBoundary(component, fallbackRender);
};

// React import (conditional)
let React: any;
try {
  React = require('react');
} catch {
  // React not available, error boundary won't work
}
