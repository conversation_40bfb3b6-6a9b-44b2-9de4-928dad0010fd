"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { cn } from '@/lib/utils';

export interface BreakpointConfig {
  name: string;
  minWidth: number;
  maxWidth?: number;
  columns: number;
  sidebarMode: 'hidden' | 'mini' | 'compact' | 'full';
  headerMode: 'minimal' | 'compact' | 'full';
  tabMode: 'icons-only' | 'minimal' | 'compact' | 'full';
  spacing: 'tight' | 'normal' | 'loose';
  maxDocuments: number;
}

export interface ResponsiveLayoutConfig {
  breakpoints: BreakpointConfig[];
  enableAutoLayout: boolean;
  enableDensityControl: boolean;
  enableOrientationAdaptation: boolean;
  minContentWidth: number;
  maxContentWidth: number;
}

interface ResponsiveLayoutManagerProps {
  children: React.ReactNode;
  config: ResponsiveLayoutConfig;
  onLayoutChange?: (breakpoint: BreakpointConfig) => void;
  className?: string;
}

const DEFAULT_BREAKPOINTS: BreakpointConfig[] = [
  {
    name: 'mobile',
    minWidth: 0,
    maxWidth: 767,
    columns: 1,
    sidebarMode: 'hidden',
    headerMode: 'minimal',
    tabMode: 'icons-only',
    spacing: 'tight',
    maxDocuments: 1
  },
  {
    name: 'tablet',
    minWidth: 768,
    maxWidth: 1023,
    columns: 1,
    sidebarMode: 'mini',
    headerMode: 'compact',
    tabMode: 'minimal',
    spacing: 'normal',
    maxDocuments: 2
  },
  {
    name: 'laptop',
    minWidth: 1024,
    maxWidth: 1365,
    columns: 2,
    sidebarMode: 'compact',
    headerMode: 'compact',
    tabMode: 'compact',
    spacing: 'normal',
    maxDocuments: 4
  },
  {
    name: 'desktop',
    minWidth: 1366,
    maxWidth: 1919,
    columns: 2,
    sidebarMode: 'full',
    headerMode: 'full',
    tabMode: 'full',
    spacing: 'comfortable',
    maxDocuments: 6
  },
  {
    name: 'large-desktop',
    minWidth: 1920,
    maxWidth: 2559,
    columns: 3,
    sidebarMode: 'full',
    headerMode: 'full',
    tabMode: 'full',
    spacing: 'comfortable',
    maxDocuments: 8
  },
  {
    name: 'ultrawide',
    minWidth: 2560,
    columns: 4,
    sidebarMode: 'full',
    headerMode: 'full',
    tabMode: 'full',
    spacing: 'loose',
    maxDocuments: 12
  }
];

const DEFAULT_CONFIG: ResponsiveLayoutConfig = {
  breakpoints: DEFAULT_BREAKPOINTS,
  enableAutoLayout: true,
  enableDensityControl: true,
  enableOrientationAdaptation: true,
  minContentWidth: 320,
  maxContentWidth: 1600 // Increased for better desktop utilization
};

export default function ResponsiveLayoutManager({
  children,
  config = DEFAULT_CONFIG,
  onLayoutChange,
  className
}: ResponsiveLayoutManagerProps) {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<BreakpointConfig>(config.breakpoints[0]);
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('landscape');
  const [density, setDensity] = useState<'comfortable' | 'compact' | 'dense'>('comfortable');
  const containerRef = useRef<HTMLDivElement>(null);

  // Update window size and orientation
  useEffect(() => {
    const updateSize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });
      setOrientation(width > height ? 'landscape' : 'portrait');
      
      // Find matching breakpoint
      const breakpoint = config.breakpoints.find(bp => {
        return width >= bp.minWidth && (bp.maxWidth === undefined || width <= bp.maxWidth);
      }) || config.breakpoints[config.breakpoints.length - 1];
      
      if (breakpoint !== currentBreakpoint) {
        setCurrentBreakpoint(breakpoint);
        onLayoutChange?.(breakpoint);
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    window.addEventListener('orientationchange', updateSize);
    
    return () => {
      window.removeEventListener('resize', updateSize);
      window.removeEventListener('orientationchange', updateSize);
    };
  }, [config.breakpoints, currentBreakpoint, onLayoutChange]);

  // Auto-adjust density based on screen size
  useEffect(() => {
    if (!config.enableDensityControl) return;

    if (windowSize.width < 768) {
      setDensity('dense');
    } else if (windowSize.width < 1024) {
      setDensity('compact');
    } else {
      setDensity('comfortable');
    }
  }, [windowSize.width, config.enableDensityControl]);

  // Get responsive CSS classes
  const getResponsiveClasses = useCallback(() => {
    const classes = [];

    // Breakpoint-specific classes
    classes.push(`breakpoint-${currentBreakpoint.name}`);
    
    // Spacing classes
    switch (currentBreakpoint.spacing) {
      case 'tight':
        classes.push('spacing-tight');
        break;
      case 'loose':
        classes.push('spacing-loose');
        break;
      default:
        classes.push('spacing-normal');
    }

    // Density classes
    classes.push(`density-${density}`);

    // Orientation classes
    if (config.enableOrientationAdaptation) {
      classes.push(`orientation-${orientation}`);
    }

    // Column classes
    classes.push(`columns-${currentBreakpoint.columns}`);

    return classes;
  }, [currentBreakpoint, density, orientation, config.enableOrientationAdaptation]);

  // Get container styles
  const getContainerStyles = useCallback(() => {
    const styles: React.CSSProperties = {};

    // Content width constraints
    if (config.minContentWidth) {
      styles.minWidth = `${config.minContentWidth}px`;
    }
    
    if (config.maxContentWidth && windowSize.width > config.maxContentWidth) {
      styles.maxWidth = `${config.maxContentWidth}px`;
      styles.marginLeft = 'auto';
      styles.marginRight = 'auto';
    }

    // Grid layout for multi-column
    if (currentBreakpoint.columns > 1) {
      styles.display = 'grid';
      styles.gridTemplateColumns = `repeat(${currentBreakpoint.columns}, 1fr)`;
      
      // Adjust gap based on spacing
      switch (currentBreakpoint.spacing) {
        case 'tight':
          styles.gap = '0.25rem';
          break;
        case 'loose':
          styles.gap = '1rem';
          break;
        default:
          styles.gap = '0.5rem';
      }
    }

    return styles;
  }, [config.minContentWidth, config.maxContentWidth, windowSize.width, currentBreakpoint]);

  // Provide layout context to children
  const layoutContext = {
    breakpoint: currentBreakpoint,
    windowSize,
    orientation,
    density,
    isSmallScreen: windowSize.width < 768,
    isMediumScreen: windowSize.width >= 768 && windowSize.width < 1024,
    isLargeScreen: windowSize.width >= 1024,
    isUltrawide: windowSize.width >= 1920,
    isMobile: currentBreakpoint.name === 'mobile',
    isTablet: currentBreakpoint.name === 'tablet',
    isDesktop: ['laptop', 'desktop', 'ultrawide'].includes(currentBreakpoint.name),
    shouldShowSidebar: currentBreakpoint.sidebarMode !== 'hidden',
    shouldUseMiniSidebar: currentBreakpoint.sidebarMode === 'mini',
    shouldUseCompactHeader: currentBreakpoint.headerMode !== 'full',
    shouldUseMinimalTabs: ['icons-only', 'minimal'].includes(currentBreakpoint.tabMode),
    maxVisibleDocuments: currentBreakpoint.maxDocuments,
    gridColumns: currentBreakpoint.columns
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'responsive-layout-container',
        ...getResponsiveClasses(),
        className
      )}
      style={getContainerStyles()}
      data-breakpoint={currentBreakpoint.name}
      data-orientation={orientation}
      data-density={density}
    >
      {/* Provide context through data attributes and CSS custom properties */}
      <style jsx="true">{`
        .responsive-layout-container {
          --breakpoint-name: ${currentBreakpoint.name};
          --sidebar-mode: ${currentBreakpoint.sidebarMode};
          --header-mode: ${currentBreakpoint.headerMode};
          --tab-mode: ${currentBreakpoint.tabMode};
          --spacing-mode: ${currentBreakpoint.spacing};
          --columns: ${currentBreakpoint.columns};
          --max-documents: ${currentBreakpoint.maxDocuments};
          --window-width: ${windowSize.width}px;
          --window-height: ${windowSize.height}px;
          --density: ${density};
          --orientation: ${orientation};
        }

        /* Spacing variations */
        .spacing-tight {
          --spacing-xs: 0.125rem;
          --spacing-sm: 0.25rem;
          --spacing-md: 0.375rem;
          --spacing-lg: 0.5rem;
          --spacing-xl: 0.75rem;
        }

        .spacing-normal {
          --spacing-xs: 0.25rem;
          --spacing-sm: 0.5rem;
          --spacing-md: 0.75rem;
          --spacing-lg: 1rem;
          --spacing-xl: 1.5rem;
        }

        .spacing-loose {
          --spacing-xs: 0.5rem;
          --spacing-sm: 0.75rem;
          --spacing-md: 1rem;
          --spacing-lg: 1.5rem;
          --spacing-xl: 2rem;
        }

        /* Density variations */
        .density-dense {
          --density-multiplier: 0.75;
          --font-size-multiplier: 0.875;
        }

        .density-compact {
          --density-multiplier: 0.875;
          --font-size-multiplier: 0.9375;
        }

        .density-comfortable {
          --density-multiplier: 1;
          --font-size-multiplier: 1;
        }

        /* Responsive utilities */
        .breakpoint-mobile {
          --is-mobile: 1;
          --is-desktop: 0;
        }

        .breakpoint-tablet {
          --is-mobile: 0;
          --is-tablet: 1;
          --is-desktop: 0;
        }

        .breakpoint-laptop,
        .breakpoint-desktop,
        .breakpoint-ultrawide {
          --is-mobile: 0;
          --is-tablet: 0;
          --is-desktop: 1;
        }

        /* Orientation-specific styles */
        .orientation-portrait {
          --is-portrait: 1;
          --is-landscape: 0;
        }

        .orientation-landscape {
          --is-portrait: 0;
          --is-landscape: 1;
        }

        /* Column-based layouts */
        .columns-1 {
          --grid-columns: 1;
        }

        .columns-2 {
          --grid-columns: 2;
        }

        .columns-3 {
          --grid-columns: 3;
        }

        .columns-4 {
          --grid-columns: 4;
        }
      `}</style>

      {/* Pass layout context to children */}
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            ...child.props,
            layoutContext
          } as any);
        }
        return child;
      })}
    </div>
  );
}

// Hook to use layout context in child components
export function useResponsiveLayout() {
  const [layoutContext, setLayoutContext] = useState<any>(null);

  useEffect(() => {
    const container = document.querySelector('.responsive-layout-container');
    if (container) {
      const breakpoint = container.getAttribute('data-breakpoint');
      const orientation = container.getAttribute('data-orientation');
      const density = container.getAttribute('data-density');
      
      setLayoutContext({
        breakpoint,
        orientation,
        density,
        isSmallScreen: window.innerWidth < 768,
        isMediumScreen: window.innerWidth >= 768 && window.innerWidth < 1024,
        isLargeScreen: window.innerWidth >= 1024,
        isMobile: breakpoint === 'mobile',
        isTablet: breakpoint === 'tablet',
        isDesktop: ['laptop', 'desktop', 'ultrawide'].includes(breakpoint || ''),
      });
    }
  }, []);

  return layoutContext;
}

// Export types and defaults
export { DEFAULT_BREAKPOINTS, DEFAULT_CONFIG };
export type { BreakpointConfig, ResponsiveLayoutConfig };
