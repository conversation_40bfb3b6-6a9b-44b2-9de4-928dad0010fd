"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  StickyNote,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Clock,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  BookOpen,
  Eye,
  EyeOff
} from 'lucide-react';
import type { PresentationNote } from './presentation-mode';

interface PresentationNotesProps {
  notes: PresentationNote[];
  onNotesChange: (notes: PresentationNote[]) => void;
  currentPage: number;
  totalPages: number;
  isPresenting: boolean;
  className?: string;
}

export default function PresentationNotes({
  notes,
  onNotesChange,
  currentPage,
  totalPages,
  isPresenting,
  className
}: PresentationNotesProps) {
  const [showNotes, setShowNotes] = useState(true);
  const [editingNote, setEditingNote] = useState<string | null>(null);
  const [newNoteContent, setNewNoteContent] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAllPages, setShowAllPages] = useState(false);
  const [isAddingNote, setIsAddingNote] = useState(false);

  // Get notes for current page
  const currentPageNotes = notes.filter(note => note.pageNumber === currentPage);
  
  // Get all notes (for overview)
  const allNotes = showAllPages ? notes : currentPageNotes;
  
  // Filter notes by search term
  const filteredNotes = allNotes.filter(note =>
    note.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Add new note
  const addNote = useCallback(() => {
    if (!newNoteContent.trim()) return;

    const newNote: PresentationNote = {
      pageNumber: currentPage,
      content: newNoteContent.trim(),
      timestamp: Date.now()
    };

    onNotesChange([...notes, newNote]);
    setNewNoteContent('');
    setIsAddingNote(false);
  }, [newNoteContent, currentPage, notes, onNotesChange]);

  // Update note
  const updateNote = useCallback((noteId: string, content: string) => {
    const updatedNotes = notes.map(note => {
      if (note.timestamp.toString() === noteId) {
        return { ...note, content: content.trim() };
      }
      return note;
    });
    onNotesChange(updatedNotes);
    setEditingNote(null);
  }, [notes, onNotesChange]);

  // Delete note
  const deleteNote = useCallback((noteId: string) => {
    const updatedNotes = notes.filter(note => note.timestamp.toString() !== noteId);
    onNotesChange(updatedNotes);
  }, [notes, onNotesChange]);

  // Navigate to page with note
  const goToNotePage = useCallback((pageNumber: number) => {
    // This would need to be connected to the main presentation navigation
    console.log('Navigate to page:', pageNumber);
  }, []);

  // Format timestamp
  const formatTimestamp = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!isPresenting) return;

      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'n':
            event.preventDefault();
            setIsAddingNote(true);
            break;
          case 'f':
            event.preventDefault();
            // Focus search input
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isPresenting]);

  if (!showNotes) {
    return (
      <Button
        onClick={() => setShowNotes(true)}
        variant="outline"
        size="sm"
        className="fixed bottom-4 right-4 bg-black/80 backdrop-blur-sm text-white border-white/20"
      >
        <StickyNote className="h-4 w-4 mr-2" />
        Show Notes
      </Button>
    );
  }

  return (
    <Card className={`bg-black/90 backdrop-blur-sm text-white border-white/20 ${className}`}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <StickyNote className="h-5 w-5" />
            <span className="font-medium">Speaker Notes</span>
            <Badge variant="outline" className="text-xs">
              {showAllPages ? `${notes.length} total` : `${currentPageNotes.length} on slide ${currentPage}`}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button
              onClick={() => setShowAllPages(!showAllPages)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              title={showAllPages ? "Show current page only" : "Show all pages"}
            >
              {showAllPages ? <Eye className="h-4 w-4" /> : <BookOpen className="h-4 w-4" />}
            </Button>
            <Button
              onClick={() => setShowNotes(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              <EyeOff className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search */}
        {notes.length > 0 && (
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-black/50 border border-white/20 rounded text-sm"
              />
            </div>
          </div>
        )}

        {/* Add new note */}
        <div className="mb-4">
          {isAddingNote ? (
            <div className="space-y-2">
              <Textarea
                value={newNoteContent}
                onChange={(e) => setNewNoteContent(e.target.value)}
                placeholder={`Add note for slide ${currentPage}...`}
                className="bg-black/50 border-white/20 text-white resize-none"
                rows={3}
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  onClick={addNote}
                  disabled={!newNoteContent.trim()}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </Button>
                <Button
                  onClick={() => {
                    setIsAddingNote(false);
                    setNewNoteContent('');
                  }}
                  variant="outline"
                  size="sm"
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <Button
              onClick={() => setIsAddingNote(true)}
              variant="outline"
              size="sm"
              className="w-full border-dashed border-white/30 hover:border-white/50"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Note for Slide {currentPage}
            </Button>
          )}
        </div>

        {/* Notes list */}
        <ScrollArea className="h-64">
          <div className="space-y-3">
            {filteredNotes.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                {searchTerm ? (
                  <>
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No notes found matching "{searchTerm}"</p>
                  </>
                ) : (
                  <>
                    <StickyNote className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">
                      {showAllPages ? 'No notes yet' : `No notes for slide ${currentPage}`}
                    </p>
                    <p className="text-xs mt-1">
                      Press Ctrl+N to add a note
                    </p>
                  </>
                )}
              </div>
            ) : (
              filteredNotes.map((note) => {
                const noteId = note.timestamp.toString();
                const isEditing = editingNote === noteId;

                return (
                  <Card key={noteId} className="p-3 bg-black/50 border-white/10">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          Slide {note.pageNumber}
                        </Badge>
                        <span className="text-xs text-gray-400">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {formatTimestamp(note.timestamp)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        {showAllPages && note.pageNumber !== currentPage && (
                          <Button
                            onClick={() => goToNotePage(note.pageNumber)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            title="Go to slide"
                          >
                            <ChevronRight className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          onClick={() => setEditingNote(isEditing ? null : noteId)}
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          onClick={() => deleteNote(noteId)}
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {isEditing ? (
                      <div className="space-y-2">
                        <Textarea
                          defaultValue={note.content}
                          className="bg-black/50 border-white/20 text-white resize-none"
                          rows={3}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                              updateNote(noteId, e.currentTarget.value);
                            }
                            if (e.key === 'Escape') {
                              setEditingNote(null);
                            }
                          }}
                          onBlur={(e) => updateNote(noteId, e.target.value)}
                          autoFocus
                        />
                        <div className="text-xs text-gray-400">
                          Press Ctrl+Enter to save, Esc to cancel
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                    )}
                  </Card>
                );
              })
            )}
          </div>
        </ScrollArea>

        {/* Quick stats */}
        {notes.length > 0 && (
          <>
            <Separator className="my-4 bg-white/10" />
            <div className="text-xs text-gray-400 space-y-1">
              <div className="flex justify-between">
                <span>Total notes:</span>
                <span>{notes.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Pages with notes:</span>
                <span>{new Set(notes.map(n => n.pageNumber)).size} / {totalPages}</span>
              </div>
              {searchTerm && (
                <div className="flex justify-between">
                  <span>Matching search:</span>
                  <span>{filteredNotes.length}</span>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </Card>
  );
}
