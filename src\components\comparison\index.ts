// Document Comparison Components
export { default as PDFComparisonViewer } from './pdf-comparison-viewer';
export { default as ComparisonManager } from './comparison-manager';
export { default as PDFDiffOverlay, TextDiffHighlight, DiffStats } from './pdf-diff-overlay';

// Utilities
export { ComparisonExporter } from './comparison-export';
export { pdfTextExtractor } from './pdf-text-extractor';

// Re-export types for convenience
export type {
  ComparisonResult,
  DiffItem,
  TextDiff,
  VisualDiff,
  ComparisonOptions
} from './pdf-comparison-viewer';

export type {
  ExtractedText,
  TextItem,
  TextPosition
} from './pdf-text-extractor';

export type {
  ComparisonExportData
} from './comparison-export';
