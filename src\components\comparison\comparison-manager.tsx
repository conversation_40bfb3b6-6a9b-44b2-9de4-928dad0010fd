"use client";

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  GitCompare, 
  X, 
  ArrowRight,
  FileText,
  CheckCircle2
} from 'lucide-react';
import { toast } from 'sonner';
import PDFComparisonViewer from './pdf-comparison-viewer';
import type { DocumentInstance } from '@/lib/types/pdf';

interface ComparisonManagerProps {
  documents: DocumentInstance[];
  onClose: () => void;
  className?: string;
}

interface ComparisonPair {
  document1: DocumentInstance;
  document2: DocumentInstance;
  id: string;
}

export default function ComparisonManager({
  documents,
  onClose,
  className
}: ComparisonManagerProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentInstance[]>([]);
  const [activeComparison, setActiveComparison] = useState<ComparisonPair | null>(null);
  const [comparisonHistory, setComparisonHistory] = useState<ComparisonPair[]>([]);

  // Handle document selection for comparison
  const handleDocumentSelect = useCallback((document: DocumentInstance) => {
    setSelectedDocuments(prev => {
      const isSelected = prev.some(d => d.id === document.id);
      
      if (isSelected) {
        // Remove from selection
        return prev.filter(d => d.id !== document.id);
      } else if (prev.length < 2) {
        // Add to selection (max 2 documents)
        return [...prev, document];
      } else {
        // Replace first document with new selection
        return [prev[1], document];
      }
    });
  }, []);

  // Start comparison between selected documents
  const startComparison = useCallback(() => {
    if (selectedDocuments.length !== 2) {
      toast.error('Please select exactly 2 documents to compare');
      return;
    }

    const [doc1, doc2] = selectedDocuments;
    const comparisonPair: ComparisonPair = {
      document1: doc1,
      document2: doc2,
      id: `comparison_${doc1.id}_${doc2.id}_${Date.now()}`
    };

    setActiveComparison(comparisonPair);
    
    // Add to history if not already present
    setComparisonHistory(prev => {
      const exists = prev.some(c => 
        (c.document1.id === doc1.id && c.document2.id === doc2.id) ||
        (c.document1.id === doc2.id && c.document2.id === doc1.id)
      );
      
      if (!exists) {
        return [comparisonPair, ...prev.slice(0, 4)]; // Keep last 5 comparisons
      }
      return prev;
    });

    toast.success(`Starting comparison: ${doc1.title} vs ${doc2.title}`);
  }, [selectedDocuments]);

  // Close active comparison
  const closeComparison = useCallback(() => {
    setActiveComparison(null);
    setSelectedDocuments([]);
  }, []);

  // Load comparison from history
  const loadComparison = useCallback((comparison: ComparisonPair) => {
    setActiveComparison(comparison);
    setSelectedDocuments([comparison.document1, comparison.document2]);
  }, []);

  // Export comparison results
  const handleExport = useCallback((format: 'pdf' | 'html' | 'json') => {
    if (!activeComparison) return;
    
    // This would be implemented based on the comparison results
    toast.success(`Comparison exported as ${format.toUpperCase()}`);
  }, [activeComparison]);

  // If we have an active comparison, show the comparison viewer
  if (activeComparison) {
    return (
      <PDFComparisonViewer
        document1={activeComparison.document1}
        document2={activeComparison.document2}
        onClose={closeComparison}
        onExport={handleExport}
        className={className}
      />
    );
  }

  // Show document selection interface
  return (
    <div className={`h-screen flex flex-col bg-background ${className}`}>
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-2">
              <GitCompare className="h-5 w-5" />
              <h1 className="text-lg font-semibold">Document Comparison</h1>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {selectedDocuments.length}/2 documents selected
            </Badge>
            <Button
              onClick={startComparison}
              disabled={selectedDocuments.length !== 2}
              size="sm"
            >
              <GitCompare className="h-4 w-4 mr-2" />
              Compare Documents
            </Button>
          </div>
        </div>

        {/* Selected documents preview */}
        {selectedDocuments.length > 0 && (
          <div className="mt-4 flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Selected:</span>
              {selectedDocuments.map((doc, index) => (
                <React.Fragment key={doc.id}>
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    {doc.title}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => handleDocumentSelect(doc)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                  {index === 0 && selectedDocuments.length === 2 && (
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="flex-1 flex">
        {/* Document selection grid */}
        <div className="flex-1 p-6">
          <h2 className="text-lg font-medium mb-4">Select Documents to Compare</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {documents.map((document) => {
              const isSelected = selectedDocuments.some(d => d.id === document.id);
              const canSelect = selectedDocuments.length < 2 || isSelected;
              
              return (
                <Card
                  key={document.id}
                  className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                    isSelected 
                      ? 'ring-2 ring-primary bg-primary/5' 
                      : canSelect 
                      ? 'hover:bg-muted/50' 
                      : 'opacity-50 cursor-not-allowed'
                  }`}
                  onClick={() => canSelect && handleDocumentSelect(document)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      {isSelected && (
                        <CheckCircle2 className="h-4 w-4 text-primary" />
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {document.numPages} pages
                    </Badge>
                  </div>
                  
                  <h3 className="font-medium text-sm mb-1 line-clamp-2">
                    {document.title}
                  </h3>
                  
                  <div className="text-xs text-muted-foreground space-y-1">
                    {document.metadata.author && (
                      <p>Author: {document.metadata.author}</p>
                    )}
                    <p>
                      Modified: {new Date(document.metadata.lastAccessedDate).toLocaleDateString()}
                    </p>
                    <p>Size: {document.metadata.fileSize}</p>
                  </div>
                </Card>
              );
            })}
          </div>

          {documents.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Documents Available</h3>
              <p className="text-muted-foreground">
                Open some documents first to start comparing them.
              </p>
            </div>
          )}
        </div>

        {/* Comparison history sidebar */}
        {comparisonHistory.length > 0 && (
          <div className="w-80 border-l bg-muted/20 p-4">
            <h3 className="font-medium mb-4">Recent Comparisons</h3>
            <div className="space-y-2">
              {comparisonHistory.map((comparison) => (
                <Card
                  key={comparison.id}
                  className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => loadComparison(comparison)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <GitCompare className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Comparison</span>
                    </div>
                    <div className="text-xs space-y-1">
                      <p className="font-medium">{comparison.document1.title}</p>
                      <div className="flex items-center gap-1">
                        <ArrowRight className="h-3 w-3" />
                        <span className="text-muted-foreground">vs</span>
                      </div>
                      <p className="font-medium">{comparison.document2.title}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
