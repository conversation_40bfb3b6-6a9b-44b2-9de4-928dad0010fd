"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  User, 
  Monitor, 
  Accessibility, 
  Zap, 
  Shield, 
  Users, 
  Code,
  Save,
  Download,
  Upload,
  RotateCcw,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  UserPreferenceSystem, 
  type UserPreferences,
  type PreferenceProfile,
  type PreferenceValidation,
} from '@/lib/preferences/user-preference-system';

interface UserPreferencePanelProps {
  preferenceSystem: UserPreferenceSystem;
  className?: string;
}

export default function UserPreferencePanel({
  preferenceSystem,
  className,
}: UserPreferencePanelProps) {
  const [preferences, setPreferences] = useState<UserPreferences>(preferenceSystem.getPreferences());
  const [profiles, setProfiles] = useState<PreferenceProfile[]>([]);
  const [currentProfile, setCurrentProfile] = useState<PreferenceProfile | null>(null);
  const [validation, setValidation] = useState<PreferenceValidation | null>(null);
  const [showCreateProfile, setShowCreateProfile] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileDescription, setNewProfileDescription] = useState('');
  const [newProfileCategory, setNewProfileCategory] = useState<PreferenceProfile['category']>('custom');

  // Load data on mount
  useEffect(() => {
    updateData();
  }, []);

  // Listen for preference system events
  useEffect(() => {
    const handlePreferencesUpdated = () => updateData();
    const handleProfileCreated = () => updateData();
    const handleProfileLoaded = () => updateData();
    const handleProfileDeleted = () => updateData();

    preferenceSystem.addEventListener('preferences-updated', handlePreferencesUpdated);
    preferenceSystem.addEventListener('profile-created', handleProfileCreated);
    preferenceSystem.addEventListener('profile-loaded', handleProfileLoaded);
    preferenceSystem.addEventListener('profile-deleted', handleProfileDeleted);

    return () => {
      preferenceSystem.removeEventListener('preferences-updated', handlePreferencesUpdated);
      preferenceSystem.removeEventListener('profile-created', handleProfileCreated);
      preferenceSystem.removeEventListener('profile-loaded', handleProfileLoaded);
      preferenceSystem.removeEventListener('profile-deleted', handleProfileDeleted);
    };
  }, [preferenceSystem]);

  const updateData = useCallback(() => {
    setPreferences(preferenceSystem.getPreferences());
    setProfiles(preferenceSystem.getProfiles());
    setCurrentProfile(preferenceSystem.getCurrentProfile());
  }, [preferenceSystem]);

  const handlePreferenceChange = useCallback((category: keyof UserPreferences, key: string, value: any) => {
    const updates = {
      [category]: {
        [key]: value,
      },
    } as Partial<UserPreferences>;

    const validationResult = preferenceSystem.updatePreferences(updates);
    setValidation(validationResult);
  }, [preferenceSystem]);

  const handleCreateProfile = useCallback(() => {
    if (!newProfileName.trim()) return;

    preferenceSystem.createProfile(newProfileName, newProfileDescription, newProfileCategory);
    setNewProfileName('');
    setNewProfileDescription('');
    setNewProfileCategory('custom');
    setShowCreateProfile(false);
  }, [preferenceSystem, newProfileName, newProfileDescription, newProfileCategory]);

  const handleLoadProfile = useCallback((profileId: string) => {
    preferenceSystem.loadProfile(profileId);
  }, [preferenceSystem]);

  const handleDeleteProfile = useCallback((profileId: string) => {
    preferenceSystem.deleteProfile(profileId);
  }, [preferenceSystem]);

  const handleExportPreferences = useCallback(() => {
    const exportData = preferenceSystem.exportPreferences();
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `preferences-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [preferenceSystem]);

  const handleImportPreferences = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        const success = preferenceSystem.importPreferences(data);
        if (success) {
          setShowImportDialog(false);
        }
      } catch (error) {
        console.error('Failed to import preferences:', error);
      }
    };
    reader.readAsText(file);
  }, [preferenceSystem]);

  const handleResetToDefaults = useCallback(() => {
    if (confirm('Are you sure you want to reset all preferences to defaults? This cannot be undone.')) {
      preferenceSystem.resetToDefaults();
    }
  }, [preferenceSystem]);

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Preferences
          </CardTitle>
          <CardDescription>
            Comprehensive user settings with accessibility profiles and preferences management
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Validation Messages */}
      {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
        <div className="space-y-2">
          {validation.errors.map((error, index) => (
            <Alert key={index} variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error.message}</AlertDescription>
            </Alert>
          ))}
          {validation.warnings.map((warning, index) => (
            <Alert key={index}>
              <Info className="h-4 w-4" />
              <AlertDescription>{warning.message}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="profiles">Profiles</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          {/* General Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="h-5 w-5" />
                General Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Theme</Label>
                <Select
                  value={preferences.general.theme}
                  onValueChange={(value: any) => handlePreferenceChange('general', 'theme', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Language</Label>
                <Select
                  value={preferences.general.language}
                  onValueChange={(value) => handlePreferenceChange('general', 'language', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Español</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="zh">中文</SelectItem>
                    <SelectItem value="ja">日本語</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="auto-save">Auto Save</Label>
                <Switch
                  id="auto-save"
                  checked={preferences.general.autoSave}
                  onCheckedChange={(checked) => handlePreferenceChange('general', 'autoSave', checked)}
                />
              </div>

              {preferences.general.autoSave && (
                <div className="space-y-2 ml-4">
                  <Label>Auto Save Interval: {preferences.general.autoSaveInterval} minutes</Label>
                  <Slider
                    value={[preferences.general.autoSaveInterval]}
                    onValueChange={([value]) => handlePreferenceChange('general', 'autoSaveInterval', value)}
                    min={1}
                    max={60}
                    step={1}
                    className="w-full"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="confirm-exit">Confirm Before Exit</Label>
                <Switch
                  id="confirm-exit"
                  checked={preferences.general.confirmBeforeExit}
                  onCheckedChange={(checked) => handlePreferenceChange('general', 'confirmBeforeExit', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="welcome-screen">Show Welcome Screen</Label>
                <Switch
                  id="welcome-screen"
                  checked={preferences.general.showWelcomeScreen}
                  onCheckedChange={(checked) => handlePreferenceChange('general', 'showWelcomeScreen', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="analytics">Enable Analytics</Label>
                <Switch
                  id="analytics"
                  checked={preferences.general.enableAnalytics}
                  onCheckedChange={(checked) => handlePreferenceChange('general', 'enableAnalytics', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Viewer Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Monitor className="h-5 w-5" />
                PDF Viewer Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Default Zoom: {Math.round(preferences.viewer.defaultZoom * 100)}%</Label>
                <Slider
                  value={[preferences.viewer.defaultZoom]}
                  onValueChange={([value]) => handlePreferenceChange('viewer', 'defaultZoom', value)}
                  min={0.25}
                  max={5.0}
                  step={0.25}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Default View Mode</Label>
                <Select
                  value={preferences.viewer.defaultViewMode}
                  onValueChange={(value: any) => handlePreferenceChange('viewer', 'defaultViewMode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">Single Page</SelectItem>
                    <SelectItem value="continuous">Continuous</SelectItem>
                    <SelectItem value="facing">Facing Pages</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="thumbnails">Enable Thumbnails</Label>
                <Switch
                  id="thumbnails"
                  checked={preferences.viewer.enableThumbnails}
                  onCheckedChange={(checked) => handlePreferenceChange('viewer', 'enableThumbnails', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="outline">Enable Outline</Label>
                <Switch
                  id="outline"
                  checked={preferences.viewer.enableOutline}
                  onCheckedChange={(checked) => handlePreferenceChange('viewer', 'enableOutline', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="annotations">Enable Annotations</Label>
                <Switch
                  id="annotations"
                  checked={preferences.viewer.enableAnnotations}
                  onCheckedChange={(checked) => handlePreferenceChange('viewer', 'enableAnnotations', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accessibility" className="space-y-4">
          {/* Accessibility Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Accessibility className="h-5 w-5" />
                Accessibility Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="screen-reader">Enable Screen Reader</Label>
                <Switch
                  id="screen-reader"
                  checked={preferences.accessibility.enableScreenReader}
                  onCheckedChange={(checked) => handlePreferenceChange('accessibility', 'enableScreenReader', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="keyboard-nav">Enable Keyboard Navigation</Label>
                <Switch
                  id="keyboard-nav"
                  checked={preferences.accessibility.enableKeyboardNavigation}
                  onCheckedChange={(checked) => handlePreferenceChange('accessibility', 'enableKeyboardNavigation', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="high-contrast">Enable High Contrast</Label>
                <Switch
                  id="high-contrast"
                  checked={preferences.accessibility.enableHighContrast}
                  onCheckedChange={(checked) => handlePreferenceChange('accessibility', 'enableHighContrast', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Font Size: {preferences.accessibility.fontSize}px</Label>
                <Slider
                  value={[preferences.accessibility.fontSize]}
                  onValueChange={([value]) => handlePreferenceChange('accessibility', 'fontSize', value)}
                  min={8}
                  max={72}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Line Height: {preferences.accessibility.lineHeight.toFixed(1)}</Label>
                <Slider
                  value={[preferences.accessibility.lineHeight]}
                  onValueChange={([value]) => handlePreferenceChange('accessibility', 'lineHeight', value)}
                  min={1.0}
                  max={3.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Color Blindness Support</Label>
                <Select
                  value={preferences.accessibility.colorBlindnessSupport}
                  onValueChange={(value: any) => handlePreferenceChange('accessibility', 'colorBlindnessSupport', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="protanopia">Protanopia (Red-blind)</SelectItem>
                    <SelectItem value="deuteranopia">Deuteranopia (Green-blind)</SelectItem>
                    <SelectItem value="tritanopia">Tritanopia (Blue-blind)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {/* Performance Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Zap className="h-5 w-5" />
                Performance Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Cache Size: {preferences.performance.cacheSize} MB</Label>
                <Slider
                  value={[preferences.performance.cacheSize]}
                  onValueChange={([value]) => handlePreferenceChange('performance', 'cacheSize', value)}
                  min={10}
                  max={1000}
                  step={10}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Preload Pages: {preferences.performance.preloadPages}</Label>
                <Slider
                  value={[preferences.performance.preloadPages]}
                  onValueChange={([value]) => handlePreferenceChange('performance', 'preloadPages', value)}
                  min={0}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="web-workers">Enable Web Workers</Label>
                <Switch
                  id="web-workers"
                  checked={preferences.performance.enableWebWorkers}
                  onCheckedChange={(checked) => handlePreferenceChange('performance', 'enableWebWorkers', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="gpu-acceleration">Enable GPU Acceleration</Label>
                <Switch
                  id="gpu-acceleration"
                  checked={preferences.performance.enableGPUAcceleration}
                  onCheckedChange={(checked) => handlePreferenceChange('performance', 'enableGPUAcceleration', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Quality Mode</Label>
                <Select
                  value={preferences.performance.qualityMode}
                  onValueChange={(value: any) => handlePreferenceChange('performance', 'qualityMode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profiles" className="space-y-4">
          {/* Profile Management */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Preference Profiles</h3>
            
            <div className="flex items-center gap-2">
              <Dialog open={showCreateProfile} onOpenChange={setShowCreateProfile}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Profile
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create Preference Profile</DialogTitle>
                    <DialogDescription>
                      Save your current settings as a new profile
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="profile-name">Profile Name</Label>
                      <Input
                        id="profile-name"
                        value={newProfileName}
                        onChange={(e) => setNewProfileName(e.target.value)}
                        placeholder="Enter profile name"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="profile-description">Description</Label>
                      <Textarea
                        id="profile-description"
                        value={newProfileDescription}
                        onChange={(e) => setNewProfileDescription(e.target.value)}
                        placeholder="Enter profile description"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="profile-category">Category</Label>
                      <Select
                        value={newProfileCategory}
                        onValueChange={(value: any) => setNewProfileCategory(value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="accessibility">Accessibility</SelectItem>
                          <SelectItem value="performance">Performance</SelectItem>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowCreateProfile(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleCreateProfile}>
                        Create Profile
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              <Button variant="outline" onClick={handleExportPreferences}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>

              <input
                type="file"
                accept=".json"
                onChange={handleImportPreferences}
                className="hidden"
                id="import-preferences"
              />
              <Button
                variant="outline"
                onClick={() => document.getElementById('import-preferences')?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>

              <Button variant="outline" onClick={handleResetToDefaults}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>

          {/* Profiles List */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {profiles.map((profile) => (
              <Card
                key={profile.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-muted/50",
                  currentProfile?.id === profile.id && "ring-2 ring-primary"
                )}
                onClick={() => handleLoadProfile(profile.id)}
              >
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium">{profile.name}</h4>
                        <p className="text-sm text-muted-foreground">{profile.description}</p>
                      </div>
                      
                      {!profile.isSystem && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteProfile(profile.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Badge variant="outline">{profile.category}</Badge>
                      {profile.isSystem && <Badge variant="secondary">System</Badge>}
                      {profile.isDefault && <Badge variant="default">Default</Badge>}
                      <span>Used {profile.usageCount} times</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
