"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Monitor, 
  Zap, 
  Battery, 
  Eye, 
  Palette, 
  Cpu, 
  Memory, 
  Wifi,
  Smartphone,
  Download,
  Upload,
  RotateCcw,
  Info,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { QualityManager, type QualitySettings, type UserPreferences, type DeviceCapabilities } from '@/lib/pdf/quality-manager';

interface QualityControlsProps {
  onSettingsChange?: (settings: QualitySettings) => void;
  onPreferencesChange?: (preferences: UserPreferences) => void;
  className?: string;
}

export default function QualityControls({
  onSettingsChange,
  onPreferencesChange,
  className,
}: QualityControlsProps) {
  const [qualityManager] = useState(() => new QualityManager());
  const [currentSettings, setCurrentSettings] = useState<QualitySettings>(
    qualityManager.getCurrentSettings()
  );
  const [userPreferences, setUserPreferences] = useState<UserPreferences>(
    qualityManager.getUserPreferences()
  );
  const [deviceCapabilities, setDeviceCapabilities] = useState<DeviceCapabilities>(
    qualityManager.getDeviceCapabilities()
  );
  const [activeTab, setActiveTab] = useState<'quick' | 'advanced' | 'device' | 'accessibility'>('quick');

  // Update settings when quality manager changes
  useEffect(() => {
    const settings = qualityManager.getCurrentSettings();
    setCurrentSettings(settings);
    onSettingsChange?.(settings);
  }, [qualityManager, onSettingsChange]);

  const updatePreferences = useCallback((newPreferences: Partial<UserPreferences>) => {
    const updated = { ...userPreferences, ...newPreferences };
    setUserPreferences(updated);
    qualityManager.updateUserPreferences(newPreferences);
    onPreferencesChange?.(updated);
    
    // Update current settings after preference change
    setCurrentSettings(qualityManager.getCurrentSettings());
  }, [userPreferences, qualityManager, onPreferencesChange]);

  const updateCustomSettings = useCallback((newSettings: Partial<QualitySettings>) => {
    const updated = { ...currentSettings, ...newSettings };
    setCurrentSettings(updated);
    qualityManager.setCustomSettings(newSettings);
    onSettingsChange?.(updated);
  }, [currentSettings, qualityManager, onSettingsChange]);

  const resetToOptimal = useCallback(() => {
    qualityManager.resetToOptimal();
    setCurrentSettings(qualityManager.getCurrentSettings());
    onSettingsChange?.(qualityManager.getCurrentSettings());
  }, [qualityManager, onSettingsChange]);

  const exportSettings = useCallback(() => {
    const settingsJson = qualityManager.exportSettings();
    const blob = new Blob([settingsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'pdf-quality-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  }, [qualityManager]);

  const importSettings = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (qualityManager.importSettings(content)) {
        setCurrentSettings(qualityManager.getCurrentSettings());
        setUserPreferences(qualityManager.getUserPreferences());
        onSettingsChange?.(qualityManager.getCurrentSettings());
        onPreferencesChange?.(qualityManager.getUserPreferences());
      }
    };
    reader.readAsText(file);
  }, [qualityManager, onSettingsChange, onPreferencesChange]);

  const getQualityBadgeColor = (quality: string) => {
    switch (quality) {
      case 'ultra': return 'bg-purple-500';
      case 'high': return 'bg-blue-500';
      case 'medium': return 'bg-green-500';
      case 'low': return 'bg-yellow-500';
      case 'battery-saver': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            PDF Quality Controls
          </CardTitle>
          <CardDescription>
            Optimize rendering quality based on your device and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge className={getQualityBadgeColor(userPreferences.preferredQuality)}>
                {userPreferences.preferredQuality}
              </Badge>
              <span className="text-sm text-muted-foreground">
                Render Scale: {currentSettings.renderScale}x
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={resetToOptimal}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button variant="outline" size="sm" onClick={exportSettings}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <label className="cursor-pointer">
                <Button variant="outline" size="sm" asChild>
                  <span>
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </span>
                </Button>
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  className="hidden"
                />
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="quick">Quick Settings</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="device">Device Info</TabsTrigger>
          <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
        </TabsList>

        <TabsContent value="quick" className="space-y-6">
          {/* Quality Preset */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quality Preset</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Preferred Quality</label>
                <Select
                  value={userPreferences.preferredQuality}
                  onValueChange={(value) => updatePreferences({ preferredQuality: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto (Recommended)</SelectItem>
                    <SelectItem value="ultra">Ultra Quality</SelectItem>
                    <SelectItem value="high">High Quality</SelectItem>
                    <SelectItem value="medium">Medium Quality</SelectItem>
                    <SelectItem value="low">Low Quality</SelectItem>
                    <SelectItem value="battery-saver">Battery Saver</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority Toggles */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Prioritize Battery</label>
                    <p className="text-xs text-muted-foreground">Reduce quality to save battery</p>
                  </div>
                  <Switch
                    checked={userPreferences.prioritizeBattery}
                    onCheckedChange={(checked) => updatePreferences({ prioritizeBattery: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <label className="text-sm font-medium">Prioritize Speed</label>
                    <p className="text-xs text-muted-foreground">Optimize for fast rendering</p>
                  </div>
                  <Switch
                    checked={userPreferences.prioritizeSpeed}
                    onCheckedChange={(checked) => updatePreferences({ prioritizeSpeed: checked })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Render Scale */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Render Scale</CardTitle>
              <CardDescription>
                Higher values provide sharper text but use more resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Scale: {currentSettings.renderScale}x</span>
                  <Badge variant="outline">
                    {currentSettings.renderScale < 1 ? 'Low' : 
                     currentSettings.renderScale === 1 ? 'Normal' : 
                     currentSettings.renderScale < 1.5 ? 'High' : 'Ultra'}
                  </Badge>
                </div>
                <Slider
                  value={[currentSettings.renderScale]}
                  onValueChange={([value]) => updateCustomSettings({ renderScale: value })}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0.5x (Faster)</span>
                  <span>2.0x (Sharper)</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          {/* Rendering Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Rendering Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Text Layer Mode</label>
                    <Select
                      value={currentSettings.textLayerMode}
                      onValueChange={(value) => updateCustomSettings({ textLayerMode: value as any })}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="disable">Disabled</SelectItem>
                        <SelectItem value="enable">Enabled</SelectItem>
                        <SelectItem value="enhance">Enhanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Image Quality</label>
                    <Select
                      value={currentSettings.imageQuality}
                      onValueChange={(value) => updateCustomSettings({ imageQuality: value as any })}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="ultra">Ultra</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Cache Strategy</label>
                    <Select
                      value={currentSettings.cacheStrategy}
                      onValueChange={(value) => updateCustomSettings({ cacheStrategy: value as any })}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="balanced">Balanced</SelectItem>
                        <SelectItem value="aggressive">Aggressive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">WebGL Acceleration</label>
                      <p className="text-xs text-muted-foreground">Use GPU for rendering</p>
                    </div>
                    <Switch
                      checked={currentSettings.enableWebGL}
                      onCheckedChange={(checked) => updateCustomSettings({ enableWebGL: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Hardware Acceleration</label>
                      <p className="text-xs text-muted-foreground">Use hardware features</p>
                    </div>
                    <Switch
                      checked={currentSettings.enableHardwareAcceleration}
                      onCheckedChange={(checked) => updateCustomSettings({ enableHardwareAcceleration: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Progressive Rendering</label>
                      <p className="text-xs text-muted-foreground">Show content as it loads</p>
                    </div>
                    <Switch
                      checked={currentSettings.enableProgressiveRendering}
                      onCheckedChange={(checked) => updateCustomSettings({ enableProgressiveRendering: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Anti-aliasing</label>
                      <p className="text-xs text-muted-foreground">Smooth edges</p>
                    </div>
                    <Switch
                      checked={currentSettings.antiAliasing}
                      onCheckedChange={(checked) => updateCustomSettings({ antiAliasing: checked })}
                    />
                  </div>
                </div>
              </div>

              {/* Performance Settings */}
              <div className="space-y-4">
                <h4 className="font-medium">Performance Settings</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Preload Pages: {currentSettings.preloadPages}</label>
                    <Slider
                      value={[currentSettings.preloadPages]}
                      onValueChange={([value]) => updateCustomSettings({ preloadPages: value })}
                      min={1}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Max Concurrent Renders: {currentSettings.maxConcurrentRenders}</label>
                    <Slider
                      value={[currentSettings.maxConcurrentRenders]}
                      onValueChange={([value]) => updateCustomSettings({ maxConcurrentRenders: value })}
                      min={1}
                      max={8}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="device" className="space-y-6">
          {/* Device Capabilities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Device Capabilities
              </CardTitle>
              <CardDescription>
                Detected hardware and software capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">CPU Cores</span>
                  </div>
                  <p className="text-lg font-bold">{deviceCapabilities.cpuCores}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Memory className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Memory</span>
                  </div>
                  <p className="text-lg font-bold">{deviceCapabilities.memoryGB} GB</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">GPU Tier</span>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {deviceCapabilities.gpuTier}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">Screen Density</span>
                  </div>
                  <p className="text-lg font-bold">{deviceCapabilities.screenDensity}x</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Wifi className="h-4 w-4 text-cyan-500" />
                    <span className="text-sm font-medium">Connection</span>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {deviceCapabilities.connectionType}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Smartphone className="h-4 w-4 text-pink-500" />
                    <span className="text-sm font-medium">Touch Support</span>
                  </div>
                  <Badge variant={deviceCapabilities.isTouch ? "default" : "secondary"}>
                    {deviceCapabilities.isTouch ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>

              {deviceCapabilities.batteryLevel !== null && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Battery className="h-4 w-4" />
                    <span className="text-sm font-medium">Battery Status</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm">Level: {Math.round(deviceCapabilities.batteryLevel * 100)}%</span>
                    {deviceCapabilities.isLowPowerMode && (
                      <Badge variant="destructive">Low Power Mode</Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accessibility" className="space-y-6">
          {/* Accessibility Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Accessibility Settings
              </CardTitle>
              <CardDescription>
                Customize the viewer for better accessibility
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Reduced Motion</label>
                      <p className="text-xs text-muted-foreground">Minimize animations</p>
                    </div>
                    <Switch
                      checked={userPreferences.reducedMotion}
                      onCheckedChange={(checked) => updatePreferences({ reducedMotion: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">High Contrast</label>
                      <p className="text-xs text-muted-foreground">Increase contrast</p>
                    </div>
                    <Switch
                      checked={userPreferences.highContrast}
                      onCheckedChange={(checked) => updatePreferences({ highContrast: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium">Large Text</label>
                      <p className="text-xs text-muted-foreground">Increase text size</p>
                    </div>
                    <Switch
                      checked={userPreferences.largeText}
                      onCheckedChange={(checked) => updatePreferences({ largeText: checked })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Color Blindness Support</label>
                    <Select
                      value={userPreferences.colorBlindnessType}
                      onValueChange={(value) => updatePreferences({ colorBlindnessType: value as any })}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="protanopia">Protanopia (Red-blind)</SelectItem>
                        <SelectItem value="deuteranopia">Deuteranopia (Green-blind)</SelectItem>
                        <SelectItem value="tritanopia">Tritanopia (Blue-blind)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {(userPreferences.reducedMotion || userPreferences.highContrast) && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Accessibility settings may affect rendering quality and performance.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
