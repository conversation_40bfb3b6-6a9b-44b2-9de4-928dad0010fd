/**
 * Embedding Worker
 * Web Worker for generating text embeddings using various models
 */

// Simple sentence transformer implementation for browser
class SimpleSentenceTransformer {
  constructor() {
    this.vocabulary = new Map();
    this.embeddingDim = 384;
    this.maxLength = 512;
  }

  async loadModel() {
    // In a real implementation, this would load a pre-trained model
    // For now, we'll use a simple word-based approach
    console.log('Loading simple sentence transformer model...');
  }

  tokenize(text) {
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 0)
      .slice(0, this.maxLength);
  }

  async encode(text) {
    const tokens = this.tokenize(text);
    const embedding = new Array(this.embeddingDim).fill(0);
    
    // Simple bag-of-words with positional encoding
    tokens.forEach((token, index) => {
      const hash = this.hashString(token);
      const position = index / tokens.length;
      
      for (let i = 0; i < this.embeddingDim; i++) {
        const dim = (hash + i) % this.embeddingDim;
        embedding[dim] += Math.cos(position * Math.PI + i) / tokens.length;
      }
    });
    
    // Normalize
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / (norm || 1));
  }

  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}

// OpenAI API integration
class OpenAIEmbeddings {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.model = 'text-embedding-ada-002';
    this.maxTokens = 8191;
  }

  async encode(text) {
    if (!this.apiKey) {
      throw new Error('OpenAI API key required');
    }

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          input: text.slice(0, this.maxTokens),
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      return data.data[0].embedding;
    } catch (error) {
      console.error('OpenAI embedding error:', error);
      throw error;
    }
  }
}

// Local transformer model (placeholder for future implementation)
class LocalTransformer {
  constructor() {
    this.model = null;
    this.tokenizer = null;
  }

  async loadModel() {
    // Placeholder for loading ONNX or TensorFlow.js model
    console.log('Local transformer model not implemented yet');
    throw new Error('Local transformer model not available');
  }

  async encode(text) {
    throw new Error('Local transformer model not available');
  }
}

// Main worker logic
let embeddingModel = null;
let currentModelType = null;

async function initializeModel(modelType, apiKey) {
  if (currentModelType === modelType && embeddingModel) {
    return embeddingModel;
  }

  switch (modelType) {
    case 'sentence-transformers':
      embeddingModel = new SimpleSentenceTransformer();
      await embeddingModel.loadModel();
      break;
    
    case 'openai':
      embeddingModel = new OpenAIEmbeddings(apiKey);
      break;
    
    case 'local':
      embeddingModel = new LocalTransformer();
      await embeddingModel.loadModel();
      break;
    
    default:
      throw new Error(`Unknown embedding model: ${modelType}`);
  }

  currentModelType = modelType;
  return embeddingModel;
}

// Message handler
self.onmessage = async function(event) {
  const { type, chunkId, text, model, apiKey } = event.data;

  try {
    switch (type) {
      case 'generate-embedding':
        if (!text) {
          throw new Error('Text is required for embedding generation');
        }

        const embeddingModel = await initializeModel(model || 'sentence-transformers', apiKey);
        const embedding = await embeddingModel.encode(text);

        self.postMessage({
          type: 'embedding-complete',
          chunkId,
          embedding,
        });
        break;

      case 'batch-generate':
        const { texts, batchId } = event.data;
        const batchModel = await initializeModel(model || 'sentence-transformers', apiKey);
        const embeddings = [];

        for (let i = 0; i < texts.length; i++) {
          try {
            const embedding = await batchModel.encode(texts[i]);
            embeddings.push({ index: i, embedding });
            
            // Send progress update
            self.postMessage({
              type: 'batch-progress',
              batchId,
              progress: (i + 1) / texts.length,
              completed: i + 1,
              total: texts.length,
            });
          } catch (error) {
            embeddings.push({ index: i, error: error.message });
          }
        }

        self.postMessage({
          type: 'batch-complete',
          batchId,
          embeddings,
        });
        break;

      case 'similarity-search':
        const { queryEmbedding, candidateEmbeddings, threshold } = event.data;
        const similarities = candidateEmbeddings.map((candidate, index) => ({
          index,
          similarity: cosineSimilarity(queryEmbedding, candidate.embedding),
        }));

        const results = similarities
          .filter(result => result.similarity >= (threshold || 0.7))
          .sort((a, b) => b.similarity - a.similarity);

        self.postMessage({
          type: 'similarity-complete',
          chunkId,
          results,
        });
        break;

      case 'cluster-embeddings':
        const { embeddings: clusterEmbeddings, numClusters } = event.data;
        const clusters = performKMeansClustering(clusterEmbeddings, numClusters || 5);

        self.postMessage({
          type: 'clustering-complete',
          chunkId,
          clusters,
        });
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'embedding-error',
      chunkId,
      error: error.message,
    });
  }
};

// Utility functions
function cosineSimilarity(a, b) {
  if (a.length !== b.length) return 0;
  
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

function performKMeansClustering(embeddings, k) {
  if (embeddings.length === 0 || k <= 0) return [];
  
  const dim = embeddings[0].length;
  const maxIterations = 100;
  
  // Initialize centroids randomly
  let centroids = [];
  for (let i = 0; i < k; i++) {
    const centroid = new Array(dim);
    for (let j = 0; j < dim; j++) {
      centroid[j] = Math.random() * 2 - 1; // Random values between -1 and 1
    }
    centroids.push(centroid);
  }
  
  let assignments = new Array(embeddings.length);
  let converged = false;
  let iteration = 0;
  
  while (!converged && iteration < maxIterations) {
    // Assign points to nearest centroid
    const newAssignments = embeddings.map((embedding, index) => {
      let bestCluster = 0;
      let bestDistance = Infinity;
      
      for (let c = 0; c < k; c++) {
        const distance = euclideanDistance(embedding, centroids[c]);
        if (distance < bestDistance) {
          bestDistance = distance;
          bestCluster = c;
        }
      }
      
      return bestCluster;
    });
    
    // Check for convergence
    converged = newAssignments.every((assignment, index) => assignment === assignments[index]);
    assignments = newAssignments;
    
    // Update centroids
    const newCentroids = new Array(k);
    for (let c = 0; c < k; c++) {
      const clusterPoints = embeddings.filter((_, index) => assignments[index] === c);
      
      if (clusterPoints.length > 0) {
        const centroid = new Array(dim).fill(0);
        clusterPoints.forEach(point => {
          point.forEach((value, index) => {
            centroid[index] += value;
          });
        });
        centroid.forEach((value, index) => {
          centroid[index] = value / clusterPoints.length;
        });
        newCentroids[c] = centroid;
      } else {
        newCentroids[c] = centroids[c]; // Keep old centroid if no points assigned
      }
    }
    
    centroids = newCentroids;
    iteration++;
  }
  
  // Return cluster assignments and centroids
  return {
    assignments,
    centroids,
    iterations: iteration,
    converged,
  };
}

function euclideanDistance(a, b) {
  if (a.length !== b.length) return Infinity;
  
  let sum = 0;
  for (let i = 0; i < a.length; i++) {
    const diff = a[i] - b[i];
    sum += diff * diff;
  }
  
  return Math.sqrt(sum);
}

// Error handling
self.onerror = function(error) {
  console.error('Worker error:', error);
  self.postMessage({
    type: 'worker-error',
    error: error.message,
  });
};

// Initialize
console.log('Embedding worker initialized');
