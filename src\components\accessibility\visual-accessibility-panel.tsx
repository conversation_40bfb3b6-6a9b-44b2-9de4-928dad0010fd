"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Eye, 
  Palette, 
  Type, 
  Contrast, 
  Save,
  Plus,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>c<PERSON>,
  <PERSON>tings,
  Monitor,
  Sun,
  Moon,
  Zap,
  MousePointer,
  Focus,
  Accessibility,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  VisualAccessibilityManager, 
  type VisualAccessibilityConfig,
  type AccessibilityProfile,
  type ColorBlindnessFilter,
} from '@/lib/accessibility/visual-accessibility';

interface VisualAccessibilityPanelProps {
  accessibilityManager: VisualAccessibilityManager;
  className?: string;
}

export default function VisualAccessibilityPanel({
  accessibilityManager,
  className,
}: VisualAccessibilityPanelProps) {
  const [config, setConfig] = useState<VisualAccessibilityConfig>(accessibilityManager.getConfig());
  const [profiles, setProfiles] = useState<AccessibilityProfile[]>([]);
  const [currentProfile, setCurrentProfile] = useState<AccessibilityProfile | null>(null);
  const [showCreateProfile, setShowCreateProfile] = useState(false);
  const [newProfileName, setNewProfileName] = useState('');
  const [newProfileDescription, setNewProfileDescription] = useState('');
  const [colorBlindnessFilters] = useState<ColorBlindnessFilter[]>(accessibilityManager.getColorBlindnessFilters());

  // Load data on mount
  useEffect(() => {
    updateData();
  }, []);

  // Listen for accessibility manager events
  useEffect(() => {
    const handleConfigUpdated = () => updateData();
    const handleProfileLoaded = () => updateData();
    const handleProfileCreated = () => updateData();
    const handleProfileDeleted = () => updateData();

    accessibilityManager.addEventListener('config-updated', handleConfigUpdated);
    accessibilityManager.addEventListener('profile-loaded', handleProfileLoaded);
    accessibilityManager.addEventListener('profile-created', handleProfileCreated);
    accessibilityManager.addEventListener('profile-deleted', handleProfileDeleted);

    return () => {
      accessibilityManager.removeEventListener('config-updated', handleConfigUpdated);
      accessibilityManager.removeEventListener('profile-loaded', handleProfileLoaded);
      accessibilityManager.removeEventListener('profile-created', handleProfileCreated);
      accessibilityManager.removeEventListener('profile-deleted', handleProfileDeleted);
    };
  }, [accessibilityManager]);

  const updateData = useCallback(() => {
    setConfig(accessibilityManager.getConfig());
    setProfiles(accessibilityManager.getProfiles());
    setCurrentProfile(accessibilityManager.getCurrentProfile());
  }, [accessibilityManager]);

  const handleConfigChange = useCallback((updates: Partial<VisualAccessibilityConfig>) => {
    accessibilityManager.updateConfig(updates);
  }, [accessibilityManager]);

  const handleCreateProfile = useCallback(() => {
    if (!newProfileName.trim()) return;

    accessibilityManager.createProfile(newProfileName, newProfileDescription);
    setNewProfileName('');
    setNewProfileDescription('');
    setShowCreateProfile(false);
  }, [accessibilityManager, newProfileName, newProfileDescription]);

  const handleLoadProfile = useCallback((profileId: string) => {
    accessibilityManager.loadProfile(profileId);
  }, [accessibilityManager]);

  const handleDeleteProfile = useCallback((profileId: string) => {
    accessibilityManager.deleteProfile(profileId);
  }, [accessibilityManager]);

  const handleResetToDefaults = useCallback(() => {
    accessibilityManager.resetToDefaults();
  }, [accessibilityManager]);

  const ColorPicker = ({ label, value, onChange }: { label: string; value: string; onChange: (value: string) => void }) => (
    <div className="flex items-center gap-2">
      <Label className="text-sm">{label}</Label>
      <div className="flex items-center gap-2">
        <div
          className="w-8 h-8 rounded border-2 border-gray-300 cursor-pointer"
          style={{ backgroundColor: value }}
          onClick={() => {
            const input = document.createElement('input');
            input.type = 'color';
            input.value = value;
            input.onchange = (e) => onChange((e.target as HTMLInputElement).value);
            input.click();
          }}
        />
        <Input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-20 text-xs"
        />
      </div>
    </div>
  );

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Visual Accessibility
          </CardTitle>
          <CardDescription>
            Customize visual settings for enhanced accessibility and comfort
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="display" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="display">Display</TabsTrigger>
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="profiles">Profiles</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="display" className="space-y-4">
          {/* Font and Text Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Type className="h-5 w-5" />
                Font & Text
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Font Size: {Math.round(config.fontScaling * 100)}%</Label>
                <Slider
                  value={[config.fontScaling]}
                  onValueChange={([value]) => handleConfigChange({ fontScaling: value })}
                  min={0.5}
                  max={3.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Line Height: {config.lineHeight.toFixed(1)}</Label>
                <Slider
                  value={[config.lineHeight]}
                  onValueChange={([value]) => handleConfigChange({ lineHeight: value })}
                  min={1.0}
                  max={3.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Letter Spacing: {config.letterSpacing}px</Label>
                <Slider
                  value={[config.letterSpacing]}
                  onValueChange={([value]) => handleConfigChange({ letterSpacing: value })}
                  min={0}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="dyslexia-font">Dyslexia-Friendly Font</Label>
                <Switch
                  id="dyslexia-font"
                  checked={config.enableDyslexiaFont}
                  onCheckedChange={(checked) => handleConfigChange({ enableDyslexiaFont: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="text-shadow">Text Shadow</Label>
                <Switch
                  id="text-shadow"
                  checked={config.textShadow}
                  onCheckedChange={(checked) => handleConfigChange({ textShadow: checked })}
                />
              </div>
            </CardContent>
          </Card>

          {/* High Contrast Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Contrast className="h-5 w-5" />
                Contrast & Visibility
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>High Contrast Mode</Label>
                <Select
                  value={config.highContrastMode}
                  onValueChange={(value: any) => handleConfigChange({ highContrastMode: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        None
                      </div>
                    </SelectItem>
                    <SelectItem value="light">
                      <div className="flex items-center gap-2">
                        <Sun className="h-4 w-4" />
                        Light
                      </div>
                    </SelectItem>
                    <SelectItem value="dark">
                      <div className="flex items-center gap-2">
                        <Moon className="h-4 w-4" />
                        Dark
                      </div>
                    </SelectItem>
                    <SelectItem value="custom">
                      <div className="flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Custom
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Brightness: {config.brightness}%</Label>
                <Slider
                  value={[config.brightness]}
                  onValueChange={([value]) => handleConfigChange({ brightness: value })}
                  min={50}
                  max={150}
                  step={5}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Contrast: {config.contrast}%</Label>
                <Slider
                  value={[config.contrast]}
                  onValueChange={([value]) => handleConfigChange({ contrast: value })}
                  min={50}
                  max={200}
                  step={5}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Saturation: {config.saturation}%</Label>
                <Slider
                  value={[config.saturation]}
                  onValueChange={([value]) => handleConfigChange({ saturation: value })}
                  min={0}
                  max={200}
                  step={5}
                  className="w-full"
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="invert-colors">Invert Colors</Label>
                <Switch
                  id="invert-colors"
                  checked={config.invertColors}
                  onCheckedChange={(checked) => handleConfigChange({ invertColors: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colors" className="space-y-4">
          {/* Color Scheme */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Palette className="h-5 w-5" />
                Color Scheme
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Color Blindness Support</Label>
                <Select
                  value={config.colorScheme}
                  onValueChange={(value: any) => handleConfigChange({ colorScheme: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="protanopia">Protanopia (Red-blind)</SelectItem>
                    <SelectItem value="deuteranopia">Deuteranopia (Green-blind)</SelectItem>
                    <SelectItem value="tritanopia">Tritanopia (Blue-blind)</SelectItem>
                    <SelectItem value="monochrome">Monochrome</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.colorScheme !== 'default' && config.colorScheme !== 'monochrome' && (
                <div className="text-sm text-muted-foreground">
                  {colorBlindnessFilters.find(f => f.type === config.colorScheme)?.description}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Custom Colors */}
          {(config.highContrastMode === 'custom' || config.colorScheme === 'custom') && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Custom Colors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ColorPicker
                  label="Background"
                  value={config.customColors.background}
                  onChange={(value) => handleConfigChange({
                    customColors: { ...config.customColors, background: value }
                  })}
                />
                
                <ColorPicker
                  label="Text"
                  value={config.customColors.text}
                  onChange={(value) => handleConfigChange({
                    customColors: { ...config.customColors, text: value }
                  })}
                />
                
                <ColorPicker
                  label="Accent"
                  value={config.customColors.accent}
                  onChange={(value) => handleConfigChange({
                    customColors: { ...config.customColors, accent: value }
                  })}
                />
                
                <ColorPicker
                  label="Border"
                  value={config.customColors.border}
                  onChange={(value) => handleConfigChange({
                    customColors: { ...config.customColors, border: value }
                  })}
                />
                
                <ColorPicker
                  label="Highlight"
                  value={config.customColors.highlight}
                  onChange={(value) => handleConfigChange({
                    customColors: { ...config.customColors, highlight: value }
                  })}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="profiles" className="space-y-4">
          {/* Profile Management */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Accessibility Profiles</h3>
            
            <Dialog open={showCreateProfile} onOpenChange={setShowCreateProfile}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Profile
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Accessibility Profile</DialogTitle>
                  <DialogDescription>
                    Save your current settings as a new profile
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="profile-name">Profile Name</Label>
                    <Input
                      id="profile-name"
                      value={newProfileName}
                      onChange={(e) => setNewProfileName(e.target.value)}
                      placeholder="Enter profile name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="profile-description">Description</Label>
                    <Input
                      id="profile-description"
                      value={newProfileDescription}
                      onChange={(e) => setNewProfileDescription(e.target.value)}
                      placeholder="Enter profile description"
                    />
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowCreateProfile(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateProfile}>
                      Create Profile
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Profiles List */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {profiles.map((profile) => (
              <Card
                key={profile.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-muted/50",
                  currentProfile?.id === profile.id && "ring-2 ring-primary"
                )}
                onClick={() => handleLoadProfile(profile.id)}
              >
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium">{profile.name}</h4>
                        <p className="text-sm text-muted-foreground">{profile.description}</p>
                      </div>
                      
                      {!profile.id.startsWith('predefined-') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteProfile(profile.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      {profile.isDefault && <Badge variant="secondary">Default</Badge>}
                      {profile.id.startsWith('predefined-') && <Badge variant="outline">System</Badge>}
                      <span>Last used: {profile.lastUsed.toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          {/* Advanced Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="h-5 w-5" />
                Advanced Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="reduced-motion">Reduce Motion</Label>
                <Switch
                  id="reduced-motion"
                  checked={config.reducedMotion}
                  onCheckedChange={(checked) => handleConfigChange({ reducedMotion: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="cursor-enhancement">Enhanced Cursor</Label>
                <Switch
                  id="cursor-enhancement"
                  checked={config.cursorEnhancement}
                  onCheckedChange={(checked) => handleConfigChange({ cursorEnhancement: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="reading-guide">Reading Guide</Label>
                <Switch
                  id="reading-guide"
                  checked={config.enableReadingGuide}
                  onCheckedChange={(checked) => handleConfigChange({ enableReadingGuide: checked })}
                />
              </div>

              {config.enableReadingGuide && (
                <div className="ml-4 space-y-4">
                  <ColorPicker
                    label="Guide Color"
                    value={config.readingGuideColor}
                    onChange={(value) => handleConfigChange({ readingGuideColor: value })}
                  />
                  
                  <div className="space-y-2">
                    <Label>Guide Opacity: {Math.round(config.readingGuideOpacity * 100)}%</Label>
                    <Slider
                      value={[config.readingGuideOpacity]}
                      onValueChange={([value]) => handleConfigChange({ readingGuideOpacity: value })}
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label>Focus Indicator Style</Label>
                <Select
                  value={config.focusIndicatorStyle}
                  onValueChange={(value: any) => handleConfigChange({ focusIndicatorStyle: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="enhanced">Enhanced</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Reset Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Reset Options</CardTitle>
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                onClick={handleResetToDefaults}
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Default Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
