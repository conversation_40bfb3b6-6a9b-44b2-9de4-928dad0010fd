/**
 * PDF Error Handler & Recovery System
 * Comprehensive error handling with automatic recovery, fallbacks, and user guidance
 */

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
export type ErrorCategory = 'network' | 'parsing' | 'rendering' | 'memory' | 'security' | 'compatibility' | 'user' | 'unknown';

export interface PDFError {
  id: string;
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: number;
  context: Record<string, any>;
  stack?: string;
  recoverable: boolean;
  userMessage: string;
  suggestedActions: string[];
  technicalDetails?: string;
}

export interface RecoveryStrategy {
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  fallback?: RecoveryStrategy;
  maxAttempts: number;
  retryDelay: number;
}

export interface ErrorHandlerConfig {
  enableAutoRecovery: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  enableFallbacks: boolean;
  enableTelemetry: boolean;
  enableUserNotifications: boolean;
  logLevel: 'none' | 'error' | 'warn' | 'info' | 'debug';
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  recoverySuccessRate: number;
  averageRecoveryTime: number;
  recentErrors: PDFError[];
}

const DEFAULT_CONFIG: ErrorHandlerConfig = {
  enableAutoRecovery: true,
  maxRetryAttempts: 3,
  retryDelay: 1000,
  enableFallbacks: true,
  enableTelemetry: true,
  enableUserNotifications: true,
  logLevel: 'warn',
};

const ERROR_CODES = {
  // Network errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_OFFLINE: 'NETWORK_OFFLINE',
  NETWORK_CORS: 'NETWORK_CORS',
  NETWORK_NOT_FOUND: 'NETWORK_NOT_FOUND',
  NETWORK_UNAUTHORIZED: 'NETWORK_UNAUTHORIZED',
  
  // Parsing errors
  INVALID_PDF: 'INVALID_PDF',
  CORRUPTED_PDF: 'CORRUPTED_PDF',
  UNSUPPORTED_VERSION: 'UNSUPPORTED_VERSION',
  ENCRYPTED_PDF: 'ENCRYPTED_PDF',
  MISSING_XREF: 'MISSING_XREF',
  
  // Rendering errors
  RENDER_FAILED: 'RENDER_FAILED',
  CANVAS_ERROR: 'CANVAS_ERROR',
  WEBGL_ERROR: 'WEBGL_ERROR',
  FONT_LOADING_FAILED: 'FONT_LOADING_FAILED',
  
  // Memory errors
  OUT_OF_MEMORY: 'OUT_OF_MEMORY',
  MEMORY_PRESSURE: 'MEMORY_PRESSURE',
  CACHE_OVERFLOW: 'CACHE_OVERFLOW',
  
  // Security errors
  CSP_VIOLATION: 'CSP_VIOLATION',
  UNSAFE_CONTENT: 'UNSAFE_CONTENT',
  
  // Compatibility errors
  BROWSER_UNSUPPORTED: 'BROWSER_UNSUPPORTED',
  FEATURE_UNSUPPORTED: 'FEATURE_UNSUPPORTED',
  
  // User errors
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  
  // Unknown errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export class PDFErrorHandler {
  private config: ErrorHandlerConfig;
  private errors: PDFError[] = [];
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();
  private metrics: ErrorMetrics;
  private eventListeners: Map<string, Set<(error: PDFError) => void>> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.metrics = this.initializeMetrics();
    this.setupRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  private initializeMetrics(): ErrorMetrics {
    return {
      totalErrors: 0,
      errorsByCategory: {
        network: 0,
        parsing: 0,
        rendering: 0,
        memory: 0,
        security: 0,
        compatibility: 0,
        user: 0,
        unknown: 0,
      },
      errorsBySeverity: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0,
      },
      recoverySuccessRate: 0,
      averageRecoveryTime: 0,
      recentErrors: [],
    };
  }

  private setupRecoveryStrategies(): void {
    // Network recovery strategies
    this.addRecoveryStrategy('network-retry', {
      name: 'Network Retry',
      description: 'Retry network request with exponential backoff',
      execute: async () => {
        await this.delay(this.config.retryDelay);
        return true; // Simplified - would implement actual retry logic
      },
      maxAttempts: 3,
      retryDelay: 1000,
    });

    // Memory recovery strategies
    this.addRecoveryStrategy('memory-cleanup', {
      name: 'Memory Cleanup',
      description: 'Clear caches and free memory',
      execute: async () => {
        // Trigger garbage collection if available
        if (window.gc) {
          window.gc();
        }
        return true;
      },
      maxAttempts: 1,
      retryDelay: 0,
    });

    // Rendering recovery strategies
    this.addRecoveryStrategy('fallback-renderer', {
      name: 'Fallback Renderer',
      description: 'Switch to software rendering',
      execute: async () => {
        // Switch to canvas 2D rendering
        return true;
      },
      maxAttempts: 1,
      retryDelay: 0,
    });

    // PDF parsing recovery strategies
    this.addRecoveryStrategy('repair-pdf', {
      name: 'PDF Repair',
      description: 'Attempt to repair corrupted PDF',
      execute: async () => {
        // Implement PDF repair logic
        return false; // Not implemented in this example
      },
      maxAttempts: 1,
      retryDelay: 0,
    });
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, {
        source: 'unhandledrejection',
        promise: event.promise,
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error, {
        source: 'global',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });
  }

  public async handleError(error: any, context: Record<string, any> = {}): Promise<PDFError> {
    const pdfError = this.createPDFError(error, context);
    this.errors.push(pdfError);
    this.updateMetrics(pdfError);

    // Log error
    this.logError(pdfError);

    // Emit error event
    this.emit('error', pdfError);

    // Attempt recovery if enabled and error is recoverable
    if (this.config.enableAutoRecovery && pdfError.recoverable) {
      await this.attemptRecovery(pdfError);
    }

    // Notify user if enabled
    if (this.config.enableUserNotifications) {
      this.notifyUser(pdfError);
    }

    return pdfError;
  }

  private createPDFError(error: any, context: Record<string, any>): PDFError {
    const errorInfo = this.analyzeError(error);
    
    return {
      id: this.generateErrorId(),
      code: errorInfo.code,
      message: error?.message || 'Unknown error',
      category: errorInfo.category,
      severity: errorInfo.severity,
      timestamp: Date.now(),
      context,
      stack: error?.stack,
      recoverable: errorInfo.recoverable,
      userMessage: errorInfo.userMessage,
      suggestedActions: errorInfo.suggestedActions,
      technicalDetails: errorInfo.technicalDetails,
    };
  }

  private analyzeError(error: any): {
    code: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    recoverable: boolean;
    userMessage: string;
    suggestedActions: string[];
    technicalDetails?: string;
  } {
    const message = error?.message || '';
    const name = error?.name || '';

    // Network errors
    if (message.includes('fetch') || message.includes('network') || name === 'NetworkError') {
      if (message.includes('timeout')) {
        return {
          code: ERROR_CODES.NETWORK_TIMEOUT,
          category: 'network',
          severity: 'medium',
          recoverable: true,
          userMessage: 'The document is taking too long to load. Please check your internet connection.',
          suggestedActions: ['Check internet connection', 'Try again', 'Use a different network'],
        };
      }
      
      if (message.includes('CORS')) {
        return {
          code: ERROR_CODES.NETWORK_CORS,
          category: 'network',
          severity: 'high',
          recoverable: false,
          userMessage: 'Cannot access the document due to security restrictions.',
          suggestedActions: ['Contact administrator', 'Use a different document source'],
        };
      }

      return {
        code: ERROR_CODES.NETWORK_NOT_FOUND,
        category: 'network',
        severity: 'medium',
        recoverable: true,
        userMessage: 'The document could not be found or loaded.',
        suggestedActions: ['Check document URL', 'Verify file exists', 'Try again later'],
      };
    }

    // PDF parsing errors
    if (message.includes('Invalid PDF') || message.includes('corrupted')) {
      return {
        code: ERROR_CODES.INVALID_PDF,
        category: 'parsing',
        severity: 'high',
        recoverable: false,
        userMessage: 'The document appears to be corrupted or invalid.',
        suggestedActions: ['Try a different document', 'Re-download the file', 'Contact document provider'],
      };
    }

    // Memory errors
    if (message.includes('memory') || name === 'RangeError') {
      return {
        code: ERROR_CODES.OUT_OF_MEMORY,
        category: 'memory',
        severity: 'high',
        recoverable: true,
        userMessage: 'Not enough memory to process this document.',
        suggestedActions: ['Close other tabs', 'Try a smaller document', 'Restart browser'],
      };
    }

    // Rendering errors
    if (message.includes('canvas') || message.includes('WebGL')) {
      return {
        code: ERROR_CODES.RENDER_FAILED,
        category: 'rendering',
        severity: 'medium',
        recoverable: true,
        userMessage: 'There was a problem displaying the document.',
        suggestedActions: ['Refresh the page', 'Try a different browser', 'Disable hardware acceleration'],
      };
    }

    // Default unknown error
    return {
      code: ERROR_CODES.UNKNOWN_ERROR,
      category: 'unknown',
      severity: 'medium',
      recoverable: true,
      userMessage: 'An unexpected error occurred while processing the document.',
      suggestedActions: ['Refresh the page', 'Try again', 'Contact support if problem persists'],
      technicalDetails: `${name}: ${message}`,
    };
  }

  private async attemptRecovery(error: PDFError): Promise<boolean> {
    const strategy = this.getRecoveryStrategy(error);
    if (!strategy) return false;

    const startTime = Date.now();
    let attempts = 0;
    let success = false;

    while (attempts < strategy.maxAttempts && !success) {
      attempts++;
      
      try {
        this.log('info', `Attempting recovery: ${strategy.name} (attempt ${attempts})`);
        success = await strategy.execute();
        
        if (success) {
          const recoveryTime = Date.now() - startTime;
          this.log('info', `Recovery successful: ${strategy.name} (${recoveryTime}ms)`);
          this.updateRecoveryMetrics(true, recoveryTime);
          this.emit('recovery-success', { error, strategy, attempts, recoveryTime });
          return true;
        }
      } catch (recoveryError) {
        this.log('warn', `Recovery attempt failed: ${recoveryError}`);
      }

      if (attempts < strategy.maxAttempts) {
        await this.delay(strategy.retryDelay * attempts); // Exponential backoff
      }
    }

    // Try fallback strategy if available
    if (strategy.fallback && this.config.enableFallbacks) {
      this.log('info', `Trying fallback strategy: ${strategy.fallback.name}`);
      success = await this.attemptRecoveryWithStrategy(error, strategy.fallback);
    }

    if (!success) {
      this.updateRecoveryMetrics(false, Date.now() - startTime);
      this.emit('recovery-failed', { error, strategy, attempts });
    }

    return success;
  }

  private async attemptRecoveryWithStrategy(error: PDFError, strategy: RecoveryStrategy): Promise<boolean> {
    try {
      return await strategy.execute();
    } catch (recoveryError) {
      this.log('warn', `Fallback recovery failed: ${recoveryError}`);
      return false;
    }
  }

  private getRecoveryStrategy(error: PDFError): RecoveryStrategy | null {
    switch (error.category) {
      case 'network':
        return this.recoveryStrategies.get('network-retry') || null;
      case 'memory':
        return this.recoveryStrategies.get('memory-cleanup') || null;
      case 'rendering':
        return this.recoveryStrategies.get('fallback-renderer') || null;
      case 'parsing':
        return this.recoveryStrategies.get('repair-pdf') || null;
      default:
        return null;
    }
  }

  private updateMetrics(error: PDFError): void {
    this.metrics.totalErrors++;
    this.metrics.errorsByCategory[error.category]++;
    this.metrics.errorsBySeverity[error.severity]++;
    
    // Keep only recent errors
    this.metrics.recentErrors.unshift(error);
    if (this.metrics.recentErrors.length > 50) {
      this.metrics.recentErrors = this.metrics.recentErrors.slice(0, 50);
    }
  }

  private updateRecoveryMetrics(success: boolean, recoveryTime: number): void {
    // Update recovery success rate and average time
    // Simplified implementation
    if (success) {
      this.metrics.averageRecoveryTime = (this.metrics.averageRecoveryTime + recoveryTime) / 2;
    }
  }

  private notifyUser(error: PDFError): void {
    // Emit user notification event
    this.emit('user-notification', {
      type: 'error',
      severity: error.severity,
      message: error.userMessage,
      actions: error.suggestedActions,
      dismissible: error.severity !== 'critical',
    });
  }

  private logError(error: PDFError): void {
    const logLevel = this.getLogLevel(error.severity);
    this.log(logLevel, `PDF Error [${error.code}]: ${error.message}`, {
      category: error.category,
      severity: error.severity,
      context: error.context,
      stack: error.stack,
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' | 'debug' {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      case 'low':
        return 'info';
      default:
        return 'debug';
    }
  }

  private log(level: string, message: string, data?: any): void {
    if (this.shouldLog(level)) {
      const logMethod = console[level as keyof Console] || console.log;
      if (data) {
        (logMethod as any)(message, data);
      } else {
        (logMethod as any)(message);
      }
    }
  }

  private shouldLog(level: string): boolean {
    const levels = ['none', 'error', 'warn', 'info', 'debug'];
    const configLevel = levels.indexOf(this.config.logLevel);
    const messageLevel = levels.indexOf(level);
    return messageLevel <= configLevel;
  }

  private generateErrorId(): string {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public addRecoveryStrategy(name: string, strategy: RecoveryStrategy): void {
    this.recoveryStrategies.set(name, strategy);
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Error in error handler event listener:', error);
      }
    });
  }

  public getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  public getRecentErrors(count = 10): PDFError[] {
    return this.metrics.recentErrors.slice(0, count);
  }

  public clearErrors(): void {
    this.errors = [];
    this.metrics.recentErrors = [];
  }

  public updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public destroy(): void {
    this.eventListeners.clear();
    this.recoveryStrategies.clear();
    this.errors = [];
  }
}
