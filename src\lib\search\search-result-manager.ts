/**
 * Search Result Management System
 * Handles search result persistence, history, and advanced result manipulation
 */

export interface SearchResultItem {
  id: string;
  query: string;
  documentId: string;
  documentTitle: string;
  pageNumber: number;
  content: string;
  matchText: string;
  context: string;
  relevanceScore: number;
  timestamp: Date;
  metadata: {
    searchType: 'text' | 'semantic' | 'regex' | 'fuzzy';
    filters: Record<string, any>;
    position: { x: number; y: number };
    bounds: { x: number; y: number; width: number; height: number };
    surroundingText: string;
  };
}

export interface SearchSession {
  id: string;
  query: string;
  searchType: 'text' | 'semantic' | 'regex' | 'fuzzy';
  filters: Record<string, any>;
  results: SearchResultItem[];
  timestamp: Date;
  duration: number; // in milliseconds
  resultCount: number;
  selectedResults: string[]; // result IDs
  notes: string;
  tags: string[];
}

export interface SearchCollection {
  id: string;
  name: string;
  description: string;
  results: SearchResultItem[];
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  isPublic: boolean;
  metadata: {
    totalResults: number;
    documentsCount: number;
    averageRelevance: number;
    searchTypes: string[];
  };
}

export interface SearchAnalytics {
  totalSearches: number;
  uniqueQueries: number;
  averageResultsPerSearch: number;
  mostSearchedTerms: Array<{ term: string; count: number }>;
  searchTypeDistribution: Record<string, number>;
  documentPopularity: Array<{ documentId: string; searchCount: number }>;
  timeSpentSearching: number;
  successRate: number; // percentage of searches that yielded results
}

export interface SearchResultManagerConfig {
  maxHistorySize: number;
  maxCollections: number;
  enablePersistence: boolean;
  enableAnalytics: boolean;
  autoSaveInterval: number; // in milliseconds
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

const DEFAULT_CONFIG: SearchResultManagerConfig = {
  maxHistorySize: 1000,
  maxCollections: 50,
  enablePersistence: true,
  enableAnalytics: true,
  autoSaveInterval: 30000, // 30 seconds
  compressionEnabled: true,
  encryptionEnabled: false,
};

export class SearchResultManager {
  private config: SearchResultManagerConfig;
  private searchHistory: SearchSession[] = [];
  private collections: Map<string, SearchCollection> = new Map();
  private analytics: SearchAnalytics;
  private currentSession: SearchSession | null = null;
  private autoSaveTimer: number | null = null;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(config: Partial<SearchResultManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.analytics = this.initializeAnalytics();
    
    this.loadFromStorage();
    this.setupAutoSave();
  }

  private initializeAnalytics(): SearchAnalytics {
    return {
      totalSearches: 0,
      uniqueQueries: 0,
      averageResultsPerSearch: 0,
      mostSearchedTerms: [],
      searchTypeDistribution: {},
      documentPopularity: [],
      timeSpentSearching: 0,
      successRate: 0,
    };
  }

  private loadFromStorage(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') return;

    try {
      const historyData = localStorage.getItem('search-result-history');
      if (historyData) {
        const parsed = JSON.parse(historyData);
        if (Array.isArray(parsed)) {
          this.searchHistory = parsed.map((session: any) => ({
            ...session,
            timestamp: new Date(session.timestamp || Date.now()),
            results: Array.isArray(session.results) ? session.results.map((result: any) => ({
              ...result,
              timestamp: new Date(result.timestamp || Date.now()),
            })) : [],
          }));
        }
      }

      const collectionsData = localStorage.getItem('search-result-collections');
      if (collectionsData) {
        const parsed = JSON.parse(collectionsData);
        if (parsed && typeof parsed === 'object') {
          Object.entries(parsed).forEach(([id, collection]: [string, any]) => {
            if (collection && typeof collection === 'object') {
              this.collections.set(id, {
                ...collection,
                createdAt: new Date(collection.createdAt || Date.now()),
                updatedAt: new Date(collection.updatedAt || Date.now()),
                results: Array.isArray(collection.results) ? collection.results.map((result: any) => ({
                  ...result,
                  timestamp: new Date(result.timestamp || Date.now()),
                })) : [],
              });
            }
          });
        }
      }

      const analyticsData = localStorage.getItem('search-analytics');
      if (analyticsData) {
        const analytics = JSON.parse(analyticsData);
        if (analytics && typeof analytics === 'object') {
          this.analytics = analytics;
        }
      }
    } catch (error) {
      console.warn('Failed to load search data from storage:', error);
    }
  }

  private saveToStorage(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') return;

    try {
      localStorage.setItem('search-result-history', JSON.stringify(this.searchHistory));

      const collectionsData = Object.fromEntries(this.collections.entries());
      localStorage.setItem('search-result-collections', JSON.stringify(collectionsData));

      if (this.config.enableAnalytics) {
        localStorage.setItem('search-analytics', JSON.stringify(this.analytics));
      }
    } catch (error) {
      console.warn('Failed to save search data to storage:', error);
    }
  }

  private setupAutoSave(): void {
    if (typeof window === 'undefined') return;

    if (this.config.autoSaveInterval > 0) {
      this.autoSaveTimer = window.setInterval(() => {
        this.saveToStorage();
      }, this.config.autoSaveInterval);
    }

    window.addEventListener('beforeunload', () => {
      this.endCurrentSession();
      this.saveToStorage();
    });
  }

  public startSearchSession(
    query: string,
    searchType: SearchSession['searchType'],
    filters: Record<string, any> = {}
  ): string {
    this.endCurrentSession();

    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentSession = {
      id: sessionId,
      query,
      searchType,
      filters,
      results: [],
      timestamp: new Date(),
      duration: 0,
      resultCount: 0,
      selectedResults: [],
      notes: '',
      tags: [],
    };

    this.emit('session-started', { session: this.currentSession });
    return sessionId;
  }

  public endCurrentSession(): void {
    if (!this.currentSession) return;

    this.currentSession.duration = Date.now() - this.currentSession.timestamp.getTime();
    this.currentSession.resultCount = this.currentSession.results.length;

    // Add to history
    this.searchHistory.unshift(this.currentSession);
    
    // Limit history size
    if (this.searchHistory.length > this.config.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.config.maxHistorySize);
    }

    // Update analytics
    this.updateAnalytics(this.currentSession);

    this.emit('session-ended', { session: this.currentSession });
    this.currentSession = null;
  }

  public addSearchResults(results: SearchResultItem[]): void {
    if (!this.currentSession) return;

    this.currentSession.results.push(...results);
    this.emit('results-added', { results, sessionId: this.currentSession.id });
  }

  public selectResult(resultId: string): void {
    if (!this.currentSession) return;

    if (!this.currentSession.selectedResults.includes(resultId)) {
      this.currentSession.selectedResults.push(resultId);
      this.emit('result-selected', { resultId, sessionId: this.currentSession.id });
    }
  }

  public deselectResult(resultId: string): void {
    if (!this.currentSession) return;

    const index = this.currentSession.selectedResults.indexOf(resultId);
    if (index !== -1) {
      this.currentSession.selectedResults.splice(index, 1);
      this.emit('result-deselected', { resultId, sessionId: this.currentSession.id });
    }
  }

  public addSessionNotes(notes: string): void {
    if (!this.currentSession) return;

    this.currentSession.notes = notes;
    this.emit('notes-updated', { notes, sessionId: this.currentSession.id });
  }

  public addSessionTags(tags: string[]): void {
    if (!this.currentSession) return;

    this.currentSession.tags = [...new Set([...this.currentSession.tags, ...tags])];
    this.emit('tags-updated', { tags: this.currentSession.tags, sessionId: this.currentSession.id });
  }

  public getSearchHistory(limit?: number): SearchSession[] {
    return limit ? this.searchHistory.slice(0, limit) : [...this.searchHistory];
  }

  public getSessionById(sessionId: string): SearchSession | null {
    return this.searchHistory.find(session => session.id === sessionId) || null;
  }

  public searchInHistory(query: string): SearchSession[] {
    const lowerQuery = query.toLowerCase();
    return this.searchHistory.filter(session =>
      session.query.toLowerCase().includes(lowerQuery) ||
      session.notes.toLowerCase().includes(lowerQuery) ||
      session.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  public createCollection(
    name: string,
    description: string,
    resultIds: string[],
    tags: string[] = []
  ): string {
    if (this.collections.size >= this.config.maxCollections) {
      throw new Error('Maximum number of collections reached');
    }

    const collectionId = `collection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Gather results from history
    const results: SearchResultItem[] = [];
    this.searchHistory.forEach(session => {
      session.results.forEach(result => {
        if (resultIds.includes(result.id)) {
          results.push(result);
        }
      });
    });

    const collection: SearchCollection = {
      id: collectionId,
      name,
      description,
      results,
      createdAt: new Date(),
      updatedAt: new Date(),
      tags,
      isPublic: false,
      metadata: {
        totalResults: results.length,
        documentsCount: new Set(results.map(r => r.documentId)).size,
        averageRelevance: results.reduce((sum, r) => sum + r.relevanceScore, 0) / results.length,
        searchTypes: [...new Set(results.map(r => r.metadata.searchType))],
      },
    };

    this.collections.set(collectionId, collection);
    this.emit('collection-created', { collection });
    
    return collectionId;
  }

  public updateCollection(
    collectionId: string,
    updates: Partial<Pick<SearchCollection, 'name' | 'description' | 'tags' | 'isPublic'>>
  ): void {
    const collection = this.collections.get(collectionId);
    if (!collection) return;

    Object.assign(collection, updates);
    collection.updatedAt = new Date();
    
    this.emit('collection-updated', { collection });
  }

  public addResultsToCollection(collectionId: string, resultIds: string[]): void {
    const collection = this.collections.get(collectionId);
    if (!collection) return;

    // Find results in history
    const newResults: SearchResultItem[] = [];
    this.searchHistory.forEach(session => {
      session.results.forEach(result => {
        if (resultIds.includes(result.id) && 
            !collection.results.some(r => r.id === result.id)) {
          newResults.push(result);
        }
      });
    });

    collection.results.push(...newResults);
    collection.updatedAt = new Date();
    
    // Update metadata
    collection.metadata.totalResults = collection.results.length;
    collection.metadata.documentsCount = new Set(collection.results.map(r => r.documentId)).size;
    collection.metadata.averageRelevance = 
      collection.results.reduce((sum, r) => sum + r.relevanceScore, 0) / collection.results.length;

    this.emit('collection-results-added', { collectionId, newResults });
  }

  public removeResultsFromCollection(collectionId: string, resultIds: string[]): void {
    const collection = this.collections.get(collectionId);
    if (!collection) return;

    collection.results = collection.results.filter(result => !resultIds.includes(result.id));
    collection.updatedAt = new Date();
    
    // Update metadata
    collection.metadata.totalResults = collection.results.length;
    if (collection.results.length > 0) {
      collection.metadata.documentsCount = new Set(collection.results.map(r => r.documentId)).size;
      collection.metadata.averageRelevance = 
        collection.results.reduce((sum, r) => sum + r.relevanceScore, 0) / collection.results.length;
    }

    this.emit('collection-results-removed', { collectionId, removedIds: resultIds });
  }

  public deleteCollection(collectionId: string): void {
    const collection = this.collections.get(collectionId);
    if (collection) {
      this.collections.delete(collectionId);
      this.emit('collection-deleted', { collection });
    }
  }

  public getCollections(): SearchCollection[] {
    return Array.from(this.collections.values()).sort((a, b) => 
      b.updatedAt.getTime() - a.updatedAt.getTime()
    );
  }

  public getCollectionById(collectionId: string): SearchCollection | null {
    return this.collections.get(collectionId) || null;
  }

  public exportCollection(collectionId: string): any {
    const collection = this.collections.get(collectionId);
    if (!collection) return null;

    return {
      ...collection,
      exportDate: new Date().toISOString(),
      version: '1.0',
    };
  }

  public importCollection(data: any): string {
    const collectionId = `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const collection: SearchCollection = {
      ...data,
      id: collectionId,
      createdAt: new Date(),
      updatedAt: new Date(),
      results: data.results.map((result: any) => ({
        ...result,
        timestamp: new Date(result.timestamp),
      })),
    };

    this.collections.set(collectionId, collection);
    this.emit('collection-imported', { collection });
    
    return collectionId;
  }

  private updateAnalytics(session: SearchSession): void {
    if (!this.config.enableAnalytics) return;

    this.analytics.totalSearches++;
    this.analytics.timeSpentSearching += session.duration;
    
    if (session.resultCount > 0) {
      this.analytics.averageResultsPerSearch = 
        (this.analytics.averageResultsPerSearch * (this.analytics.totalSearches - 1) + session.resultCount) / 
        this.analytics.totalSearches;
    }

    // Update search type distribution
    this.analytics.searchTypeDistribution[session.searchType] = 
      (this.analytics.searchTypeDistribution[session.searchType] || 0) + 1;

    // Update success rate
    const successfulSearches = this.searchHistory.filter(s => s.resultCount > 0).length;
    this.analytics.successRate = (successfulSearches / this.analytics.totalSearches) * 100;

    // Update unique queries
    const uniqueQueries = new Set(this.searchHistory.map(s => s.query.toLowerCase()));
    this.analytics.uniqueQueries = uniqueQueries.size;

    // Update most searched terms
    const termCounts = new Map<string, number>();
    this.searchHistory.forEach(s => {
      const normalizedQuery = s.query.toLowerCase().trim();
      termCounts.set(normalizedQuery, (termCounts.get(normalizedQuery) || 0) + 1);
    });
    
    this.analytics.mostSearchedTerms = Array.from(termCounts.entries())
      .map(([term, count]) => ({ term, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);

    // Update document popularity
    const docCounts = new Map<string, number>();
    this.searchHistory.forEach(session => {
      session.results.forEach(result => {
        docCounts.set(result.documentId, (docCounts.get(result.documentId) || 0) + 1);
      });
    });
    
    this.analytics.documentPopularity = Array.from(docCounts.entries())
      .map(([documentId, searchCount]) => ({ documentId, searchCount }))
      .sort((a, b) => b.searchCount - a.searchCount)
      .slice(0, 10);
  }

  public getAnalytics(): SearchAnalytics {
    return { ...this.analytics };
  }

  public clearHistory(): void {
    this.searchHistory = [];
    this.analytics = this.initializeAnalytics();
    this.emit('history-cleared', {});
  }

  public clearCollections(): void {
    this.collections.clear();
    this.emit('collections-cleared', {});
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Search result manager event listener error:', error);
      }
    });
  }

  public destroy(): void {
    this.endCurrentSession();
    this.saveToStorage();
    
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    
    this.eventListeners.clear();
  }
}
