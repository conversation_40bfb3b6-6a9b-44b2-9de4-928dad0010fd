import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AnnotationEngine, type AdvancedAnnotation, type AnnotationStyle } from '@/lib/annotations/annotation-engine';

// Mock browser APIs
Object.defineProperty(global, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
});

Object.defineProperty(global, 'navigator', {
  value: {
    clipboard: {
      writeText: vi.fn().mockResolvedValue(undefined),
    },
  },
});

describe('AnnotationEngine', () => {
  let engine: AnnotationEngine;

  beforeEach(() => {
    vi.clearAllMocks();
    engine = new AnnotationEngine();
  });

  describe('Annotation Creation', () => {
    it('should create a basic annotation', () => {
      const annotation = engine.createAnnotation(
        'rectangle',
        1,
        { x: 100, y: 200 }
      );

      expect(annotation).toMatchObject({
        type: 'rectangle',
        pageNumber: 1,
        x: 100,
        y: 200,
        width: 100,
        height: 60,
        color: '#FF6B6B',
        strokeWidth: 2,
        opacity: 1,
        author: 'Current User',
        isVisible: true,
        isLocked: false,
        layer: 0,
      });

      expect(annotation.id).toBeDefined();
      expect(annotation.createdAt).toBeInstanceOf(Date);
      expect(annotation.updatedAt).toBeInstanceOf(Date);
      expect(annotation.zIndex).toBeGreaterThan(0);
    });

    it('should create annotation with custom style', () => {
      const style: Partial<AnnotationStyle> = {
        stroke: {
          color: '#FF0000',
          width: 5,
          opacity: 0.8,
          style: 'dashed',
          lineCap: 'round',
          lineJoin: 'round',
        },
        fill: {
          color: '#00FF00',
          opacity: 0.5,
        },
      };

      const annotation = engine.createAnnotation(
        'circle',
        1,
        { x: 50, y: 75 },
        style
      );

      expect(annotation.color).toBe('#FF0000');
      expect(annotation.strokeColor).toBe('#FF0000');
      expect(annotation.strokeWidth).toBe(5);
    });

    it('should create annotation with advanced options', () => {
      const options: Partial<AdvancedAnnotation> = {
        content: 'Test annotation',
        rotation: 45,
        shadow: {
          offsetX: 2,
          offsetY: 2,
          blur: 4,
          color: '#00000040',
        },
        interactive: {
          clickable: true,
          hoverable: true,
          onClick: 'alert("Clicked!")',
        },
      };

      const annotation = engine.createAnnotation(
        'text',
        2,
        { x: 200, y: 300 },
        undefined,
        options
      );

      expect(annotation.content).toBe('Test annotation');
      expect(annotation.rotation).toBe(45);
      expect(annotation.shadow).toEqual(options.shadow);
      expect(annotation.interactive).toEqual(options.interactive);
    });

    it('should assign unique IDs to annotations', () => {
      const annotation1 = engine.createAnnotation('rectangle', 1, { x: 0, y: 0 });
      const annotation2 = engine.createAnnotation('rectangle', 1, { x: 0, y: 0 });

      expect(annotation1.id).not.toBe(annotation2.id);
    });

    it('should assign incremental z-index values', () => {
      const annotation1 = engine.createAnnotation('rectangle', 1, { x: 0, y: 0 });
      const annotation2 = engine.createAnnotation('rectangle', 1, { x: 0, y: 0 });

      expect(annotation2.zIndex).toBeGreaterThan(annotation1.zIndex!);
    });
  });

  describe('Annotation Management', () => {
    let annotation: AdvancedAnnotation;

    beforeEach(() => {
      annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
    });

    it('should update an existing annotation', () => {
      const updates = {
        x: 150,
        y: 200,
        content: 'Updated content',
        color: '#0000FF',
      };

      const updated = engine.updateAnnotation(annotation.id, updates);

      expect(updated).toMatchObject(updates);
      expect(updated?.updatedAt).not.toEqual(annotation.updatedAt);
    });

    it('should return null when updating non-existent annotation', () => {
      const result = engine.updateAnnotation('non-existent-id', { x: 100 });
      expect(result).toBeNull();
    });

    it('should delete an annotation', () => {
      const deleted = engine.deleteAnnotation(annotation.id);
      expect(deleted).toBe(true);

      const retrieved = engine.getAnnotation(annotation.id);
      expect(retrieved).toBeNull();
    });

    it('should return false when deleting non-existent annotation', () => {
      const result = engine.deleteAnnotation('non-existent-id');
      expect(result).toBe(false);
    });

    it('should retrieve annotation by ID', () => {
      const retrieved = engine.getAnnotation(annotation.id);
      expect(retrieved).toEqual(annotation);
    });

    it('should return null for non-existent annotation ID', () => {
      const result = engine.getAnnotation('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('Page-based Queries', () => {
    beforeEach(() => {
      // Create annotations on different pages
      engine.createAnnotation('rectangle', 1, { x: 0, y: 0 });
      engine.createAnnotation('circle', 1, { x: 100, y: 100 });
      engine.createAnnotation('text', 2, { x: 200, y: 200 });
      engine.createAnnotation('line', 3, { x: 300, y: 300 });
    });

    it('should get annotations for specific page', () => {
      const page1Annotations = engine.getAnnotationsForPage(1);
      const page2Annotations = engine.getAnnotationsForPage(2);
      const page3Annotations = engine.getAnnotationsForPage(3);

      expect(page1Annotations).toHaveLength(2);
      expect(page2Annotations).toHaveLength(1);
      expect(page3Annotations).toHaveLength(1);

      expect(page1Annotations.every(a => a.pageNumber === 1)).toBe(true);
      expect(page2Annotations.every(a => a.pageNumber === 2)).toBe(true);
      expect(page3Annotations.every(a => a.pageNumber === 3)).toBe(true);
    });

    it('should return empty array for page with no annotations', () => {
      const annotations = engine.getAnnotationsForPage(99);
      expect(annotations).toHaveLength(0);
    });

    it('should sort annotations by z-index', () => {
      const annotations = engine.getAnnotationsForPage(1);
      
      for (let i = 1; i < annotations.length; i++) {
        expect(annotations[i].zIndex! >= annotations[i - 1].zIndex!).toBe(true);
      }
    });

    it('should get all annotations', () => {
      const allAnnotations = engine.getAllAnnotations();
      expect(allAnnotations).toHaveLength(4);
    });
  });

  describe('Template System', () => {
    it('should create annotation from template', () => {
      // The engine loads default templates in constructor
      const annotation = engine.createFromTemplate(
        'approval-stamp',
        1,
        { x: 100, y: 100 }
      );

      expect(annotation).toBeDefined();
      expect(annotation?.type).toBe('stamp');
      expect(annotation?.stamp?.template).toBe('APPROVED');
      expect(annotation?.color).toBe('#22C55E');
    });

    it('should return null for non-existent template', () => {
      const annotation = engine.createFromTemplate(
        'non-existent-template',
        1,
        { x: 100, y: 100 }
      );

      expect(annotation).toBeNull();
    });

    it('should apply template variables', () => {
      const annotation = engine.createFromTemplate(
        'signature-field',
        1,
        { x: 100, y: 100 },
        { name: 'John Doe', date: '2024-01-15' }
      );

      expect(annotation).toBeDefined();
      expect(annotation?.type).toBe('signature');
    });
  });

  describe('Annotation Operations', () => {
    let annotation: AdvancedAnnotation;

    beforeEach(() => {
      annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
    });

    it('should duplicate annotation', () => {
      const duplicate = engine.duplicateAnnotation(annotation.id);

      expect(duplicate).toBeDefined();
      expect(duplicate?.id).not.toBe(annotation.id);
      expect(duplicate?.x).toBe(annotation.x + 10);
      expect(duplicate?.y).toBe(annotation.y + 10);
      expect(duplicate?.type).toBe(annotation.type);
    });

    it('should duplicate annotation with custom offset', () => {
      const duplicate = engine.duplicateAnnotation(annotation.id, { x: 50, y: 75 });

      expect(duplicate?.x).toBe(annotation.x + 50);
      expect(duplicate?.y).toBe(annotation.y + 75);
    });

    it('should return null when duplicating non-existent annotation', () => {
      const result = engine.duplicateAnnotation('non-existent-id');
      expect(result).toBeNull();
    });

    it('should group annotations', () => {
      const annotation2 = engine.createAnnotation('circle', 1, { x: 200, y: 200 });
      const groupId = engine.groupAnnotations([annotation.id, annotation2.id], 'test-group');

      expect(groupId).toBe('test-group');

      const updated1 = engine.getAnnotation(annotation.id);
      const updated2 = engine.getAnnotation(annotation2.id);

      expect(updated1?.group).toBe('test-group');
      expect(updated2?.group).toBe('test-group');
    });

    it('should ungroup annotations', () => {
      const annotation2 = engine.createAnnotation('circle', 1, { x: 200, y: 200 });
      const groupId = engine.groupAnnotations([annotation.id, annotation2.id]);

      engine.ungroupAnnotations(groupId);

      const updated1 = engine.getAnnotation(annotation.id);
      const updated2 = engine.getAnnotation(annotation2.id);

      expect(updated1?.group).toBeUndefined();
      expect(updated2?.group).toBeUndefined();
    });
  });

  describe('History Management', () => {
    it('should track annotation creation in history', () => {
      const annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      
      expect(engine.history).toHaveLength(1);
      expect(engine.history[0].action).toBe('create');
      expect(engine.history[0].annotation.id).toBe(annotation.id);
    });

    it('should track annotation updates in history', () => {
      const annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      engine.updateAnnotation(annotation.id, { x: 200 });

      expect(engine.history).toHaveLength(2);
      expect(engine.history[1].action).toBe('update');
    });

    it('should track annotation deletion in history', () => {
      const annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      engine.deleteAnnotation(annotation.id);

      expect(engine.history).toHaveLength(2);
      expect(engine.history[1].action).toBe('delete');
    });

    it('should undo last action', () => {
      const annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      const undone = engine.undo();

      expect(undone).toEqual(annotation);
      expect(engine.getAnnotation(annotation.id)).toBeNull();
    });

    it('should redo last undone action', () => {
      const annotation = engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      engine.undo();
      const redone = engine.redo();

      expect(redone).toEqual(annotation);
      expect(engine.getAnnotation(annotation.id)).toEqual(annotation);
    });

    it('should return null when no actions to undo', () => {
      const result = engine.undo();
      expect(result).toBeNull();
    });

    it('should return null when no actions to redo', () => {
      const result = engine.redo();
      expect(result).toBeNull();
    });
  });

  describe('Export/Import', () => {
    beforeEach(() => {
      engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      engine.createAnnotation('circle', 2, { x: 200, y: 200 });
    });

    it('should export annotations as JSON', () => {
      const exported = engine.exportAnnotations('json');
      const parsed = JSON.parse(exported);

      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0]).toHaveProperty('id');
      expect(parsed[0]).toHaveProperty('type');
      expect(parsed[0]).toHaveProperty('pageNumber');
    });

    it('should export annotations as CSV', () => {
      const exported = engine.exportAnnotations('csv');
      const lines = exported.split('\n');

      expect(lines[0]).toContain('ID,Type,Page,X,Y');
      expect(lines).toHaveLength(3); // Header + 2 annotations
    });

    it('should export annotations as XFDF', () => {
      const exported = engine.exportAnnotations('xfdf');

      expect(exported).toContain('<?xml version="1.0"');
      expect(exported).toContain('<xfdf xmlns="http://ns.adobe.com/xfdf/"');
      expect(exported).toContain('<annots>');
    });

    it('should import annotations from JSON', () => {
      const testAnnotations = [
        {
          id: 'test-1',
          type: 'rectangle',
          pageNumber: 1,
          x: 50,
          y: 50,
          width: 100,
          height: 100,
          color: '#FF0000',
          author: 'Test User',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      const imported = engine.importAnnotations(JSON.stringify(testAnnotations), 'json');

      expect(imported).toBe(1);
      expect(engine.getAllAnnotations()).toHaveLength(3); // 2 existing + 1 imported
    });

    it('should handle import errors gracefully', () => {
      const imported = engine.importAnnotations('invalid json', 'json');
      expect(imported).toBe(0);
    });
  });

  describe('Cleanup', () => {
    it('should clear all annotations', () => {
      engine.createAnnotation('rectangle', 1, { x: 100, y: 100 });
      engine.createAnnotation('circle', 2, { x: 200, y: 200 });

      engine.clearAll();

      expect(engine.getAllAnnotations()).toHaveLength(0);
      expect(engine.history).toHaveLength(0);
      expect(engine.currentHistoryIndex).toBe(-1);
    });
  });
});
