/**
 * Cobalt PDF Viewer - Theme Customization
 * Custom themes and styling options for the PDF viewer
 */

/* Default Theme Variables */
:root {
  /* Primary Colors */
  --pdf-viewer-primary: #3b82f6;
  --pdf-viewer-primary-hover: #2563eb;
  --pdf-viewer-primary-light: #dbeafe;
  
  /* Background Colors */
  --pdf-viewer-bg: #ffffff;
  --pdf-viewer-bg-secondary: #f8fafc;
  --pdf-viewer-bg-muted: #f1f5f9;
  
  /* Text Colors */
  --pdf-viewer-text: #1e293b;
  --pdf-viewer-text-muted: #64748b;
  --pdf-viewer-text-light: #94a3b8;
  
  /* Border Colors */
  --pdf-viewer-border: #e2e8f0;
  --pdf-viewer-border-hover: #cbd5e1;
  
  /* Control Colors */
  --pdf-viewer-control-bg: rgba(0, 0, 0, 0.8);
  --pdf-viewer-control-text: #ffffff;
  --pdf-viewer-control-border: rgba(255, 255, 255, 0.2);
  
  /* Status Colors */
  --pdf-viewer-success: #10b981;
  --pdf-viewer-warning: #f59e0b;
  --pdf-viewer-error: #ef4444;
  --pdf-viewer-info: #3b82f6;
  
  /* Accessibility Colors */
  --pdf-viewer-focus: #3b82f6;
  --pdf-viewer-focus-ring: rgba(59, 130, 246, 0.3);
  
  /* Animation Durations */
  --pdf-viewer-transition: 0.2s ease-in-out;
  --pdf-viewer-transition-slow: 0.3s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
  --pdf-viewer-primary: #60a5fa;
  --pdf-viewer-primary-hover: #3b82f6;
  --pdf-viewer-primary-light: #1e3a8a;
  
  --pdf-viewer-bg: #0f172a;
  --pdf-viewer-bg-secondary: #1e293b;
  --pdf-viewer-bg-muted: #334155;
  
  --pdf-viewer-text: #f8fafc;
  --pdf-viewer-text-muted: #cbd5e1;
  --pdf-viewer-text-light: #94a3b8;
  
  --pdf-viewer-border: #334155;
  --pdf-viewer-border-hover: #475569;
  
  --pdf-viewer-control-bg: rgba(0, 0, 0, 0.9);
  --pdf-viewer-control-text: #ffffff;
  --pdf-viewer-control-border: rgba(255, 255, 255, 0.1);
}

/* High Contrast Theme */
[data-theme="high-contrast"] {
  --pdf-viewer-primary: #000000;
  --pdf-viewer-primary-hover: #333333;
  --pdf-viewer-primary-light: #f0f0f0;
  
  --pdf-viewer-bg: #ffffff;
  --pdf-viewer-bg-secondary: #f0f0f0;
  --pdf-viewer-bg-muted: #e0e0e0;
  
  --pdf-viewer-text: #000000;
  --pdf-viewer-text-muted: #333333;
  --pdf-viewer-text-light: #666666;
  
  --pdf-viewer-border: #000000;
  --pdf-viewer-border-hover: #333333;
  
  --pdf-viewer-focus: #ff0000;
  --pdf-viewer-focus-ring: rgba(255, 0, 0, 0.5);
}

/* Corporate Theme */
[data-theme="corporate"] {
  --pdf-viewer-primary: #1f2937;
  --pdf-viewer-primary-hover: #111827;
  --pdf-viewer-primary-light: #f3f4f6;
  
  --pdf-viewer-bg: #ffffff;
  --pdf-viewer-bg-secondary: #f9fafb;
  --pdf-viewer-bg-muted: #f3f4f6;
  
  --pdf-viewer-text: #1f2937;
  --pdf-viewer-text-muted: #6b7280;
  --pdf-viewer-text-light: #9ca3af;
  
  --pdf-viewer-border: #d1d5db;
  --pdf-viewer-border-hover: #9ca3af;
}

/* Component-specific Styling */

/* PDF Viewer Container */
.pdf-viewer {
  background-color: var(--pdf-viewer-bg);
  color: var(--pdf-viewer-text);
  border: 1px solid var(--pdf-viewer-border);
  transition: all var(--pdf-viewer-transition);
}

.pdf-viewer:focus-within {
  border-color: var(--pdf-viewer-focus);
  box-shadow: 0 0 0 3px var(--pdf-viewer-focus-ring);
}

/* Controls */
.pdf-viewer-controls {
  background-color: var(--pdf-viewer-control-bg);
  color: var(--pdf-viewer-control-text);
  border: 1px solid var(--pdf-viewer-control-border);
  backdrop-filter: blur(8px);
}

.pdf-viewer-button {
  background-color: transparent;
  color: var(--pdf-viewer-control-text);
  border: 1px solid var(--pdf-viewer-control-border);
  transition: all var(--pdf-viewer-transition);
}

.pdf-viewer-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.pdf-viewer-button:focus {
  outline: 2px solid var(--pdf-viewer-focus);
  outline-offset: 2px;
}

/* Search Highlights */
.pdf-search-highlight {
  background-color: var(--pdf-viewer-warning);
  color: var(--pdf-viewer-text);
  padding: 1px 2px;
  border-radius: 2px;
  animation: highlight-pulse 2s ease-in-out;
}

.pdf-search-highlight.active {
  background-color: var(--pdf-viewer-primary);
  color: white;
}

@keyframes highlight-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Loading States */
.pdf-viewer-loading {
  background: linear-gradient(
    90deg,
    var(--pdf-viewer-bg-muted) 25%,
    var(--pdf-viewer-bg-secondary) 50%,
    var(--pdf-viewer-bg-muted) 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Error States */
.pdf-viewer-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: var(--pdf-viewer-error);
  color: var(--pdf-viewer-error);
}

/* Success States */
.pdf-viewer-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: var(--pdf-viewer-success);
  color: var(--pdf-viewer-success);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .pdf-viewer-controls {
    padding: 8px;
    font-size: 14px;
  }
  
  .pdf-viewer-button {
    min-height: 44px; /* Touch target size */
    min-width: 44px;
  }
  
  .pdf-viewer-text {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .pdf-viewer,
  .pdf-viewer-button,
  .pdf-search-highlight {
    animation: none;
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .pdf-viewer {
    border-width: 2px;
  }
  
  .pdf-viewer-button {
    border-width: 2px;
  }
  
  .pdf-search-highlight {
    border: 2px solid var(--pdf-viewer-text);
  }
}

/* Print Styles */
@media print {
  .pdf-viewer-controls,
  .pdf-viewer-sidebar,
  .pdf-viewer-overlay {
    display: none !important;
  }
  
  .pdf-viewer {
    border: none;
    box-shadow: none;
  }
}

/* Custom Scrollbars */
.pdf-viewer-scrollable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-viewer-scrollable::-webkit-scrollbar-track {
  background: var(--pdf-viewer-bg-muted);
  border-radius: 4px;
}

.pdf-viewer-scrollable::-webkit-scrollbar-thumb {
  background: var(--pdf-viewer-border-hover);
  border-radius: 4px;
}

.pdf-viewer-scrollable::-webkit-scrollbar-thumb:hover {
  background: var(--pdf-viewer-text-muted);
}

/* Focus Management */
.pdf-viewer-focus-trap {
  position: relative;
}

.pdf-viewer-focus-trap:focus {
  outline: 2px solid var(--pdf-viewer-focus);
  outline-offset: 2px;
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.pdf-viewer-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--pdf-viewer-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top var(--pdf-viewer-transition);
}

.pdf-viewer-skip-link:focus {
  top: 6px;
}

/* Utility Classes */
.pdf-viewer-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.pdf-viewer-slide-up {
  animation: slideUp 0.3s ease-out;
}

.pdf-viewer-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
