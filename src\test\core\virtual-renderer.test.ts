import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { VirtualPageRenderer, DEFAULT_VIRTUAL_CONFIG } from '@/lib/virtual-renderer';

// Mock PDF.js types
const mockPDFDocument = {
  numPages: 10,
  getPage: vi.fn().mockImplementation((pageNum: number) => Promise.resolve({
    getViewport: vi.fn().mockReturnValue({
      width: 612,
      height: 792,
    }),
  })),
};

describe('VirtualPageRenderer', () => {
  let renderer: VirtualPageRenderer;

  beforeEach(() => {
    renderer = new VirtualPageRenderer({
      viewportHeight: 800,
      viewportWidth: 600,
      overscanCount: 2,
      maxConcurrentRenders: 3,
      maxCachedPages: 5,
    });
  });

  afterEach(() => {
    renderer.destroy();
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      const defaultRenderer = new VirtualPageRenderer();
      expect(defaultRenderer).toBeDefined();
      defaultRenderer.destroy();
    });

    it('should initialize with custom config', () => {
      const customConfig = {
        viewportHeight: 1000,
        maxCachedPages: 20,
      };
      const customRenderer = new VirtualPageRenderer(customConfig);
      expect(customRenderer).toBeDefined();
      customRenderer.destroy();
    });

    it('should initialize pages correctly', async () => {
      await renderer.initialize(mockPDFDocument as any);
      const state = renderer.getState();
      
      expect(state.pages.size).toBe(10);
      expect(state.totalHeight).toBeGreaterThan(0);
      
      // Check first page
      const firstPage = state.pages.get(1);
      expect(firstPage).toBeDefined();
      expect(firstPage?.pageNumber).toBe(1);
      expect(firstPage?.height).toBe(792);
      expect(firstPage?.width).toBe(612);
    });
  });

  describe('Visible Range Calculation', () => {
    beforeEach(async () => {
      await renderer.initialize(mockPDFDocument as any);
    });

    it('should calculate initial visible range', () => {
      const state = renderer.getState();
      expect(state.visibleRange.start).toBe(1);
      expect(state.visibleRange.end).toBeGreaterThanOrEqual(1);
    });

    it('should update visible range on scroll', () => {
      // Scroll to middle of document
      const scrollPosition = 3000; // Approximate middle
      renderer.updateVisibleRange(scrollPosition);
      
      const state = renderer.getState();
      expect(state.scrollPosition).toBe(scrollPosition);
      expect(state.visibleRange.start).toBeGreaterThan(1);
    });

    it('should include overscan pages', () => {
      renderer.updateVisibleRange(0);
      const state = renderer.getState();
      
      // Should include overscan pages
      expect(state.visibleRange.end - state.visibleRange.start).toBeGreaterThanOrEqual(2);
    });

    it('should handle scroll to bottom', async () => {
      await renderer.initialize(mockPDFDocument as any);

      const largeScrollPosition = 10000;
      renderer.updateVisibleRange(largeScrollPosition);

      const state = renderer.getState();
      // Should include the last page in the visible range
      expect(state.visibleRange.end).toBeGreaterThanOrEqual(3);
      expect(state.visibleRange.end).toBeLessThanOrEqual(10);
    });
  });

  describe('Render Priority System', () => {
    beforeEach(async () => {
      await renderer.initialize(mockPDFDocument as any);
    });

    it('should assign higher priority to visible pages', () => {
      renderer.updateVisibleRange(0);
      const state = renderer.getState();
      
      // First page should be visible and have high priority
      const firstPage = state.pages.get(1);
      expect(firstPage?.isVisible).toBe(true);
      expect(firstPage?.renderPriority).toBeGreaterThan(0.5);
    });

    it('should assign lower priority to non-visible pages', () => {
      renderer.updateVisibleRange(0);
      const state = renderer.getState();
      
      // Last page should not be visible and have lower priority
      const lastPage = state.pages.get(10);
      expect(lastPage?.isVisible).toBe(false);
      expect(lastPage?.renderPriority).toBeLessThan(0.5);
    });

    it('should update priorities when scrolling', () => {
      // Initial state
      renderer.updateVisibleRange(0);
      const initialState = renderer.getState();
      const initialFirstPagePriority = initialState.pages.get(1)?.renderPriority || 0;
      
      // Scroll down significantly
      renderer.updateVisibleRange(5000);
      const newState = renderer.getState();
      const newFirstPagePriority = newState.pages.get(1)?.renderPriority || 0;
      
      // First page priority should decrease when scrolled away
      expect(newFirstPagePriority).toBeLessThan(initialFirstPagePriority);
    });
  });

  describe('Performance Metrics', () => {
    beforeEach(async () => {
      await renderer.initialize(mockPDFDocument as any);
    });

    it('should track render times', () => {
      const state = renderer.getState();
      expect(state.metrics).toBeDefined();
      expect(state.metrics.renderTimes).toBeDefined();
      expect(state.metrics.averageRenderTime).toBeDefined();
    });

    it('should update metrics after rendering', async () => {
      // Trigger some renders by updating visible range
      renderer.updateVisibleRange(0);
      
      // Wait a bit for async rendering
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const state = renderer.getState();
      expect(state.metrics.renderTimes.size).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Memory Management', () => {
    beforeEach(async () => {
      await renderer.initialize(mockPDFDocument as any);
    });

    it('should limit cached pages', () => {
      const state = renderer.getState();
      expect(state.cachedPages.size).toBeLessThanOrEqual(5); // maxCachedPages
    });

    it('should track loading pages', () => {
      const state = renderer.getState();
      expect(state.loadingPages).toBeDefined();
      expect(state.loadingPages.size).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Configuration Updates', () => {
    beforeEach(async () => {
      await renderer.initialize(mockPDFDocument as any);
    });

    it('should update configuration', () => {
      const newConfig = {
        viewportHeight: 1200,
        maxCachedPages: 15,
      };
      
      renderer.updateConfig(newConfig);
      
      // Should trigger recalculation when viewport changes
      const state = renderer.getState();
      expect(state).toBeDefined();
    });

    it('should recalculate visible range on viewport change', () => {
      const initialState = renderer.getState();
      const initialRange = initialState.visibleRange;
      
      renderer.updateConfig({ viewportHeight: 1200 });
      
      const newState = renderer.getState();
      // Range might change due to different viewport size
      expect(newState.visibleRange).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid PDF document gracefully', async () => {
      const invalidDoc = null;
      
      // Should not throw
      expect(() => {
        renderer.updateVisibleRange(0);
      }).not.toThrow();
    });

    it('should handle negative scroll positions', () => {
      expect(() => {
        renderer.updateVisibleRange(-100);
      }).not.toThrow();
    });

    it('should handle very large scroll positions', () => {
      expect(() => {
        renderer.updateVisibleRange(999999);
      }).not.toThrow();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources on destroy', async () => {
      await renderer.initialize(mockPDFDocument as any);

      const state = renderer.getState();
      expect(state.pages.size).toBeGreaterThan(0);

      renderer.destroy();

      const cleanedState = renderer.getState();
      expect(cleanedState.pages.size).toBe(0);
      expect(cleanedState.cachedPages.size).toBe(0);
      expect(cleanedState.loadingPages.size).toBe(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle single page documents', async () => {
      const singlePageDoc = {
        numPages: 1,
        getPage: vi.fn().mockImplementation(() => Promise.resolve({
          getViewport: vi.fn().mockReturnValue({
            width: 612,
            height: 792,
          }),
        })),
      };

      await renderer.initialize(singlePageDoc as any);
      const state = renderer.getState();
      
      expect(state.pages.size).toBe(1);
      expect(state.visibleRange.start).toBe(1);
      expect(state.visibleRange.end).toBe(1);
    });

    it('should handle very large documents', async () => {
      const largeDoc = {
        numPages: 1000,
        getPage: vi.fn().mockImplementation(() => Promise.resolve({
          getViewport: vi.fn().mockReturnValue({
            width: 612,
            height: 792,
          }),
        })),
      };

      await renderer.initialize(largeDoc as any);
      const state = renderer.getState();
      
      expect(state.pages.size).toBe(1000);
      expect(state.totalHeight).toBeGreaterThan(792 * 1000);
    });

    it('should handle zero viewport dimensions', () => {
      const zeroViewportRenderer = new VirtualPageRenderer({
        viewportHeight: 0,
        viewportWidth: 0,
      });

      expect(() => {
        zeroViewportRenderer.updateVisibleRange(0);
      }).not.toThrow();

      zeroViewportRenderer.destroy();
    });
  });
});
