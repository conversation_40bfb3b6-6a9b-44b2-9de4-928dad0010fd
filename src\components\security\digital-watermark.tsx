"use client";

import React, { useEffect, useRef, useCallback } from 'react';

export interface WatermarkConfig {
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  opacity: number;
  rotation: number; // degrees
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'repeat';
  spacing: { x: number; y: number }; // for repeat mode
  offset: { x: number; y: number };
  zIndex: number;
  blend: 'normal' | 'multiply' | 'screen' | 'overlay' | 'soft-light' | 'hard-light';
  visible: boolean;
  dynamic: boolean; // updates with current time
  includeMetadata: boolean;
}

export interface WatermarkMetadata {
  userId?: string;
  userEmail?: string;
  userName?: string;
  sessionId?: string;
  documentId?: string;
  documentTitle?: string;
  accessTime: string;
  ipAddress?: string;
  userAgent?: string;
  customFields?: Record<string, string>;
}

interface DigitalWatermarkProps {
  config: WatermarkConfig;
  metadata: WatermarkMetadata;
  containerRef: React.RefObject<HTMLElement>;
  className?: string;
}

const DEFAULT_CONFIG: WatermarkConfig = {
  text: 'CONFIDENTIAL',
  fontSize: 24,
  fontFamily: 'Arial, sans-serif',
  color: '#000000',
  opacity: 0.1,
  rotation: -45,
  position: 'repeat',
  spacing: { x: 200, y: 150 },
  offset: { x: 0, y: 0 },
  zIndex: 1000,
  blend: 'multiply',
  visible: true,
  dynamic: true,
  includeMetadata: true
};

export default function DigitalWatermark({
  config,
  metadata,
  containerRef,
  className
}: DigitalWatermarkProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();

  // Generate watermark text with metadata
  const generateWatermarkText = useCallback(() => {
    let text = config.text;

    if (config.includeMetadata) {
      const parts = [text];
      
      if (metadata.userName) parts.push(metadata.userName);
      if (metadata.userEmail) parts.push(metadata.userEmail);
      if (metadata.sessionId) parts.push(`Session: ${metadata.sessionId.substring(0, 8)}`);
      if (config.dynamic) parts.push(new Date().toLocaleString());
      
      text = parts.join(' • ');
    }

    return text;
  }, [config.text, config.includeMetadata, config.dynamic, metadata]);

  // Draw watermark on canvas
  const drawWatermark = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match container
    const rect = container.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (!config.visible) return;

    // Set up text styling
    ctx.font = `${config.fontSize}px ${config.fontFamily}`;
    ctx.fillStyle = config.color;
    ctx.globalAlpha = config.opacity;
    ctx.globalCompositeOperation = config.blend as GlobalCompositeOperation;

    const text = generateWatermarkText();
    const textMetrics = ctx.measureText(text);
    const textWidth = textMetrics.width;
    const textHeight = config.fontSize;

    // Save context for rotation
    ctx.save();

    if (config.position === 'repeat') {
      // Draw repeating watermarks
      const spacingX = config.spacing.x;
      const spacingY = config.spacing.y;
      
      for (let x = -textWidth; x < canvas.width + textWidth; x += spacingX) {
        for (let y = 0; y < canvas.height + textHeight; y += spacingY) {
          ctx.save();
          ctx.translate(x + config.offset.x, y + config.offset.y);
          ctx.rotate((config.rotation * Math.PI) / 180);
          ctx.fillText(text, 0, 0);
          ctx.restore();
        }
      }
    } else {
      // Draw single watermark at specified position
      let x: number, y: number;

      switch (config.position) {
        case 'center':
          x = canvas.width / 2;
          y = canvas.height / 2;
          break;
        case 'top-left':
          x = textWidth / 2 + 20;
          y = textHeight + 20;
          break;
        case 'top-right':
          x = canvas.width - textWidth / 2 - 20;
          y = textHeight + 20;
          break;
        case 'bottom-left':
          x = textWidth / 2 + 20;
          y = canvas.height - 20;
          break;
        case 'bottom-right':
          x = canvas.width - textWidth / 2 - 20;
          y = canvas.height - 20;
          break;
        default:
          x = canvas.width / 2;
          y = canvas.height / 2;
      }

      x += config.offset.x;
      y += config.offset.y;

      ctx.translate(x, y);
      ctx.rotate((config.rotation * Math.PI) / 180);
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(text, 0, 0);
    }

    ctx.restore();
  }, [config, metadata, generateWatermarkText, containerRef]);

  // Handle container resize
  const handleResize = useCallback(() => {
    drawWatermark();
  }, [drawWatermark]);

  // Animation loop for dynamic watermarks
  const animate = useCallback(() => {
    if (config.dynamic && config.visible) {
      drawWatermark();
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  }, [config.dynamic, config.visible, drawWatermark]);

  // Setup resize observer
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, [handleResize, containerRef]);

  // Initial draw and animation setup
  useEffect(() => {
    drawWatermark();

    if (config.dynamic) {
      animate();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [drawWatermark, animate, config.dynamic]);

  // Redraw when config changes
  useEffect(() => {
    drawWatermark();
  }, [drawWatermark]);

  if (!config.visible) return null;

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{
        zIndex: config.zIndex,
        mixBlendMode: config.blend
      }}
    />
  );
}

// Watermark configuration component
interface WatermarkConfiguratorProps {
  config: WatermarkConfig;
  onConfigChange: (config: WatermarkConfig) => void;
  className?: string;
}

export function WatermarkConfigurator({
  config,
  onConfigChange,
  className
}: WatermarkConfiguratorProps) {
  const updateConfig = (updates: Partial<WatermarkConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-medium">Watermark Settings</h3>
      
      {/* Text */}
      <div>
        <label className="block text-sm font-medium mb-1">Text</label>
        <input
          type="text"
          value={config.text}
          onChange={(e) => updateConfig({ text: e.target.value })}
          className="w-full px-3 py-2 border rounded-md"
        />
      </div>

      {/* Visibility */}
      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="visible"
          checked={config.visible}
          onChange={(e) => updateConfig({ visible: e.target.checked })}
        />
        <label htmlFor="visible" className="text-sm font-medium">Visible</label>
      </div>

      {/* Font Size */}
      <div>
        <label className="block text-sm font-medium mb-1">
          Font Size: {config.fontSize}px
        </label>
        <input
          type="range"
          min="8"
          max="72"
          value={config.fontSize}
          onChange={(e) => updateConfig({ fontSize: parseInt(e.target.value) })}
          className="w-full"
        />
      </div>

      {/* Opacity */}
      <div>
        <label className="block text-sm font-medium mb-1">
          Opacity: {Math.round(config.opacity * 100)}%
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.01"
          value={config.opacity}
          onChange={(e) => updateConfig({ opacity: parseFloat(e.target.value) })}
          className="w-full"
        />
      </div>

      {/* Rotation */}
      <div>
        <label className="block text-sm font-medium mb-1">
          Rotation: {config.rotation}°
        </label>
        <input
          type="range"
          min="-180"
          max="180"
          value={config.rotation}
          onChange={(e) => updateConfig({ rotation: parseInt(e.target.value) })}
          className="w-full"
        />
      </div>

      {/* Color */}
      <div>
        <label className="block text-sm font-medium mb-1">Color</label>
        <input
          type="color"
          value={config.color}
          onChange={(e) => updateConfig({ color: e.target.value })}
          className="w-full h-10 border rounded-md"
        />
      </div>

      {/* Position */}
      <div>
        <label className="block text-sm font-medium mb-1">Position</label>
        <select
          value={config.position}
          onChange={(e) => updateConfig({ position: e.target.value as WatermarkConfig['position'] })}
          className="w-full px-3 py-2 border rounded-md"
        >
          <option value="center">Center</option>
          <option value="top-left">Top Left</option>
          <option value="top-right">Top Right</option>
          <option value="bottom-left">Bottom Left</option>
          <option value="bottom-right">Bottom Right</option>
          <option value="repeat">Repeat</option>
        </select>
      </div>

      {/* Blend Mode */}
      <div>
        <label className="block text-sm font-medium mb-1">Blend Mode</label>
        <select
          value={config.blend}
          onChange={(e) => updateConfig({ blend: e.target.value as WatermarkConfig['blend'] })}
          className="w-full px-3 py-2 border rounded-md"
        >
          <option value="normal">Normal</option>
          <option value="multiply">Multiply</option>
          <option value="screen">Screen</option>
          <option value="overlay">Overlay</option>
          <option value="soft-light">Soft Light</option>
          <option value="hard-light">Hard Light</option>
        </select>
      </div>

      {/* Dynamic Updates */}
      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="dynamic"
          checked={config.dynamic}
          onChange={(e) => updateConfig({ dynamic: e.target.checked })}
        />
        <label htmlFor="dynamic" className="text-sm font-medium">
          Dynamic (updates with time)
        </label>
      </div>

      {/* Include Metadata */}
      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="includeMetadata"
          checked={config.includeMetadata}
          onChange={(e) => updateConfig({ includeMetadata: e.target.checked })}
        />
        <label htmlFor="includeMetadata" className="text-sm font-medium">
          Include user metadata
        </label>
      </div>
    </div>
  );
}

// Utility functions for watermark management
export class WatermarkManager {
  static createSessionMetadata(
    userId?: string,
    userEmail?: string,
    userName?: string,
    documentId?: string,
    documentTitle?: string,
    customFields?: Record<string, string>
  ): WatermarkMetadata {
    return {
      userId,
      userEmail,
      userName,
      sessionId: this.generateSessionId(),
      documentId,
      documentTitle,
      accessTime: new Date().toISOString(),
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
      customFields
    };
  }

  static generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  static getClientIP(): string {
    // In a real implementation, this would get the client IP from the server
    return 'Unknown';
  }

  static createPresetConfigs(): Record<string, WatermarkConfig> {
    return {
      confidential: {
        ...DEFAULT_CONFIG,
        text: 'CONFIDENTIAL',
        color: '#ff0000',
        opacity: 0.15,
        fontSize: 32
      },
      draft: {
        ...DEFAULT_CONFIG,
        text: 'DRAFT',
        color: '#ff8800',
        opacity: 0.2,
        rotation: 0,
        position: 'top-right'
      },
      internal: {
        ...DEFAULT_CONFIG,
        text: 'INTERNAL USE ONLY',
        color: '#0066cc',
        opacity: 0.1,
        fontSize: 20
      },
      sample: {
        ...DEFAULT_CONFIG,
        text: 'SAMPLE',
        color: '#888888',
        opacity: 0.3,
        rotation: -30,
        position: 'center'
      }
    };
  }
}
