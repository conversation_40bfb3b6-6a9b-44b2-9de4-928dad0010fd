"use client";

import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { SearchSuggestionsEngine, type SearchSuggestion, type SearchContext } from '@/lib/search/search-suggestions-engine';
import type { 
  AdvancedSearchQuery, 
  SearchFilters, 
  SearchOptions, 
  SearchResult 
} from '@/components/search/advanced-search-interface';
import type { SearchAnalyticsData } from '@/components/search/search-analytics';

export interface EnhancedSearchConfig {
  enableSuggestions: boolean;
  enableAnalytics: boolean;
  enableCaching: boolean;
  enableOCR: boolean;
  enableSemanticSearch: boolean;
  maxResults: number;
  debounceDelay: number;
  cacheTimeout: number;
}

export interface SearchPerformanceMetrics {
  searchTime: number;
  renderTime: number;
  cacheHit: boolean;
  resultCount: number;
  queryLength: number;
  timestamp: Date;
}

const DEFAULT_CONFIG: EnhancedSearchConfig = {
  enableSuggestions: true,
  enableAnalytics: true,
  enableCaching: true,
  enableOCR: false,
  enableSemanticSearch: false,
  maxResults: 100,
  debounceDelay: 300,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
};

const DEFAULT_FILTERS: SearchFilters = {
  contentTypes: [],
  dateRange: { start: null, end: null },
  pageRange: { start: null, end: null },
  authors: [],
  tags: [],
  hasAnnotations: null,
  hasBookmarks: null,
  hasForms: null,
  fileSize: { min: null, max: null },
  language: null,
  rating: null,
};

const DEFAULT_OPTIONS: SearchOptions = {
  caseSensitive: false,
  wholeWords: false,
  useRegex: false,
  fuzzySearch: false,
  semanticSearch: false,
  includeOCR: false,
  searchScope: 'all',
  maxResults: 100,
  sortBy: 'relevance',
  sortOrder: 'desc',
  groupBy: 'none',
};

export function useEnhancedSearch(
  searchFunction: (query: AdvancedSearchQuery) => Promise<SearchResult[]>,
  config: Partial<EnhancedSearchConfig> = {}
) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Core search state
  const [query, setQuery] = useState<AdvancedSearchQuery>({
    text: '',
    filters: DEFAULT_FILTERS,
    options: DEFAULT_OPTIONS,
    facets: {
      contentTypes: [],
      authors: [],
      tags: [],
      dateRanges: [],
      languages: [],
    },
  });
  
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(-1);

  // Suggestions and analytics
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [analytics, setAnalytics] = useState<SearchAnalyticsData>({
    totalSearches: 0,
    uniqueQueries: 0,
    averageResultsPerSearch: 0,
    searchSuccessRate: 0,
    topQueries: [],
    recentQueries: [],
    searchTrends: [],
    contentTypeDistribution: [],
    userBehavior: {
      avgSearchLength: 0,
      avgTimeToClick: 0,
      mostActiveHours: [],
      searchPatterns: [],
    },
    performanceMetrics: {
      avgSearchTime: 0,
      avgRenderTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
    },
  });

  // Refs and engines
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const suggestionsEngine = useRef<SearchSuggestionsEngine>();
  const searchCache = useRef<Map<string, { results: SearchResult[]; timestamp: number }>>(new Map());
  const performanceMetrics = useRef<SearchPerformanceMetrics[]>([]);

  // Initialize suggestions engine
  useEffect(() => {
    if (finalConfig.enableSuggestions) {
      suggestionsEngine.current = new SearchSuggestionsEngine({
        maxSuggestions: 8,
        enableRecentSearches: true,
        enablePopularSearches: true,
        enableSmartSuggestions: true,
        enableSemanticSuggestions: finalConfig.enableSemanticSearch,
        enableContextualSuggestions: true,
      });
    }
  }, [finalConfig.enableSuggestions, finalConfig.enableSemanticSearch]);

  // Search context for suggestions
  const searchContext = useMemo((): SearchContext => ({
    recentSearches: searchHistory,
    userPreferences: {
      preferredContentTypes: query.filters.contentTypes,
      frequentAuthors: <AUTHORS>
      commonTags: query.filters.tags,
    },
    documentMetadata: {
      availableAuthors: <AUTHORS>
      availableTags: ['important', 'draft', 'review', 'final'], // Would come from actual data
      availableLanguages: ['en', 'es', 'fr', 'de'],
      contentTypes: ['text', 'annotation', 'bookmark', 'form', 'metadata'],
    },
  }), [searchHistory, query.filters]);

  // Generate cache key for search query
  const getCacheKey = useCallback((searchQuery: AdvancedSearchQuery): string => {
    return JSON.stringify({
      text: searchQuery.text,
      filters: searchQuery.filters,
      options: searchQuery.options,
    });
  }, []);

  // Check cache for results
  const getCachedResults = useCallback((searchQuery: AdvancedSearchQuery): SearchResult[] | null => {
    if (!finalConfig.enableCaching) return null;
    
    const cacheKey = getCacheKey(searchQuery);
    const cached = searchCache.current.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < finalConfig.cacheTimeout) {
      return cached.results;
    }
    
    return null;
  }, [finalConfig.enableCaching, finalConfig.cacheTimeout, getCacheKey]);

  // Cache search results
  const cacheResults = useCallback((searchQuery: AdvancedSearchQuery, searchResults: SearchResult[]) => {
    if (!finalConfig.enableCaching) return;
    
    const cacheKey = getCacheKey(searchQuery);
    searchCache.current.set(cacheKey, {
      results: searchResults,
      timestamp: Date.now(),
    });

    // Clean old cache entries
    const now = Date.now();
    for (const [key, value] of searchCache.current.entries()) {
      if (now - value.timestamp > finalConfig.cacheTimeout) {
        searchCache.current.delete(key);
      }
    }
  }, [finalConfig.enableCaching, finalConfig.cacheTimeout, getCacheKey]);

  // Record performance metrics
  const recordPerformance = useCallback((metrics: SearchPerformanceMetrics) => {
    if (!finalConfig.enableAnalytics) return;
    
    performanceMetrics.current.push(metrics);
    
    // Keep only last 1000 metrics
    if (performanceMetrics.current.length > 1000) {
      performanceMetrics.current = performanceMetrics.current.slice(-1000);
    }

    // Update analytics
    const allMetrics = performanceMetrics.current;
    const avgSearchTime = allMetrics.reduce((sum, m) => sum + m.searchTime, 0) / allMetrics.length;
    const avgRenderTime = allMetrics.reduce((sum, m) => sum + m.renderTime, 0) / allMetrics.length;
    const cacheHitRate = allMetrics.filter(m => m.cacheHit).length / allMetrics.length;
    
    setAnalytics(prev => ({
      ...prev,
      performanceMetrics: {
        ...prev.performanceMetrics,
        avgSearchTime: Math.round(avgSearchTime),
        avgRenderTime: Math.round(avgRenderTime),
        cacheHitRate,
      },
    }));
  }, [finalConfig.enableAnalytics]);

  // Perform search with caching and analytics
  const performSearch = useCallback(async (searchQuery: AdvancedSearchQuery) => {
    if (!searchQuery.text.trim()) {
      setResults([]);
      setCurrentIndex(-1);
      return;
    }

    const startTime = Date.now();
    setIsSearching(true);
    setError(null);

    try {
      // Check cache first
      const cachedResults = getCachedResults(searchQuery);
      let searchResults: SearchResult[];
      let cacheHit = false;

      if (cachedResults) {
        searchResults = cachedResults;
        cacheHit = true;
      } else {
        // Perform actual search
        searchResults = await searchFunction(searchQuery);
        cacheResults(searchQuery, searchResults);
      }

      const searchTime = Date.now() - startTime;
      const renderStart = Date.now();

      setResults(searchResults);
      setCurrentIndex(searchResults.length > 0 ? 0 : -1);

      const renderTime = Date.now() - renderStart;

      // Record performance metrics
      recordPerformance({
        searchTime,
        renderTime,
        cacheHit,
        resultCount: searchResults.length,
        queryLength: searchQuery.text.length,
        timestamp: new Date(),
      });

      // Record search for suggestions and analytics
      if (suggestionsEngine.current) {
        suggestionsEngine.current.recordSearch(searchQuery.text, searchResults.length);
      }

      // Update search history
      if (searchQuery.text && !searchHistory.includes(searchQuery.text)) {
        setSearchHistory(prev => [searchQuery.text, ...prev.slice(0, 19)]);
      }

      // Update analytics
      if (finalConfig.enableAnalytics) {
        setAnalytics(prev => ({
          ...prev,
          totalSearches: prev.totalSearches + 1,
          averageResultsPerSearch: 
            (prev.averageResultsPerSearch * prev.totalSearches + searchResults.length) / (prev.totalSearches + 1),
          searchSuccessRate: 
            (prev.searchSuccessRate * prev.totalSearches + (searchResults.length > 0 ? 1 : 0)) / (prev.totalSearches + 1),
          recentQueries: [
            {
              query: searchQuery.text,
              timestamp: new Date(),
              resultCount: searchResults.length,
              clickedResults: 0,
            },
            ...prev.recentQueries.slice(0, 49),
          ],
        }));
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      setResults([]);
      setCurrentIndex(-1);

      // Record error in analytics
      if (finalConfig.enableAnalytics) {
        setAnalytics(prev => ({
          ...prev,
          performanceMetrics: {
            ...prev.performanceMetrics,
            errorRate: (prev.performanceMetrics.errorRate * prev.totalSearches + 1) / (prev.totalSearches + 1),
          },
        }));
      }
    } finally {
      setIsSearching(false);
    }
  }, [
    searchFunction,
    getCachedResults,
    cacheResults,
    recordPerformance,
    searchHistory,
    finalConfig.enableAnalytics,
  ]);

  // Debounced search
  const debouncedSearch = useCallback((searchQuery: AdvancedSearchQuery) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      performSearch(searchQuery);
    }, finalConfig.debounceDelay);
  }, [performSearch, finalConfig.debounceDelay]);

  // Update search text
  const updateSearchText = useCallback((text: string) => {
    const newQuery = { ...query, text };
    setQuery(newQuery);

    // Generate suggestions
    if (finalConfig.enableSuggestions && suggestionsEngine.current) {
      suggestionsEngine.current.generateSuggestions(text, searchContext).then(setSuggestions);
    }

    // Trigger search
    if (text.trim()) {
      debouncedSearch(newQuery);
    } else {
      setResults([]);
      setCurrentIndex(-1);
      setSuggestions([]);
    }
  }, [query, finalConfig.enableSuggestions, searchContext, debouncedSearch]);

  // Update filters
  const updateFilters = useCallback((filters: Partial<SearchFilters>) => {
    const newQuery = { ...query, filters: { ...query.filters, ...filters } };
    setQuery(newQuery);

    if (newQuery.text.trim()) {
      debouncedSearch(newQuery);
    }
  }, [query, debouncedSearch]);

  // Update options
  const updateOptions = useCallback((options: Partial<SearchOptions>) => {
    const newQuery = { ...query, options: { ...query.options, ...options } };
    setQuery(newQuery);

    if (newQuery.text.trim()) {
      debouncedSearch(newQuery);
    }
  }, [query, debouncedSearch]);

  // Navigate results
  const navigateResults = useCallback((direction: 'next' | 'prev') => {
    if (results.length === 0) return;

    setCurrentIndex(prev => {
      if (direction === 'next') {
        return prev < results.length - 1 ? prev + 1 : 0;
      } else {
        return prev > 0 ? prev - 1 : results.length - 1;
      }
    });
  }, [results.length]);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery({
      text: '',
      filters: DEFAULT_FILTERS,
      options: DEFAULT_OPTIONS,
      facets: query.facets,
    });
    setResults([]);
    setCurrentIndex(-1);
    setError(null);
    setSuggestions([]);
  }, [query.facets]);

  // Clear cache
  const clearCache = useCallback(() => {
    searchCache.current.clear();
  }, []);

  // Clear analytics
  const clearAnalytics = useCallback(() => {
    performanceMetrics.current = [];
    setAnalytics({
      totalSearches: 0,
      uniqueQueries: 0,
      averageResultsPerSearch: 0,
      searchSuccessRate: 0,
      topQueries: [],
      recentQueries: [],
      searchTrends: [],
      contentTypeDistribution: [],
      userBehavior: {
        avgSearchLength: 0,
        avgTimeToClick: 0,
        mostActiveHours: [],
        searchPatterns: [],
      },
      performanceMetrics: {
        avgSearchTime: 0,
        avgRenderTime: 0,
        cacheHitRate: 0,
        errorRate: 0,
      },
    });
    if (suggestionsEngine.current) {
      suggestionsEngine.current.clearData();
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Search state
    query,
    results,
    currentIndex,
    isSearching,
    error,
    
    // Suggestions and analytics
    suggestions,
    searchHistory,
    analytics,
    
    // Search actions
    updateSearchText,
    updateFilters,
    updateOptions,
    navigateResults,
    clearSearch,
    performSearch,
    
    // Cache and analytics actions
    clearCache,
    clearAnalytics,
    
    // Utility
    config: finalConfig,
    cacheSize: searchCache.current.size,
    metricsCount: performanceMetrics.current.length,
  };
}
