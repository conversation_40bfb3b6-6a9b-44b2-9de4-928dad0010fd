"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Search, 
  Menu,
  X,
  Maximize,
  Minimize,
  Hand,
  Move,
  TouchIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TouchGestureHandler, type GestureEvent, type GestureCallbacks } from '@/lib/mobile/touch-gestures';

interface MobileControlsProps {
  currentPage: number;
  totalPages: number;
  zoomLevel: number;
  rotation: number;
  onPageChange: (page: number) => void;
  onZoomChange: (zoom: number) => void;
  onRotate: () => void;
  onSearch: () => void;
  onToggleMenu: () => void;
  onToggleFullscreen: () => void;
  className?: string;
}

export default function MobileControls({
  currentPage,
  totalPages,
  zoomLevel,
  rotation,
  onPageChange,
  onZoomChange,
  onRotate,
  onSearch,
  onToggleMenu,
  onToggleFullscreen,
  className,
}: MobileControlsProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [gestureInfo, setGestureInfo] = useState<string>('');
  const [lastGesture, setLastGesture] = useState<GestureEvent | null>(null);
  const [hideTimer, setHideTimer] = useState<number | null>(null);

  const controlsRef = useRef<HTMLDivElement>(null);
  const gestureHandlerRef = useRef<TouchGestureHandler | null>(null);

  // Auto-hide controls after inactivity
  const resetHideTimer = useCallback(() => {
    if (hideTimer) {
      clearTimeout(hideTimer);
    }
    
    setIsVisible(true);
    const timer = window.setTimeout(() => {
      setIsVisible(false);
    }, 3000);
    
    setHideTimer(timer);
  }, [hideTimer]);

  // Setup gesture handling
  useEffect(() => {
    if (!controlsRef.current) return;

    const gestureCallbacks: GestureCallbacks = {
      onTap: (event) => {
        setGestureInfo('Tap detected');
        setLastGesture(event);
        resetHideTimer();
      },
      
      onDoubleTap: (event) => {
        setGestureInfo('Double tap - Toggle zoom');
        setLastGesture(event);
        // Toggle between fit and 200% zoom
        const newZoom = zoomLevel > 1.5 ? 1 : 2;
        onZoomChange(newZoom);
      },
      
      onLongPress: (event) => {
        setGestureInfo('Long press - Show menu');
        setLastGesture(event);
        onToggleMenu();
      },
      
      onPanStart: (event) => {
        setGestureInfo('Pan started');
        setLastGesture(event);
      },
      
      onPanMove: (event) => {
        setGestureInfo(`Panning: ${Math.round(event.deltaX)}, ${Math.round(event.deltaY)}`);
        setLastGesture(event);
      },
      
      onPanEnd: (event) => {
        setGestureInfo('Pan ended');
        setLastGesture(event);
        
        // Page navigation based on horizontal pan
        if (Math.abs(event.deltaX) > 100) {
          if (event.deltaX > 0 && currentPage > 1) {
            onPageChange(currentPage - 1);
          } else if (event.deltaX < 0 && currentPage < totalPages) {
            onPageChange(currentPage + 1);
          }
        }
      },
      
      onPinchStart: (event) => {
        setGestureInfo('Pinch started');
        setLastGesture(event);
      },
      
      onPinchMove: (event) => {
        setGestureInfo(`Pinching: ${event.scale.toFixed(2)}x`);
        setLastGesture(event);
        
        // Apply zoom based on pinch scale
        const newZoom = Math.max(0.5, Math.min(3, zoomLevel * event.scale));
        onZoomChange(newZoom);
      },
      
      onPinchEnd: (event) => {
        setGestureInfo('Pinch ended');
        setLastGesture(event);
      },
      
      onSwipe: (event) => {
        setGestureInfo(`Swipe ${event.direction} - velocity: ${event.velocity.toFixed(2)}`);
        setLastGesture(event);
        
        // Page navigation based on swipe direction
        if (event.direction === 'left' && currentPage < totalPages) {
          onPageChange(currentPage + 1);
        } else if (event.direction === 'right' && currentPage > 1) {
          onPageChange(currentPage - 1);
        }
      },
      
      onRotateStart: (event) => {
        setGestureInfo('Rotation started');
        setLastGesture(event);
      },
      
      onRotateMove: (event) => {
        setGestureInfo(`Rotating: ${Math.round(event.rotation)}°`);
        setLastGesture(event);
      },
      
      onRotateEnd: (event) => {
        setGestureInfo('Rotation ended');
        setLastGesture(event);
        
        // Rotate document if rotation is significant
        if (Math.abs(event.rotation) > 45) {
          onRotate();
        }
      },
    };

    gestureHandlerRef.current = new TouchGestureHandler(
      controlsRef.current,
      gestureCallbacks,
      {
        enableHapticFeedback: true,
        preventDefaultTouch: false, // Allow scrolling
      }
    );

    return () => {
      gestureHandlerRef.current?.destroy();
    };
  }, [currentPage, totalPages, zoomLevel, onPageChange, onZoomChange, onRotate, onToggleMenu, resetHideTimer]);

  // Clear gesture info after delay
  useEffect(() => {
    if (gestureInfo) {
      const timer = setTimeout(() => {
        setGestureInfo('');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [gestureInfo]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Cleanup hide timer
  useEffect(() => {
    return () => {
      if (hideTimer) {
        clearTimeout(hideTimer);
      }
    };
  }, [hideTimer]);

  const handleZoomIn = () => {
    onZoomChange(Math.min(3, zoomLevel + 0.25));
    resetHideTimer();
  };

  const handleZoomOut = () => {
    onZoomChange(Math.max(0.5, zoomLevel - 0.25));
    resetHideTimer();
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
    resetHideTimer();
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
    resetHideTimer();
  };

  return (
    <div 
      ref={controlsRef}
      className={cn(
        "fixed inset-0 pointer-events-none z-50 transition-opacity duration-300",
        isVisible ? "opacity-100" : "opacity-0",
        className
      )}
      onTouchStart={resetHideTimer}
      onTouchMove={resetHideTimer}
    >
      {/* Top Controls */}
      <div className="absolute top-4 left-4 right-4 flex justify-between items-center pointer-events-auto">
        <Button
          variant="secondary"
          size="sm"
          onClick={onToggleMenu}
          className="bg-black/50 text-white border-white/20 backdrop-blur-sm"
        >
          <Menu className="h-4 w-4" />
        </Button>

        <div className="flex items-center gap-2">
          <Badge className="bg-black/50 text-white border-white/20 backdrop-blur-sm">
            {currentPage} / {totalPages}
          </Badge>
          
          <Button
            variant="secondary"
            size="sm"
            onClick={onSearch}
            className="bg-black/50 text-white border-white/20 backdrop-blur-sm"
          >
            <Search className="h-4 w-4" />
          </Button>

          <Button
            variant="secondary"
            size="sm"
            onClick={onToggleFullscreen}
            className="bg-black/50 text-white border-white/20 backdrop-blur-sm"
          >
            {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Bottom Controls */}
      <div className="absolute bottom-4 left-4 right-4 pointer-events-auto">
        <Card className="bg-black/50 border-white/20 backdrop-blur-sm">
          <CardContent className="p-4">
            {/* Navigation Controls */}
            <div className="flex items-center justify-between mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePreviousPage}
                disabled={currentPage <= 1}
                className="text-white hover:bg-white/20"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleZoomOut}
                  disabled={zoomLevel <= 0.5}
                  className="text-white hover:bg-white/20"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <Badge variant="outline" className="text-white border-white/20 min-w-[60px]">
                  {Math.round(zoomLevel * 100)}%
                </Badge>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleZoomIn}
                  disabled={zoomLevel >= 3}
                  className="text-white hover:bg-white/20"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRotate}
                  className="text-white hover:bg-white/20"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
                className="text-white hover:bg-white/20"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>

            {/* Page Slider */}
            <div className="space-y-2">
              <Slider
                value={[currentPage]}
                onValueChange={([page]) => onPageChange(page)}
                min={1}
                max={totalPages}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-white/70">
                <span>Page 1</span>
                <span>Page {totalPages}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gesture Feedback */}
      {gestureInfo && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
          <Card className="bg-black/70 border-white/20 backdrop-blur-sm">
            <CardContent className="p-3">
              <div className="flex items-center gap-2 text-white text-sm">
                <TouchIcon className="h-4 w-4" />
                {gestureInfo}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Gesture Debug Info (Development only) */}
      {process.env.NODE_ENV === 'development' && lastGesture && (
        <div className="absolute top-20 left-4 pointer-events-none">
          <Card className="bg-black/70 border-white/20 backdrop-blur-sm">
            <CardContent className="p-2">
              <div className="text-xs text-white/70 space-y-1">
                <div>Type: {lastGesture.type}</div>
                <div>Delta: {Math.round(lastGesture.deltaX)}, {Math.round(lastGesture.deltaY)}</div>
                <div>Distance: {Math.round(lastGesture.distance)}</div>
                <div>Velocity: {lastGesture.velocity.toFixed(2)}</div>
                {lastGesture.direction && <div>Direction: {lastGesture.direction}</div>}
                {lastGesture.scale !== 1 && <div>Scale: {lastGesture.scale.toFixed(2)}</div>}
                {lastGesture.rotation !== 0 && <div>Rotation: {Math.round(lastGesture.rotation)}°</div>}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Touch Hints */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-none">
        <Card className="bg-black/50 border-white/20 backdrop-blur-sm">
          <CardContent className="p-2">
            <div className="text-xs text-white/70 text-center space-y-1">
              <div className="flex items-center gap-2">
                <Hand className="h-3 w-3" />
                <span>Swipe: Navigate • Pinch: Zoom • Double tap: Fit</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Hook for mobile detection and orientation
export function useMobileControls() {
  const [isMobile, setIsMobile] = useState(false);
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };

    const checkOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    checkMobile();
    checkOrientation();

    window.addEventListener('resize', checkMobile);
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  return {
    isMobile,
    orientation,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
  };
}
