// Annotation components
export { default as PDFAnnotations } from "./pdf-annotations";
export { default as PDFAnnotationOverlay } from "./pdf-annotation-overlay";
export { default as PDFAnnotationExport } from "./pdf-annotation-export";
export { default as PDFHighlightOverlay } from "./pdf-highlight-overlay";

// Advanced annotation components
export { default as AdvancedAnnotationManager } from "./advanced-annotation-manager";
export { default as AdvancedAnnotationCanvas } from "./advanced-annotation-canvas";
export { default as AdvancedDrawingTools } from "./advanced-drawing-tools";
export { default as StampSignatureTools } from "./stamp-signature-tools";
export { default as MultimediaAnnotationTools } from "./multimedia-annotation-tools";

// Annotation hooks and utilities
export { useAnnotationHistory } from "./pdf-annotation-history";

// Re-export types
export type {
  Annotation,
  AnnotationType,
  AnnotationReply,
  AnnotationFilterOptions,
} from "./pdf-annotations";

// Re-export advanced types
export type {
  AdvancedAnnotation,
  AnnotationTemplate,
  DrawingPath,
  AnnotationStyle,
} from "@/lib/annotations/annotation-engine";
