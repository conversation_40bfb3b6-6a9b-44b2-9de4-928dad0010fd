/**
 * Service Worker for Advanced PDF Caching
 * Provides offline support and background caching for PDF documents
 */

const CACHE_NAME = 'cobalt-pdf-cache-v1';
const STATIC_CACHE_NAME = 'cobalt-static-cache-v1';
const PDF_CACHE_NAME = 'cobalt-pdf-documents-v1';
const THUMBNAIL_CACHE_NAME = 'cobalt-thumbnails-v1';

// Cache configuration
const CACHE_CONFIG = {
  maxPDFSize: 50 * 1024 * 1024, // 50MB max PDF size
  maxThumbnailAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxPDFAge: 30 * 24 * 60 * 60 * 1000, // 30 days
  maxCacheSize: 500 * 1024 * 1024, // 500MB total cache
};

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/pdf.worker.min.js',
  // Add other static assets as needed
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker');
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      caches.open(PDF_CACHE_NAME),
      caches.open(THUMBNAIL_CACHE_NAME),
    ])
  );
  
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== STATIC_CACHE_NAME &&
              cacheName !== PDF_CACHE_NAME &&
              cacheName !== THUMBNAIL_CACHE_NAME
            ) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Clean up expired entries
      cleanupExpiredEntries(),
      // Claim all clients
      self.clients.claim(),
    ])
  );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle different types of requests
  if (isPDFRequest(request)) {
    event.respondWith(handlePDFRequest(request));
  } else if (isThumbnailRequest(request)) {
    event.respondWith(handleThumbnailRequest(request));
  } else if (isStaticAsset(request)) {
    event.respondWith(handleStaticAssetRequest(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else {
    // Default: network first, cache fallback
    event.respondWith(handleDefaultRequest(request));
  }
});

// Message event - handle commands from main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data;

  switch (type) {
    case 'CACHE_PDF':
      handleCachePDFMessage(data, event);
      break;
    case 'CACHE_THUMBNAIL':
      handleCacheThumbnailMessage(data, event);
      break;
    case 'CLEAR_CACHE':
      handleClearCacheMessage(data, event);
      break;
    case 'GET_CACHE_STATS':
      handleGetCacheStatsMessage(event);
      break;
    default:
      console.log('[SW] Unknown message type:', type);
  }
});

// Helper functions

function isPDFRequest(request) {
  return (
    request.url.includes('.pdf') ||
    request.headers.get('accept')?.includes('application/pdf') ||
    request.url.includes('/api/pdf/')
  );
}

function isThumbnailRequest(request) {
  return (
    request.url.includes('/api/thumbnails/') ||
    request.url.includes('thumbnail') ||
    request.headers.get('x-content-type') === 'thumbnail'
  );
}

function isStaticAsset(request) {
  const url = new URL(request.url);
  return (
    url.pathname.startsWith('/_next/static/') ||
    url.pathname.includes('.js') ||
    url.pathname.includes('.css') ||
    url.pathname.includes('.woff') ||
    url.pathname === '/pdf.worker.min.js'
  );
}

function isAPIRequest(request) {
  return request.url.includes('/api/');
}

// PDF request handler - cache first, network fallback
async function handlePDFRequest(request) {
  const cache = await caches.open(PDF_CACHE_NAME);
  const cacheKey = generatePDFCacheKey(request);
  
  try {
    // Check cache first
    const cachedResponse = await cache.match(cacheKey);
    if (cachedResponse) {
      console.log('[SW] PDF cache hit:', request.url);
      return cachedResponse;
    }

    // Fetch from network
    console.log('[SW] PDF cache miss, fetching:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Check size before caching
      const contentLength = networkResponse.headers.get('content-length');
      if (contentLength && parseInt(contentLength) <= CACHE_CONFIG.maxPDFSize) {
        // Clone response for caching
        const responseToCache = networkResponse.clone();
        await cache.put(cacheKey, responseToCache);
        console.log('[SW] PDF cached:', request.url);
      }
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] PDF request failed:', error);
    
    // Try to return cached version as fallback
    const cachedResponse = await cache.match(cacheKey);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page or error response
    return new Response('PDF not available offline', { status: 503 });
  }
}

// Thumbnail request handler - cache first with longer TTL
async function handleThumbnailRequest(request) {
  const cache = await caches.open(THUMBNAIL_CACHE_NAME);
  
  try {
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      // Check if thumbnail is still fresh
      const cacheDate = new Date(cachedResponse.headers.get('sw-cache-date') || 0);
      const isExpired = Date.now() - cacheDate.getTime() > CACHE_CONFIG.maxThumbnailAge;
      
      if (!isExpired) {
        console.log('[SW] Thumbnail cache hit:', request.url);
        return cachedResponse;
      }
    }

    // Fetch fresh thumbnail
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Add cache date header and store
      const responseToCache = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: {
          ...Object.fromEntries(networkResponse.headers.entries()),
          'sw-cache-date': new Date().toISOString(),
        },
      });
      
      await cache.put(request, responseToCache.clone());
      console.log('[SW] Thumbnail cached:', request.url);
      return responseToCache;
    }
    
    return networkResponse;
  } catch (error) {
    console.error('[SW] Thumbnail request failed:', error);
    
    // Return cached version if available
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response('Thumbnail not available', { status: 503 });
  }
}

// Static asset handler - cache first
async function handleStaticAssetRequest(request) {
  const cache = await caches.open(STATIC_CACHE_NAME);
  
  const cachedResponse = await cache.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      await cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('[SW] Static asset request failed:', error);
    return new Response('Asset not available', { status: 503 });
  }
}

// API request handler - network first
async function handleAPIRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.error('[SW] API request failed:', error);
    return new Response('API not available', { status: 503 });
  }
}

// Default request handler
async function handleDefaultRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    // Try to serve from cache
    const cache = await caches.open(STATIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response('Content not available offline', { status: 503 });
  }
}

// Message handlers

async function handleCachePDFMessage(data, event) {
  try {
    const { url, documentId } = data;
    const cache = await caches.open(PDF_CACHE_NAME);
    
    const response = await fetch(url);
    if (response.ok) {
      const cacheKey = `pdf-${documentId}`;
      await cache.put(cacheKey, response);
      
      event.ports[0]?.postMessage({ success: true, documentId });
    } else {
      event.ports[0]?.postMessage({ success: false, error: 'Failed to fetch PDF' });
    }
  } catch (error) {
    event.ports[0]?.postMessage({ success: false, error: error.message });
  }
}

async function handleCacheThumbnailMessage(data, event) {
  try {
    const { imageData, documentId, pageNumber } = data;
    const cache = await caches.open(THUMBNAIL_CACHE_NAME);
    
    const response = new Response(imageData, {
      headers: {
        'Content-Type': 'image/jpeg',
        'sw-cache-date': new Date().toISOString(),
      },
    });
    
    const cacheKey = `thumbnail-${documentId}-${pageNumber}`;
    await cache.put(cacheKey, response);
    
    event.ports[0]?.postMessage({ success: true, documentId, pageNumber });
  } catch (error) {
    event.ports[0]?.postMessage({ success: false, error: error.message });
  }
}

async function handleClearCacheMessage(data, event) {
  try {
    const { type } = data;
    
    if (type === 'all') {
      await Promise.all([
        caches.delete(PDF_CACHE_NAME),
        caches.delete(THUMBNAIL_CACHE_NAME),
      ]);
    } else if (type === 'pdf') {
      await caches.delete(PDF_CACHE_NAME);
    } else if (type === 'thumbnails') {
      await caches.delete(THUMBNAIL_CACHE_NAME);
    }
    
    event.ports[0]?.postMessage({ success: true });
  } catch (error) {
    event.ports[0]?.postMessage({ success: false, error: error.message });
  }
}

async function handleGetCacheStatsMessage(event) {
  try {
    const stats = await getCacheStats();
    event.ports[0]?.postMessage({ success: true, stats });
  } catch (error) {
    event.ports[0]?.postMessage({ success: false, error: error.message });
  }
}

// Utility functions

function generatePDFCacheKey(request) {
  const url = new URL(request.url);
  return `pdf-${url.pathname}${url.search}`;
}

async function cleanupExpiredEntries() {
  const thumbnailCache = await caches.open(THUMBNAIL_CACHE_NAME);
  const pdfCache = await caches.open(PDF_CACHE_NAME);
  
  // Clean up expired thumbnails
  const thumbnailKeys = await thumbnailCache.keys();
  for (const request of thumbnailKeys) {
    const response = await thumbnailCache.match(request);
    if (response) {
      const cacheDate = new Date(response.headers.get('sw-cache-date') || 0);
      if (Date.now() - cacheDate.getTime() > CACHE_CONFIG.maxThumbnailAge) {
        await thumbnailCache.delete(request);
      }
    }
  }
  
  // Clean up old PDFs
  const pdfKeys = await pdfCache.keys();
  for (const request of pdfKeys) {
    const response = await pdfCache.match(request);
    if (response) {
      const cacheDate = new Date(response.headers.get('date') || 0);
      if (Date.now() - cacheDate.getTime() > CACHE_CONFIG.maxPDFAge) {
        await pdfCache.delete(request);
      }
    }
  }
}

async function getCacheStats() {
  const cacheNames = [PDF_CACHE_NAME, THUMBNAIL_CACHE_NAME, STATIC_CACHE_NAME];
  const stats = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    stats[cacheName] = {
      entryCount: keys.length,
      // Note: Can't easily get size without reading all responses
    };
  }
  
  return stats;
}
