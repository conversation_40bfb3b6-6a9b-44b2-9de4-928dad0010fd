import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import VirtualizedPDFViewer from '@/components/core/virtualized-pdf-viewer';

// Mock the virtual renderer
vi.mock('@/lib/virtual-renderer', () => ({
  VirtualPageRenderer: vi.fn().mockImplementation(() => ({
    initialize: vi.fn().mockResolvedValue(undefined),
    updateVisibleRange: vi.fn(),
    getState: vi.fn().mockReturnValue({
      pages: new Map([
        [1, { pageNumber: 1, height: 792, width: 612, isVisible: true, isLoaded: true, isLoading: false }],
        [2, { pageNumber: 2, height: 792, width: 612, isVisible: false, isLoaded: false, isLoading: false }],
      ]),
      visibleRange: { start: 1, end: 1 },
      cachedPages: new Set([1]),
      loadingPages: new Set(),
      metrics: { renderTimes: new Map(), averageRenderTime: 50 },
    }),
    updateConfig: vi.fn(),
    destroy: vi.fn(),
  })),
  DEFAULT_VIRTUAL_CONFIG: {
    viewportHeight: 800,
    viewportWidth: 600,
    overscanCount: 2,
    maxConcurrentRenders: 3,
    maxCachedPages: 10,
  },
}));

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Document: ({ children, onLoadSuccess, onLoadError }: any) => {
    // Simulate successful load immediately for tests
    React.useEffect(() => {
      const timer = setTimeout(() => {
        onLoadSuccess?.({ numPages: 5 });
      }, 10); // Reduced timeout for faster tests

      return () => clearTimeout(timer);
    }, [onLoadSuccess]);

    return <div data-testid="pdf-document">{children}</div>;
  },
}));

// Mock PDF simple page
vi.mock('@/components/core/pdf-simple-page', () => ({
  default: ({ pageNumber, isVirtualPlaceholder, virtualHeight, virtualWidth }: any) => {
    if (isVirtualPlaceholder) {
      return (
        <div 
          data-testid={`virtual-placeholder-${pageNumber}`}
          style={{ height: virtualHeight, width: virtualWidth }}
        >
          Virtual Placeholder Page {pageNumber}
        </div>
      );
    }
    return <div data-testid={`pdf-page-${pageNumber}`}>PDF Page {pageNumber}</div>;
  },
}));

// Mock utils
vi.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

// Mock PDF types
vi.mock('@/lib/types/pdf', () => ({
  extractPDFDocument: vi.fn().mockReturnValue({
    numPages: 5,
    getPage: vi.fn().mockResolvedValue({
      getViewport: vi.fn().mockReturnValue({ width: 612, height: 792 }),
    }),
  }),
}));

describe('VirtualizedPDFViewer', () => {
  const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
  const defaultProps = {
    file: mockFile,
    scale: 1.0,
    rotation: 0,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render loading state initially', () => {
      render(<VirtualizedPDFViewer {...defaultProps} />);
      
      expect(screen.getByText(/initializing virtual renderer/i)).toBeInTheDocument();
    });

    it('should render PDF document after loading', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });

    it('should render without virtualization when disabled', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={false} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });
  });

  describe('Virtualization Features', () => {
    it('should show performance stats in development mode', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      await waitFor(() => {
        expect(screen.getByText(/rendered:/i)).toBeInTheDocument();
        expect(screen.getByText(/cached:/i)).toBeInTheDocument();
        expect(screen.getByText(/avg render:/i)).toBeInTheDocument();
      });

      process.env.NODE_ENV = originalEnv;
    });

    it('should not show performance stats in production mode', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      expect(screen.queryByText(/rendered:/i)).not.toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });

    it('should handle scroll events for virtualization', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      const scrollContainer = screen.getByTestId('pdf-document').parentElement;
      if (scrollContainer) {
        fireEvent.scroll(scrollContainer, { target: { scrollTop: 500 } });
      }

      // Should not throw errors
      expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
    });
  });

  describe('Virtual vs Real Page Rendering', () => {
    it('should render virtual placeholders for non-visible pages', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      // Should render actual page for visible pages
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
    });

    it('should render all pages when virtualization is disabled', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={false} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      // Should render all pages
      expect(screen.getByTestId('pdf-page-1')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-page-2')).toBeInTheDocument();
    });
  });

  describe('Configuration', () => {
    it('should accept custom virtual configuration', async () => {
      const customConfig = {
        maxCachedPages: 20,
        overscanCount: 3,
      };

      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          enableVirtualization={true}
          virtualConfig={customConfig}
        />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });

    it('should handle scale changes', async () => {
      const { rerender } = render(
        <VirtualizedPDFViewer {...defaultProps} scale={1.0} enableVirtualization={true} />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      rerender(
        <VirtualizedPDFViewer {...defaultProps} scale={1.5} enableVirtualization={true} />
      );

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
    });

    it('should handle rotation changes', async () => {
      const { rerender } = render(
        <VirtualizedPDFViewer {...defaultProps} rotation={0} enableVirtualization={true} />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      rerender(
        <VirtualizedPDFViewer {...defaultProps} rotation={90} enableVirtualization={true} />
      );

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
    });
  });

  describe('Feature Integration', () => {
    it('should pass through annotation props', async () => {
      const mockAnnotations = [
        { id: '1', pageNumber: 1, type: 'highlight' as const, timestamp: Date.now() }
      ];

      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          annotations={mockAnnotations}
          enableAnnotations={true}
        />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });

    it('should pass through form props', async () => {
      const mockFormFields = [
        { 
          id: '1', 
          name: 'field1', 
          type: 'text' as const, 
          value: '', 
          position: { pageNumber: 1, x: 0, y: 0, width: 100, height: 20 },
          appearance: { fontSize: 12, fontColor: '#000', backgroundColor: '#fff', borderColor: '#000', borderWidth: 1 }
        }
      ];

      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          formFields={mockFormFields}
          enableForms={true}
        />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });

    it('should handle search functionality', async () => {
      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          searchText="test search"
          enableSearch={true}
        />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });
  });

  describe('Callbacks', () => {
    it('should call onDocumentLoadSuccess', async () => {
      const onLoadSuccess = vi.fn();
      
      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          onDocumentLoadSuccess={onLoadSuccess}
        />
      );
      
      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith({ numPages: 5 });
      });
    });

    it('should call onDocumentLoadError on error', async () => {
      const onLoadError = vi.fn();
      const errorFile = 'invalid-file';
      
      render(
        <VirtualizedPDFViewer 
          file={errorFile}
          onDocumentLoadError={onLoadError}
        />
      );
      
      // Error handling would be tested with actual error scenarios
    });

    it('should call onPageChange when provided', async () => {
      const onPageChange = vi.fn();
      
      render(
        <VirtualizedPDFViewer 
          {...defaultProps} 
          onPageChange={onPageChange}
        />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />);
      
      // Should not crash
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });

    it('should handle missing virtual renderer gracefully', async () => {
      render(<VirtualizedPDFViewer {...defaultProps} enableVirtualization={false} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });
    });
  });

  describe('Cleanup', () => {
    it('should cleanup virtual renderer on unmount', async () => {
      const { unmount } = render(
        <VirtualizedPDFViewer {...defaultProps} enableVirtualization={true} />
      );
      
      await waitFor(() => {
        expect(screen.getByTestId('pdf-document')).toBeInTheDocument();
      });

      unmount();
      
      // Should not throw errors during cleanup
    });
  });
});
