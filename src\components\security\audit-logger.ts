export interface AuditEvent {
  id: string;
  timestamp: number;
  documentId: string;
  userId?: string;
  userEmail?: string;
  sessionId: string;
  action: AuditAction;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export type AuditAction = 
  | 'document_open'
  | 'document_close'
  | 'document_view'
  | 'document_print'
  | 'document_download'
  | 'document_copy'
  | 'document_share'
  | 'annotation_add'
  | 'annotation_edit'
  | 'annotation_delete'
  | 'permission_change'
  | 'access_denied'
  | 'login_attempt'
  | 'logout'
  | 'encryption_change'
  | 'watermark_change'
  | 'search_performed'
  | 'export_performed'
  | 'comparison_performed'
  | 'presentation_started'
  | 'presentation_ended';

export interface AuditFilter {
  documentId?: string;
  userId?: string;
  actions?: AuditAction[];
  startDate?: Date;
  endDate?: Date;
  success?: boolean;
  ipAddress?: string;
}

export interface AuditSummary {
  totalEvents: number;
  uniqueUsers: number;
  uniqueDocuments: number;
  actionCounts: Record<AuditAction, number>;
  successRate: number;
  timeRange: { start: Date; end: Date };
}

export class AuditLogger {
  private static instance: AuditLogger;
  private events: AuditEvent[] = [];
  private maxEvents: number = 10000;
  private storageKey = 'cobalt_audit_log';

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  constructor() {
    this.loadFromStorage();
  }

  /**
   * Log an audit event
   */
  log(
    documentId: string,
    action: AuditAction,
    details: Record<string, any> = {},
    userContext: {
      userId?: string;
      userEmail?: string;
      sessionId?: string;
      ipAddress?: string;
      userAgent?: string;
    } = {},
    success: boolean = true,
    errorMessage?: string
  ): void {
    const event: AuditEvent = {
      id: this.generateEventId(),
      timestamp: Date.now(),
      documentId,
      userId: userContext.userId,
      userEmail: userContext.userEmail,
      sessionId: userContext.sessionId || this.generateSessionId(),
      action,
      details,
      ipAddress: userContext.ipAddress,
      userAgent: userContext.userAgent || navigator.userAgent,
      success,
      errorMessage,
      metadata: {
        url: window.location.href,
        referrer: document.referrer
      }
    };

    this.events.push(event);
    
    // Maintain max events limit
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    this.saveToStorage();
    this.notifyListeners(event);
  }

  /**
   * Get filtered audit events
   */
  getEvents(filter: AuditFilter = {}): AuditEvent[] {
    return this.events.filter(event => {
      if (filter.documentId && event.documentId !== filter.documentId) return false;
      if (filter.userId && event.userId !== filter.userId) return false;
      if (filter.actions && !filter.actions.includes(event.action)) return false;
      if (filter.startDate && event.timestamp < filter.startDate.getTime()) return false;
      if (filter.endDate && event.timestamp > filter.endDate.getTime()) return false;
      if (filter.success !== undefined && event.success !== filter.success) return false;
      if (filter.ipAddress && event.ipAddress !== filter.ipAddress) return false;
      
      return true;
    }).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get audit summary
   */
  getSummary(filter: AuditFilter = {}): AuditSummary {
    const filteredEvents = this.getEvents(filter);
    
    const uniqueUsers = new Set(filteredEvents.map(e => e.userId).filter(Boolean)).size;
    const uniqueDocuments = new Set(filteredEvents.map(e => e.documentId)).size;
    
    const actionCounts = {} as Record<AuditAction, number>;
    filteredEvents.forEach(event => {
      actionCounts[event.action] = (actionCounts[event.action] || 0) + 1;
    });

    const successfulEvents = filteredEvents.filter(e => e.success).length;
    const successRate = filteredEvents.length > 0 ? (successfulEvents / filteredEvents.length) * 100 : 0;

    const timestamps = filteredEvents.map(e => e.timestamp);
    const timeRange = {
      start: timestamps.length > 0 ? new Date(Math.min(...timestamps)) : new Date(),
      end: timestamps.length > 0 ? new Date(Math.max(...timestamps)) : new Date()
    };

    return {
      totalEvents: filteredEvents.length,
      uniqueUsers,
      uniqueDocuments,
      actionCounts,
      successRate,
      timeRange
    };
  }

  /**
   * Export audit log
   */
  export(format: 'json' | 'csv' = 'json', filter: AuditFilter = {}): string {
    const events = this.getEvents(filter);
    
    if (format === 'csv') {
      const headers = [
        'ID', 'Timestamp', 'Document ID', 'User ID', 'User Email', 'Session ID',
        'Action', 'Success', 'IP Address', 'User Agent', 'Error Message', 'Details'
      ];
      
      const rows = events.map(event => [
        event.id,
        new Date(event.timestamp).toISOString(),
        event.documentId,
        event.userId || '',
        event.userEmail || '',
        event.sessionId,
        event.action,
        event.success.toString(),
        event.ipAddress || '',
        event.userAgent || '',
        event.errorMessage || '',
        JSON.stringify(event.details)
      ]);

      return [headers, ...rows].map(row => 
        row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',')
      ).join('\n');
    }

    return JSON.stringify(events, null, 2);
  }

  /**
   * Clear audit log
   */
  clear(): void {
    this.events = [];
    this.saveToStorage();
  }

  /**
   * Get events for a specific document
   */
  getDocumentEvents(documentId: string): AuditEvent[] {
    return this.getEvents({ documentId });
  }

  /**
   * Get events for a specific user
   */
  getUserEvents(userId: string): AuditEvent[] {
    return this.getEvents({ userId });
  }

  /**
   * Get recent events
   */
  getRecentEvents(limit: number = 50): AuditEvent[] {
    return this.events
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Get security events (failed access attempts, permission changes, etc.)
   */
  getSecurityEvents(): AuditEvent[] {
    const securityActions: AuditAction[] = [
      'access_denied',
      'login_attempt',
      'permission_change',
      'encryption_change'
    ];
    
    return this.getEvents({ actions: securityActions });
  }

  /**
   * Detect suspicious activity
   */
  detectSuspiciousActivity(): {
    type: string;
    description: string;
    events: AuditEvent[];
    severity: 'low' | 'medium' | 'high';
  }[] {
    const suspicious = [];
    const recentEvents = this.getRecentEvents(1000);
    
    // Multiple failed access attempts from same IP
    const failedAttempts = recentEvents.filter(e => 
      e.action === 'access_denied' && 
      e.timestamp > Date.now() - 60 * 60 * 1000 // Last hour
    );
    
    const ipFailures = new Map<string, AuditEvent[]>();
    failedAttempts.forEach(event => {
      if (event.ipAddress) {
        if (!ipFailures.has(event.ipAddress)) {
          ipFailures.set(event.ipAddress, []);
        }
        ipFailures.get(event.ipAddress)!.push(event);
      }
    });

    ipFailures.forEach((events, ip) => {
      if (events.length >= 5) {
        suspicious.push({
          type: 'multiple_failed_access',
          description: `Multiple failed access attempts from IP ${ip}`,
          events,
          severity: events.length >= 10 ? 'high' : 'medium' as const
        });
      }
    });

    // Unusual access patterns (access outside normal hours)
    const accessEvents = recentEvents.filter(e => e.action === 'document_open');
    const unusualHours = accessEvents.filter(event => {
      const hour = new Date(event.timestamp).getHours();
      return hour < 6 || hour > 22; // Outside 6 AM - 10 PM
    });

    if (unusualHours.length >= 3) {
      suspicious.push({
        type: 'unusual_access_hours',
        description: 'Document access during unusual hours',
        events: unusualHours,
        severity: 'low'
      });
    }

    // Rapid document access (potential automated access)
    const rapidAccess = [];
    for (let i = 1; i < accessEvents.length; i++) {
      const timeDiff = accessEvents[i-1].timestamp - accessEvents[i].timestamp;
      if (timeDiff < 1000 && accessEvents[i-1].userId === accessEvents[i].userId) { // Less than 1 second
        rapidAccess.push(accessEvents[i-1], accessEvents[i]);
      }
    }

    if (rapidAccess.length >= 4) {
      suspicious.push({
        type: 'rapid_access',
        description: 'Unusually rapid document access pattern',
        events: rapidAccess,
        severity: 'medium'
      });
    }

    return suspicious;
  }

  // Event listeners for real-time monitoring
  private listeners: ((event: AuditEvent) => void)[] = [];

  addListener(callback: (event: AuditEvent) => void): void {
    this.listeners.push(callback);
  }

  removeListener(callback: (event: AuditEvent) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  private notifyListeners(event: AuditEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in audit event listener:', error);
      }
    });
  }

  // Storage management
  private saveToStorage(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.events));
    } catch (error) {
      console.error('Failed to save audit log to storage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        this.events = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load audit log from storage:', error);
      this.events = [];
    }
  }

  // Utility methods
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance
export const auditLogger = AuditLogger.getInstance();

// Convenience functions for common audit events
export const logDocumentOpen = (documentId: string, userContext?: any) => {
  auditLogger.log(documentId, 'document_open', { action: 'opened' }, userContext);
};

export const logDocumentView = (documentId: string, pageNumber: number, userContext?: any) => {
  auditLogger.log(documentId, 'document_view', { pageNumber }, userContext);
};

export const logDocumentPrint = (documentId: string, pages: number[], userContext?: any) => {
  auditLogger.log(documentId, 'document_print', { pages }, userContext);
};

export const logAccessDenied = (documentId: string, reason: string, userContext?: any) => {
  auditLogger.log(documentId, 'access_denied', { reason }, userContext, false, reason);
};

export const logAnnotationAdd = (documentId: string, annotationType: string, userContext?: any) => {
  auditLogger.log(documentId, 'annotation_add', { type: annotationType }, userContext);
};

export const logPermissionChange = (documentId: string, changes: any, userContext?: any) => {
  auditLogger.log(documentId, 'permission_change', { changes }, userContext);
};
