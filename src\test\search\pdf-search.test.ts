import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PDFSearchEngine, type SearchOptions } from '@/lib/search/pdf-search';

// Mock PDF.js
const mockTextContent = {
  items: [
    { str: 'Hello', transform: [1, 0, 0, 1, 10, 100], width: 30, height: 12 },
    { str: 'world', transform: [1, 0, 0, 1, 45, 100], width: 35, height: 12 },
    { str: 'This', transform: [1, 0, 0, 1, 10, 85], width: 25, height: 12 },
    { str: 'is', transform: [1, 0, 0, 1, 40, 85], width: 15, height: 12 },
    { str: 'a', transform: [1, 0, 0, 1, 60, 85], width: 8, height: 12 },
    { str: 'test', transform: [1, 0, 0, 1, 75, 85], width: 25, height: 12 },
    { str: 'document', transform: [1, 0, 0, 1, 105, 85], width: 50, height: 12 },
  ],
};

const mockPage = {
  getTextContent: vi.fn().mockResolvedValue(mockTextContent),
};

const mockDocument = {
  numPages: 2,
  getPage: vi.fn().mockResolvedValue(mockPage),
};

describe('PDFSearchEngine', () => {
  let searchEngine: PDFSearchEngine;

  beforeEach(() => {
    vi.clearAllMocks();
    searchEngine = new PDFSearchEngine();
  });

  afterEach(() => {
    searchEngine.destroy();
  });

  describe('Initialization', () => {
    it('should initialize without document', () => {
      expect(searchEngine).toBeDefined();
    });

    it('should set document and extract text', async () => {
      await searchEngine.setDocument(mockDocument as any);
      
      expect(mockDocument.getPage).toHaveBeenCalledWith(1);
      expect(mockDocument.getPage).toHaveBeenCalledWith(2);
      expect(mockPage.getTextContent).toHaveBeenCalledTimes(2);
    });
  });

  describe('Basic Search', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should find simple text matches', async () => {
      const results = await searchEngine.search('Hello');
      
      expect(results).toHaveLength(1);
      expect(results[0].matchText).toBe('Hello');
      expect(results[0].pageNumber).toBe(1);
    });

    it('should find case-insensitive matches by default', async () => {
      const results = await searchEngine.search('hello');
      
      expect(results).toHaveLength(1);
      expect(results[0].matchText).toBe('Hello');
    });

    it('should respect case sensitivity option', async () => {
      const results = await searchEngine.search('hello', { caseSensitive: true });
      
      expect(results).toHaveLength(0);
    });

    it('should find multiple matches', async () => {
      // Mock additional pages with repeated text
      mockPage.getTextContent.mockResolvedValue({
        items: [
          { str: 'test', transform: [1, 0, 0, 1, 10, 100], width: 25, height: 12 },
          { str: 'test', transform: [1, 0, 0, 1, 50, 100], width: 25, height: 12 },
        ],
      });

      await searchEngine.setDocument(mockDocument as any);
      const results = await searchEngine.search('test');
      
      expect(results.length).toBeGreaterThan(0);
    });

    it('should return empty results for empty query', async () => {
      const results = await searchEngine.search('');
      expect(results).toHaveLength(0);
    });

    it('should return empty results for non-existent text', async () => {
      const results = await searchEngine.search('nonexistent');
      expect(results).toHaveLength(0);
    });
  });

  describe('Search Options', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should handle whole word search', async () => {
      const results = await searchEngine.search('test', { wholeWords: true });
      
      expect(results).toHaveLength(1);
      expect(results[0].matchText).toBe('test');
    });

    it('should handle regex search', async () => {
      const results = await searchEngine.search('t.st', { useRegex: true });
      
      expect(results).toHaveLength(1);
      expect(results[0].matchText).toBe('test');
    });

    it('should handle invalid regex gracefully', async () => {
      const results = await searchEngine.search('[invalid', { useRegex: true });
      
      expect(results).toHaveLength(0);
    });

    it('should limit results based on maxResults option', async () => {
      const results = await searchEngine.search('test', { maxResults: 1 });
      
      expect(results.length).toBeLessThanOrEqual(1);
    });
  });

  describe('Fuzzy Search', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should find fuzzy matches', async () => {
      const results = await searchEngine.search('tset', { 
        fuzzySearch: true, 
        fuzzyThreshold: 0.7 
      });
      
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].confidence).toBeLessThan(1);
    });

    it('should respect fuzzy threshold', async () => {
      const strictResults = await searchEngine.search('xyz', { 
        fuzzySearch: true, 
        fuzzyThreshold: 0.9 
      });
      
      const lenientResults = await searchEngine.search('xyz', { 
        fuzzySearch: true, 
        fuzzyThreshold: 0.1 
      });
      
      expect(lenientResults.length).toBeGreaterThanOrEqual(strictResults.length);
    });

    it('should sort fuzzy results by confidence', async () => {
      const results = await searchEngine.search('tst', { 
        fuzzySearch: true, 
        fuzzyThreshold: 0.5 
      });
      
      if (results.length > 1) {
        for (let i = 1; i < results.length; i++) {
          expect(results[i].confidence).toBeLessThanOrEqual(results[i - 1].confidence);
        }
      }
    });
  });

  describe('Search Results', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should include context in results', async () => {
      const results = await searchEngine.search('test');
      
      expect(results[0].context).toBeDefined();
      expect(results[0].context).toContain('test');
    });

    it('should include bounding box information', async () => {
      const results = await searchEngine.search('Hello');
      
      expect(results[0].bounds).toBeDefined();
      expect(results[0].bounds.x).toBeDefined();
      expect(results[0].bounds.y).toBeDefined();
      expect(results[0].bounds.width).toBeDefined();
      expect(results[0].bounds.height).toBeDefined();
    });

    it('should include page number', async () => {
      const results = await searchEngine.search('Hello');
      
      expect(results[0].pageNumber).toBe(1);
    });

    it('should include start and end indices', async () => {
      const results = await searchEngine.search('Hello');
      
      expect(results[0].startIndex).toBeDefined();
      expect(results[0].endIndex).toBeDefined();
      expect(results[0].endIndex).toBeGreaterThan(results[0].startIndex);
    });
  });

  describe('Search Events', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should emit search progress events', (done) => {
      let progressEvents = 0;
      
      searchEngine.addEventListener('search-progress', () => {
        progressEvents++;
      });
      
      searchEngine.addEventListener('search-complete', () => {
        expect(progressEvents).toBeGreaterThan(0);
        done();
      });
      
      searchEngine.search('test');
    });

    it('should emit search complete event', (done) => {
      searchEngine.addEventListener('search-complete', (data) => {
        expect(data.results).toBeDefined();
        expect(data.query).toBe('test');
        done();
      });
      
      searchEngine.search('test');
    });

    it('should emit search error event for invalid operations', (done) => {
      // Remove document to cause error
      searchEngine.setDocument(null as any);
      
      searchEngine.addEventListener('search-error', (data) => {
        expect(data.error).toBeDefined();
        done();
      });
      
      // This should cause an error
      searchEngine.search('test').catch(() => {
        // Expected to fail
      });
    });
  });

  describe('Search Cancellation', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should cancel ongoing search', (done) => {
      searchEngine.addEventListener('search-cancelled', () => {
        done();
      });
      
      searchEngine.search('test');
      searchEngine.cancelSearch();
    });

    it('should not return results for cancelled search', async () => {
      const searchPromise = searchEngine.search('test');
      searchEngine.cancelSearch();

      try {
        const results = await searchPromise;
        expect(results).toHaveLength(0);
      } catch (error) {
        // Search cancellation throws an error, which is expected
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Search cancelled');
      }
    });
  });

  describe('Caching', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should cache search results', async () => {
      const results1 = await searchEngine.search('test');
      const results2 = await searchEngine.search('test');
      
      expect(results1).toEqual(results2);
      // Second search should be faster (cached)
    });

    it('should clear cache when requested', async () => {
      await searchEngine.search('test');
      searchEngine.clearCache();
      
      // Cache should be empty now
      expect(searchEngine).toBeDefined(); // Basic check
    });

    it('should use different cache for different options', async () => {
      const results1 = await searchEngine.search('test', { caseSensitive: false });
      const results2 = await searchEngine.search('test', { caseSensitive: true });
      
      // Results should be different due to different options
      expect(results1.length).not.toBe(results2.length);
    });
  });

  describe('Statistics', () => {
    beforeEach(async () => {
      await searchEngine.setDocument(mockDocument as any);
    });

    it('should track search statistics', async () => {
      await searchEngine.search('test');
      await searchEngine.search('hello');
      
      const stats = searchEngine.getStats();
      expect(stats.totalSearches).toBe(2);
      expect(stats.averageSearchTime).toBeGreaterThan(0);
      expect(stats.mostSearchedTerms.size).toBeGreaterThan(0);
    });

    it('should track most searched terms', async () => {
      await searchEngine.search('test');
      await searchEngine.search('test');
      await searchEngine.search('hello');
      
      const stats = searchEngine.getStats();
      expect(stats.mostSearchedTerms.get('test')).toBe(2);
      expect(stats.mostSearchedTerms.get('hello')).toBe(1);
    });
  });

  describe('Event Listeners', () => {
    it('should add and remove event listeners', () => {
      const listener = vi.fn();
      
      searchEngine.addEventListener('search-complete', listener);
      searchEngine.removeEventListener('search-complete', listener);
      
      // Should not crash
      expect(searchEngine).toBeDefined();
    });

    it('should handle listener errors gracefully', async () => {
      const errorListener = () => {
        throw new Error('Listener error');
      };
      
      searchEngine.addEventListener('search-complete', errorListener);
      
      await searchEngine.setDocument(mockDocument as any);
      
      // Should not crash when search completes
      await searchEngine.search('test');
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources on destroy', () => {
      searchEngine.destroy();
      
      // Should not crash
      expect(searchEngine).toBeDefined();
    });

    it('should cancel search on destroy', async () => {
      await searchEngine.setDocument(mockDocument as any);

      const searchPromise = searchEngine.search('test');
      searchEngine.destroy();

      try {
        const results = await searchPromise;
        expect(results).toHaveLength(0);
      } catch (error) {
        // Search cancellation throws an error, which is expected
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Search cancelled');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle missing document gracefully', async () => {
      const results = await searchEngine.search('test');
      expect(results).toHaveLength(0);
    });

    it('should handle page extraction errors', async () => {
      mockPage.getTextContent.mockRejectedValue(new Error('Extraction failed'));
      
      await searchEngine.setDocument(mockDocument as any);
      const results = await searchEngine.search('test');
      
      // Should not crash, may return empty results
      expect(Array.isArray(results)).toBe(true);
    });

    it('should handle invalid search options', async () => {
      await searchEngine.setDocument(mockDocument as any);
      
      const results = await searchEngine.search('test', {
        fuzzyThreshold: -1, // Invalid threshold
      } as SearchOptions);
      
      expect(Array.isArray(results)).toBe(true);
    });
  });
});
