"use client";

import type React from "react";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Upload,
  Link,
  FileText,
  X,
  Plus,
  Tag,
  Folder,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { documentLibrary } from "@/lib/document-library";
import type { DocumentMetadata } from "@/lib/types/pdf";
import { createDefaultDocumentMetadata } from "@/lib/types/pdf";

interface PDFUploadProps {
  onFileSelect: (file: string | File) => void;
  addToLibrary?: boolean;
  showMetadataForm?: boolean;
  onDocumentAdded?: (documentId: string) => void;
}

export default function PDFUpload({
  onFileSelect,
  addToLibrary = true,
  showMetadataForm = false,
  onDocumentAdded
}: PDFUploadProps) {
  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } | null>(null);

  // Metadata form state
  const [showMetadata, setShowMetadata] = useState(showMetadataForm);
  const [metadata, setMetadata] = useState<Partial<DocumentMetadata>>({
    tags: [],
    categories: [],
    description: '',
    notes: ''
  });
  const [newTag, setNewTag] = useState('');
  const [newCategory, setNewCategory] = useState('');

  // Enhanced file validation
  const validatePDFFile = useCallback(async (file: File): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file type
    if (file.type !== "application/pdf") {
      errors.push("File must be a PDF document");
    }

    // Check file size (max 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push("File size must be less than 50MB");
    }

    // Check file size warning (over 10MB)
    const warningSize = 10 * 1024 * 1024;
    if (file.size > warningSize && file.size <= maxSize) {
      warnings.push("Large file size may affect performance");
    }

    // Basic file integrity check
    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      // Check PDF header
      const header = String.fromCharCode(...uint8Array.slice(0, 4));
      if (header !== '%PDF') {
        errors.push("File does not appear to be a valid PDF");
      }
    } catch (error) {
      errors.push("Unable to read file");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, []);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsValidating(true);
    setValidationResult(null);

    try {
      const validation = await validatePDFFile(file);
      setValidationResult(validation);

      if (validation.isValid) {
        if (addToLibrary) {
          await handleAddToLibrary(file);
        } else {
          onFileSelect(file);
        }
      } else {
        toast.error("Invalid PDF file", {
          description: validation.errors.join(", ")
        });
      }
    } catch (error) {
      toast.error("File validation failed", {
        description: "Unable to validate the selected file"
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleAddToLibrary = async (file: File) => {
    try {
      setIsLoading(true);

      // Create default metadata
      const defaultMetadata = createDefaultDocumentMetadata(file);
      const finalMetadata = { ...defaultMetadata, ...metadata };

      // Add to document library
      const document = await documentLibrary.addDocument(file, finalMetadata);

      toast.success("Document added to library", {
        description: `"${document.title}" has been added to your document library`
      });

      // Call the callback if provided
      onDocumentAdded?.(document.id);

      // Also call onFileSelect to open the document
      onFileSelect(file);

      // Reset form
      setMetadata({
        tags: [],
        categories: [],
        description: '',
        notes: ''
      });
      setShowMetadata(false);
    } catch (error) {
      console.error('Failed to add document to library:', error);
      toast.error("Failed to add to library", {
        description: "The document could not be added to your library"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUrlSubmit = async () => {
    if (!url) return;

    setIsLoading(true);
    try {
      // Validate URL format
      new URL(url);

      if (addToLibrary) {
        // For URLs, we'll add basic metadata
        const urlMetadata = {
          ...metadata,
          fileName: url.split('/').pop() || 'Remote Document',
          filePath: url
        };

        const document = await documentLibrary.addDocument(url, urlMetadata);
        toast.success("Document added to library");
        onDocumentAdded?.(document.id);
      }

      onFileSelect(url);
    } catch {
      toast.error("Invalid URL", {
        description: "Please enter a valid URL.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (!file) return;

    setIsValidating(true);
    setValidationResult(null);

    try {
      const validation = await validatePDFFile(file);
      setValidationResult(validation);

      if (validation.isValid) {
        if (addToLibrary) {
          await handleAddToLibrary(file);
        } else {
          onFileSelect(file);
        }
      } else {
        toast.error("Invalid PDF file", {
          description: validation.errors.join(", ")
        });
      }
    } catch (error) {
      toast.error("File validation failed", {
        description: "Unable to validate the dropped file"
      });
    } finally {
      setIsValidating(false);
    }
  };

  // Metadata form helpers
  const addTag = () => {
    if (newTag.trim() && !metadata.tags?.includes(newTag.trim())) {
      setMetadata(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const addCategory = () => {
    if (newCategory.trim() && !metadata.categories?.includes(newCategory.trim())) {
      setMetadata(prev => ({
        ...prev,
        categories: [...(prev.categories || []), newCategory.trim()]
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (categoryToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      categories: prev.categories?.filter(cat => cat !== categoryToRemove) || []
    }));
  };

  return (
    <div className="container mx-auto mobile-padding lg:px-4 py-4 lg:py-8 max-w-2xl safe-area-top safe-area-bottom">
      <div className="text-center mb-6 lg:mb-8">
        <FileText className="mx-auto h-12 w-12 lg:h-16 lg:w-16 text-primary mb-3 lg:mb-4" />
        <h1 className="text-2xl lg:text-4xl font-bold mb-2">PDF Viewer</h1>
        <p className="text-muted-foreground text-base lg:text-lg">
          Upload a PDF file or provide a URL to get started
        </p>
        <p className="text-xs lg:text-sm text-muted-foreground mt-2">
          ✨ Features: Navigation • Zoom • Search • Bookmarks • Document Library
        </p>
      </div>

      {/* Validation Results */}
      {validationResult && (
        <div className="mb-4">
          {validationResult.isValid ? (
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-800">File validation successful</span>
            </div>
          ) : (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Validation failed</span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {validationResult.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
          {validationResult.warnings.length > 0 && (
            <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Warnings</span>
              </div>
              <ul className="text-sm text-yellow-700 space-y-1">
                {validationResult.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-2 touch-target">
          <TabsTrigger value="upload" className="touch-target">Upload File</TabsTrigger>
          <TabsTrigger value="url" className="touch-target">From URL</TabsTrigger>
        </TabsList>

        <TabsContent value="upload">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload PDF File
              </CardTitle>
              <CardDescription>
                Select a PDF file from your device
              </CardDescription>
            </CardHeader>
            <CardContent className="mobile-padding lg:p-6">
              <div
                className={cn(
                  "border-2 border-dashed border-muted-foreground/25 rounded-lg mobile-padding lg:p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer touch-manipulation min-h-[120px] lg:min-h-[160px] flex flex-col items-center justify-center",
                  (isValidating || isLoading) && "pointer-events-none opacity-50"
                )}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={() => !isValidating && !isLoading && document.getElementById("file-input")?.click()}
                role="button"
                tabIndex={0}
                aria-label="Upload PDF file"
                onKeyDown={(e) => {
                  if ((e.key === 'Enter' || e.key === ' ') && !isValidating && !isLoading) {
                    document.getElementById("file-input")?.click();
                  }
                }}
              >
                {isValidating ? (
                  <>
                    <Loader2 className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-primary mb-3 lg:mb-4 animate-spin" />
                    <p className="text-base lg:text-lg font-medium mb-2">
                      Validating PDF file...
                    </p>
                    <p className="text-xs lg:text-sm text-muted-foreground">
                      Please wait while we check your file
                    </p>
                  </>
                ) : isLoading ? (
                  <>
                    <Loader2 className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-primary mb-3 lg:mb-4 animate-spin" />
                    <p className="text-base lg:text-lg font-medium mb-2">
                      {addToLibrary ? 'Adding to library...' : 'Processing file...'}
                    </p>
                    <p className="text-xs lg:text-sm text-muted-foreground">
                      This may take a moment
                    </p>
                  </>
                ) : (
                  <>
                    <Upload className="mx-auto h-8 w-8 lg:h-12 lg:w-12 text-muted-foreground mb-3 lg:mb-4" />
                    <p className="text-base lg:text-lg font-medium mb-2">
                      Drop your PDF here or tap to browse
                    </p>
                    <p className="text-xs lg:text-sm text-muted-foreground">
                      Supports PDF files up to 50MB
                      {addToLibrary && " • Will be added to your library"}
                    </p>
                  </>
                )}
                <input
                  id="file-input"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileUpload}
                  className="hidden"
                  disabled={isValidating || isLoading}
                />
              </div>

              {/* Metadata Form Toggle */}
              {addToLibrary && (
                <div className="mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="show-metadata"
                      checked={showMetadata}
                      onCheckedChange={setShowMetadata}
                    />
                    <Label htmlFor="show-metadata" className="text-sm">
                      Add metadata and tags
                    </Label>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metadata Form */}
          {showMetadata && addToLibrary && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  Document Metadata
                </CardTitle>
                <CardDescription>
                  Add additional information to organize your document
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Description */}
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Brief description of the document..."
                    value={metadata.description || ''}
                    onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                    className="mt-1"
                  />
                </div>

                {/* Tags */}
                <div>
                  <Label>Tags</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      placeholder="Add a tag..."
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                      className="flex-1"
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {metadata.tags && metadata.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {metadata.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Categories */}
                <div>
                  <Label>Categories</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      placeholder="Add a category..."
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addCategory();
                        }
                      }}
                      className="flex-1"
                    />
                    <Button type="button" onClick={addCategory} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {metadata.categories && metadata.categories.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {metadata.categories.map(category => (
                        <Badge key={category} variant="outline" className="flex items-center gap-1">
                          <Folder className="h-3 w-3" />
                          {category}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeCategory(category)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                {/* Notes */}
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Additional notes about this document..."
                    value={metadata.notes || ''}
                    onChange={(e) => setMetadata(prev => ({ ...prev, notes: e.target.value }))}
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="url">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Load from URL
              </CardTitle>
              <CardDescription>
                Enter a direct link to a PDF file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 mobile-padding lg:p-6">
              <Input
                type="url"
                placeholder="https://example.com/document.pdf"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleUrlSubmit()}
                className="touch-target"
                aria-label="PDF URL"
              />
              <Button
                onClick={handleUrlSubmit}
                disabled={!url || isLoading}
                className="w-full touch-target-comfortable"
                aria-label={isLoading ? "Loading PDF" : "Load PDF from URL"}
              >
                {isLoading ? "Loading..." : "Load PDF"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
