/**
 * PDF Viewer Analytics & Monitoring
 * Comprehensive analytics for usage tracking and performance monitoring
 */

export interface AnalyticsEvent {
  type: string;
  timestamp: number;
  data: Record<string, any>;
  sessionId: string;
  userId?: string;
  documentId?: string;
}

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  searchTime: number;
  memoryUsage: number;
  errorCount: number;
  pageViews: number;
  sessionDuration: number;
  interactionCount: number;
}

export interface UserBehavior {
  pagesVisited: Set<number>;
  timePerPage: Map<number, number>;
  zoomLevels: number[];
  searchQueries: string[];
  featuresUsed: Set<string>;
  errorEncountered: boolean;
  completionRate: number;
}

export interface AnalyticsConfig {
  enableTracking: boolean;
  enablePerformanceMonitoring: boolean;
  enableUserBehaviorTracking: boolean;
  enableErrorTracking: boolean;
  sampleRate: number; // 0-1
  endpoint?: string;
  apiKey?: string;
  batchSize: number;
  flushInterval: number; // ms
  enableLocalStorage: boolean;
  enableConsoleLogging: boolean;
}

const DEFAULT_CONFIG: AnalyticsConfig = {
  enableTracking: true,
  enablePerformanceMonitoring: true,
  enableUserBehaviorTracking: true,
  enableErrorTracking: true,
  sampleRate: 1.0,
  batchSize: 50,
  flushInterval: 30000, // 30 seconds
  enableLocalStorage: true,
  enableConsoleLogging: false,
};

export class PDFAnalytics {
  private config: AnalyticsConfig;
  private sessionId: string;
  private events: AnalyticsEvent[] = [];
  private metrics: PerformanceMetrics;
  private userBehavior: UserBehavior;
  private startTime: number;
  private lastPageTime: number = 0;
  private flushTimer: number | null = null;

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    
    this.metrics = {
      loadTime: 0,
      renderTime: 0,
      searchTime: 0,
      memoryUsage: 0,
      errorCount: 0,
      pageViews: 0,
      sessionDuration: 0,
      interactionCount: 0,
    };

    this.userBehavior = {
      pagesVisited: new Set(),
      timePerPage: new Map(),
      zoomLevels: [],
      searchQueries: [],
      featuresUsed: new Set(),
      errorEncountered: false,
      completionRate: 0,
    };

    this.setupAutoFlush();
    this.setupPerformanceMonitoring();
    this.setupErrorTracking();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupAutoFlush(): void {
    if (this.config.flushInterval > 0) {
      this.flushTimer = window.setInterval(() => {
        this.flush();
      }, this.config.flushInterval);
    }

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush(true);
    });
  }

  private setupPerformanceMonitoring(): void {
    if (!this.config.enablePerformanceMonitoring) return;

    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        this.metrics.memoryUsage = memInfo.usedJSHeapSize;
      }, 5000);
    }

    // Monitor performance entries
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackPerformanceEntry(entry);
        }
      });

      observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
    }
  }

  private setupErrorTracking(): void {
    if (!this.config.enableErrorTracking) return;

    window.addEventListener('error', (event) => {
      this.trackError({
        type: 'javascript-error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.trackError({
        type: 'unhandled-promise-rejection',
        reason: event.reason,
        stack: event.reason?.stack,
      });
    });
  }

  public track(type: string, data: Record<string, any> = {}): void {
    if (!this.config.enableTracking || !this.shouldSample()) return;

    const event: AnalyticsEvent = {
      type,
      timestamp: Date.now(),
      data,
      sessionId: this.sessionId,
    };

    this.events.push(event);
    this.metrics.interactionCount++;

    if (this.config.enableConsoleLogging) {
      console.log('Analytics Event:', event);
    }

    // Auto-flush if batch size reached
    if (this.events.length >= this.config.batchSize) {
      this.flush();
    }
  }

  public trackDocumentLoad(documentId: string, loadTime: number): void {
    this.metrics.loadTime = loadTime;
    
    this.track('document-load', {
      documentId,
      loadTime,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    });
  }

  public trackPageView(pageNumber: number, totalPages: number): void {
    const now = Date.now();
    
    // Track time on previous page
    if (this.lastPageTime > 0) {
      const timeOnPage = now - this.lastPageTime;
      this.userBehavior.timePerPage.set(pageNumber - 1, timeOnPage);
    }
    
    this.lastPageTime = now;
    this.userBehavior.pagesVisited.add(pageNumber);
    this.metrics.pageViews++;
    
    // Calculate completion rate
    this.userBehavior.completionRate = this.userBehavior.pagesVisited.size / totalPages;

    this.track('page-view', {
      pageNumber,
      totalPages,
      completionRate: this.userBehavior.completionRate,
      timeOnPreviousPage: this.userBehavior.timePerPage.get(pageNumber - 1) || 0,
    });
  }

  public trackZoomChange(zoomLevel: number): void {
    this.userBehavior.zoomLevels.push(zoomLevel);
    this.userBehavior.featuresUsed.add('zoom');

    this.track('zoom-change', {
      zoomLevel,
      zoomHistory: this.userBehavior.zoomLevels.slice(-5), // Last 5 zoom levels
    });
  }

  public trackSearch(query: string, resultCount: number, searchTime: number): void {
    this.userBehavior.searchQueries.push(query);
    this.userBehavior.featuresUsed.add('search');
    this.metrics.searchTime += searchTime;

    this.track('search', {
      query: query.length > 50 ? query.substring(0, 50) + '...' : query, // Truncate long queries
      queryLength: query.length,
      resultCount,
      searchTime,
      totalSearches: this.userBehavior.searchQueries.length,
    });
  }

  public trackFeatureUsage(feature: string, data: Record<string, any> = {}): void {
    this.userBehavior.featuresUsed.add(feature);

    this.track('feature-usage', {
      feature,
      ...data,
      totalFeaturesUsed: this.userBehavior.featuresUsed.size,
    });
  }

  public trackError(error: Record<string, any>): void {
    this.metrics.errorCount++;
    this.userBehavior.errorEncountered = true;

    this.track('error', {
      ...error,
      totalErrors: this.metrics.errorCount,
      sessionDuration: Date.now() - this.startTime,
    });
  }

  public trackPerformanceEntry(entry: PerformanceEntry): void {
    if (entry.entryType === 'measure') {
      this.track('performance-measure', {
        name: entry.name,
        duration: entry.duration,
        startTime: entry.startTime,
      });
    } else if (entry.entryType === 'resource') {
      const resourceEntry = entry as PerformanceResourceTiming;
      this.track('resource-timing', {
        name: resourceEntry.name,
        duration: resourceEntry.duration,
        transferSize: resourceEntry.transferSize,
        encodedBodySize: resourceEntry.encodedBodySize,
        decodedBodySize: resourceEntry.decodedBodySize,
      });
    }
  }

  public getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      sessionDuration: Date.now() - this.startTime,
    };
  }

  public getUserBehavior(): UserBehavior {
    return {
      ...this.userBehavior,
      pagesVisited: new Set(this.userBehavior.pagesVisited),
      timePerPage: new Map(this.userBehavior.timePerPage),
      featuresUsed: new Set(this.userBehavior.featuresUsed),
    };
  }

  public getSessionSummary(): Record<string, any> {
    const metrics = this.getMetrics();
    const behavior = this.getUserBehavior();

    return {
      sessionId: this.sessionId,
      duration: metrics.sessionDuration,
      pageViews: metrics.pageViews,
      uniquePagesVisited: behavior.pagesVisited.size,
      completionRate: behavior.completionRate,
      featuresUsed: Array.from(behavior.featuresUsed),
      searchCount: behavior.searchQueries.length,
      errorCount: metrics.errorCount,
      averageTimePerPage: this.calculateAverageTimePerPage(),
      performanceScore: this.calculatePerformanceScore(),
    };
  }

  private calculateAverageTimePerPage(): number {
    const times = Array.from(this.userBehavior.timePerPage.values());
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  private calculatePerformanceScore(): number {
    // Simple performance score based on load time, errors, and usage
    let score = 100;
    
    // Deduct for slow load times
    if (this.metrics.loadTime > 5000) score -= 20;
    else if (this.metrics.loadTime > 2000) score -= 10;
    
    // Deduct for errors
    score -= this.metrics.errorCount * 5;
    
    // Deduct for high memory usage
    if (this.metrics.memoryUsage > 100 * 1024 * 1024) score -= 10; // 100MB
    
    return Math.max(0, score);
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  public async flush(immediate = false): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      if (this.config.endpoint) {
        await this.sendToEndpoint(eventsToSend, immediate);
      }

      if (this.config.enableLocalStorage) {
        this.saveToLocalStorage(eventsToSend);
      }
    } catch (error) {
      console.error('Failed to flush analytics events:', error);
      // Re-add events to queue for retry
      this.events.unshift(...eventsToSend);
    }
  }

  private async sendToEndpoint(events: AnalyticsEvent[], immediate: boolean): Promise<void> {
    const payload = {
      events,
      session: this.getSessionSummary(),
      timestamp: Date.now(),
    };

    const options: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
      },
      body: JSON.stringify(payload),
    };

    if (immediate && 'sendBeacon' in navigator) {
      // Use sendBeacon for immediate sends (page unload)
      navigator.sendBeacon(this.config.endpoint!, options.body as string);
    } else {
      await fetch(this.config.endpoint!, options);
    }
  }

  private saveToLocalStorage(events: AnalyticsEvent[]): void {
    try {
      const key = `pdf-analytics-${this.sessionId}`;
      const existing = localStorage.getItem(key);
      const allEvents = existing ? JSON.parse(existing) : [];
      
      allEvents.push(...events);
      localStorage.setItem(key, JSON.stringify(allEvents));
    } catch (error) {
      console.warn('Failed to save analytics to localStorage:', error);
    }
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    
    this.flush(true);
  }
}
