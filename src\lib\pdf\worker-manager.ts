/**
 * PDF.js WebWorker Manager
 * Enhanced worker configuration with memory management and parallel processing
 */

import * as pdfjsLib from 'pdfjs-dist';

export interface WorkerConfig {
  maxWorkers: number;
  workerSrc: string;
  enableSharedArrayBuffer: boolean;
  memoryLimit: number; // MB
  idleTimeout: number; // ms
  enableParallelProcessing: boolean;
  workerPoolSize: number;
  enableMemoryOptimization: boolean;
  enableProgressiveRendering: boolean;
}

export interface WorkerStats {
  activeWorkers: number;
  totalWorkers: number;
  memoryUsage: number;
  tasksQueued: number;
  tasksCompleted: number;
  averageTaskTime: number;
  workerUtilization: number;
}

export interface WorkerTask {
  id: string;
  type: 'render' | 'parse' | 'extract';
  priority: 'low' | 'normal' | 'high';
  data: any;
  resolve: (result: any) => void;
  reject: (error: Error) => void;
  startTime?: number;
  workerId?: string;
}

const DEFAULT_CONFIG: WorkerConfig = {
  maxWorkers: Math.max(1, Math.min(navigator.hardwareConcurrency || 4, 8)),
  workerSrc: '/pdf.worker.min.js',
  enableSharedArrayBuffer: 'SharedArrayBuffer' in window,
  memoryLimit: 512, // 512MB
  idleTimeout: 30000, // 30 seconds
  enableParallelProcessing: true,
  workerPoolSize: 4,
  enableMemoryOptimization: true,
  enableProgressiveRendering: true,
};

export class PDFWorkerManager {
  private config: WorkerConfig;
  private workers: Map<string, Worker> = new Map();
  private workerStats: Map<string, { tasks: number; memory: number; lastUsed: number }> = new Map();
  private taskQueue: WorkerTask[] = [];
  private activeTasks: Map<string, WorkerTask> = new Map();
  private taskHistory: number[] = [];
  private isInitialized = false;
  private cleanupInterval: number | null = null;

  constructor(config: Partial<WorkerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializePDFJS();
  }

  private initializePDFJS(): void {
    // Configure PDF.js global worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = this.config.workerSrc;
    
    // Enable SharedArrayBuffer if available
    if (this.config.enableSharedArrayBuffer && 'SharedArrayBuffer' in window) {
      // Configure SharedArrayBuffer support
      console.log('SharedArrayBuffer support enabled for PDF.js');
    }

    this.isInitialized = true;
    this.startCleanupTimer();
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Pre-create worker pool
    if (this.config.enableParallelProcessing) {
      await this.createWorkerPool();
    }

    this.isInitialized = true;
  }

  private async createWorkerPool(): Promise<void> {
    const poolSize = Math.min(this.config.workerPoolSize, this.config.maxWorkers);
    
    for (let i = 0; i < poolSize; i++) {
      const workerId = `worker-${i}`;
      await this.createWorker(workerId);
    }
  }

  private async createWorker(workerId: string): Promise<Worker> {
    if (this.workers.has(workerId)) {
      return this.workers.get(workerId)!;
    }

    const worker = new Worker(this.config.workerSrc);
    
    // Configure worker
    worker.onmessage = (event) => this.handleWorkerMessage(workerId, event);
    worker.onerror = (error) => this.handleWorkerError(workerId, error);
    
    // Initialize worker stats
    this.workerStats.set(workerId, {
      tasks: 0,
      memory: 0,
      lastUsed: Date.now(),
    });

    this.workers.set(workerId, worker);
    
    // Send initialization message
    worker.postMessage({
      type: 'init',
      config: {
        enableMemoryOptimization: this.config.enableMemoryOptimization,
        memoryLimit: this.config.memoryLimit,
      },
    });

    return worker;
  }

  private handleWorkerMessage(workerId: string, event: MessageEvent): void {
    const { taskId, type, data, error } = event.data;

    if (type === 'task-complete') {
      const task = this.activeTasks.get(taskId);
      if (task) {
        const duration = Date.now() - (task.startTime || 0);
        this.taskHistory.push(duration);
        
        // Keep only last 100 task times
        if (this.taskHistory.length > 100) {
          this.taskHistory.shift();
        }

        // Update worker stats
        const stats = this.workerStats.get(workerId);
        if (stats) {
          stats.tasks++;
          stats.lastUsed = Date.now();
        }

        this.activeTasks.delete(taskId);
        
        if (error) {
          task.reject(new Error(error));
        } else {
          task.resolve(data);
        }

        // Process next task in queue
        this.processNextTask();
      }
    } else if (type === 'memory-usage') {
      const stats = this.workerStats.get(workerId);
      if (stats) {
        stats.memory = data.memoryUsage;
      }
    }
  }

  private handleWorkerError(workerId: string, error: ErrorEvent): void {
    console.error(`Worker ${workerId} error:`, error);
    
    // Restart worker if it crashes
    this.restartWorker(workerId);
  }

  private async restartWorker(workerId: string): Promise<void> {
    const worker = this.workers.get(workerId);
    if (worker) {
      worker.terminate();
      this.workers.delete(workerId);
      this.workerStats.delete(workerId);
    }

    // Create new worker
    await this.createWorker(workerId);
  }

  public async executeTask<T = any>(
    type: WorkerTask['type'],
    data: any,
    priority: WorkerTask['priority'] = 'normal'
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const task: WorkerTask = {
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type,
        priority,
        data,
        resolve,
        reject,
      };

      // Add to queue based on priority
      if (priority === 'high') {
        this.taskQueue.unshift(task);
      } else {
        this.taskQueue.push(task);
      }

      this.processNextTask();
    });
  }

  private processNextTask(): void {
    if (this.taskQueue.length === 0) return;

    const availableWorker = this.findAvailableWorker();
    if (!availableWorker) return;

    const task = this.taskQueue.shift()!;
    task.startTime = Date.now();
    task.workerId = availableWorker;

    this.activeTasks.set(task.id, task);

    const worker = this.workers.get(availableWorker)!;
    worker.postMessage({
      type: 'execute-task',
      taskId: task.id,
      taskType: task.type,
      data: task.data,
    });
  }

  private findAvailableWorker(): string | null {
    // Find worker with least active tasks
    let bestWorker: string | null = null;
    let minTasks = Infinity;

    for (const [workerId, stats] of this.workerStats) {
      const activeTasks = Array.from(this.activeTasks.values())
        .filter(task => task.workerId === workerId).length;

      if (activeTasks < minTasks) {
        minTasks = activeTasks;
        bestWorker = workerId;
      }
    }

    // Create new worker if needed and under limit
    if (bestWorker === null && this.workers.size < this.config.maxWorkers) {
      const newWorkerId = `worker-${this.workers.size}`;
      this.createWorker(newWorkerId);
      return newWorkerId;
    }

    return bestWorker;
  }

  public getStats(): WorkerStats {
    const totalTasks = Array.from(this.workerStats.values())
      .reduce((sum, stats) => sum + stats.tasks, 0);
    
    const totalMemory = Array.from(this.workerStats.values())
      .reduce((sum, stats) => sum + stats.memory, 0);

    const averageTaskTime = this.taskHistory.length > 0
      ? this.taskHistory.reduce((sum, time) => sum + time, 0) / this.taskHistory.length
      : 0;

    const activeWorkers = Array.from(this.activeTasks.values())
      .reduce((workers, task) => {
        if (task.workerId) workers.add(task.workerId);
        return workers;
      }, new Set()).size;

    return {
      activeWorkers,
      totalWorkers: this.workers.size,
      memoryUsage: totalMemory,
      tasksQueued: this.taskQueue.length,
      tasksCompleted: totalTasks,
      averageTaskTime,
      workerUtilization: this.workers.size > 0 ? activeWorkers / this.workers.size : 0,
    };
  }

  private startCleanupTimer(): void {
    this.cleanupInterval = window.setInterval(() => {
      this.cleanupIdleWorkers();
      this.optimizeMemory();
    }, this.config.idleTimeout);
  }

  private cleanupIdleWorkers(): void {
    const now = Date.now();
    const idleThreshold = this.config.idleTimeout;

    for (const [workerId, stats] of this.workerStats) {
      if (now - stats.lastUsed > idleThreshold) {
        // Don't terminate if it's the last worker or has active tasks
        const hasActiveTasks = Array.from(this.activeTasks.values())
          .some(task => task.workerId === workerId);
        
        if (!hasActiveTasks && this.workers.size > 1) {
          this.terminateWorker(workerId);
        }
      }
    }
  }

  private optimizeMemory(): void {
    if (!this.config.enableMemoryOptimization) return;

    const memoryLimit = this.config.memoryLimit * 1024 * 1024; // Convert to bytes
    
    for (const [workerId, stats] of this.workerStats) {
      if (stats.memory > memoryLimit) {
        const worker = this.workers.get(workerId);
        if (worker) {
          worker.postMessage({ type: 'optimize-memory' });
        }
      }
    }
  }

  private terminateWorker(workerId: string): void {
    const worker = this.workers.get(workerId);
    if (worker) {
      worker.terminate();
      this.workers.delete(workerId);
      this.workerStats.delete(workerId);
    }
  }

  public async renderPage(
    page: any,
    viewport: any,
    canvas: HTMLCanvasElement,
    options: any = {}
  ): Promise<void> {
    const renderData = {
      pageData: page,
      viewport,
      canvasData: {
        width: canvas.width,
        height: canvas.height,
      },
      options: {
        enableProgressiveRendering: this.config.enableProgressiveRendering,
        ...options,
      },
    };

    const result = await this.executeTask('render', renderData, 'high');
    
    // Apply rendered data to canvas
    const ctx = canvas.getContext('2d');
    if (ctx && result.imageData) {
      ctx.putImageData(result.imageData, 0, 0);
    }
  }

  public async extractText(page: any): Promise<string> {
    const result = await this.executeTask('extract', { pageData: page }, 'normal');
    return result.text || '';
  }

  public async parsePDF(data: ArrayBuffer): Promise<any> {
    return this.executeTask('parse', { pdfData: data }, 'high');
  }

  public updateConfig(newConfig: Partial<WorkerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Apply new configuration to existing workers
    for (const worker of this.workers.values()) {
      worker.postMessage({
        type: 'update-config',
        config: this.config,
      });
    }
  }

  public destroy(): void {
    // Clear cleanup timer
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Terminate all workers
    for (const worker of this.workers.values()) {
      worker.terminate();
    }

    // Clear all data structures
    this.workers.clear();
    this.workerStats.clear();
    this.taskQueue.length = 0;
    this.activeTasks.clear();
    this.taskHistory.length = 0;

    this.isInitialized = false;
  }

  public getWorkerCount(): number {
    return this.workers.size;
  }

  public getQueueLength(): number {
    return this.taskQueue.length;
  }

  public getActiveTaskCount(): number {
    return this.activeTasks.size;
  }
}

// Singleton instance
let workerManagerInstance: PDFWorkerManager | null = null;

export function getWorkerManager(config?: Partial<WorkerConfig>): PDFWorkerManager {
  if (!workerManagerInstance) {
    workerManagerInstance = new PDFWorkerManager(config);
  }
  return workerManagerInstance;
}

export function destroyWorkerManager(): void {
  if (workerManagerInstance) {
    workerManagerInstance.destroy();
    workerManagerInstance = null;
  }
}
