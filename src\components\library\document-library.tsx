"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Filter,
  Star,
  Pin,
  Clock,
  FileText,
  Eye,
  Download,
  Trash2,
  MoreHorizontal,
  Plus,
  Folder,
  Tag,
  Calendar,
  User,
  FileSize,
  BookOpen
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type {
  DocumentInstance,
  DocumentSearchQuery,
  DocumentLibrarySettings,
  DocumentCollection
} from '@/lib/types/pdf';
import { formatFileSize, formatDuration } from '@/lib/types/pdf';
import { documentLibrary } from '@/lib/document-library';

interface DocumentLibraryProps {
  onDocumentSelect: (document: DocumentInstance) => void;
  onDocumentOpen: (document: DocumentInstance) => void;
  className?: string;
}

type ViewMode = 'grid' | 'list' | 'compact';
type SortField = 'name' | 'dateAdded' | 'dateModified' | 'size' | 'lastAccessed';
type SortOrder = 'asc' | 'desc';

export default function DocumentLibrary({
  onDocumentSelect,
  onDocumentOpen,
  className
}: DocumentLibraryProps) {
  // State management
  const [documents, setDocuments] = useState<DocumentInstance[]>([]);
  const [collections, setCollections] = useState<DocumentCollection[]>([]);
  const [settings, setSettings] = useState<DocumentLibrarySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortField, setSortField] = useState<SortField>('dateAdded');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  // Load initial data
  useEffect(() => {
    loadLibraryData();
  }, []);

  const loadLibraryData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialize the document library
      await documentLibrary.initialize();
      await documentLibrary.initializeDefaultCollections();

      // Load data
      const [docsData, collectionsData, settingsData] = await Promise.all([
        documentLibrary.getAllDocuments(),
        documentLibrary.getAllCollections(),
        documentLibrary.getSettings()
      ]);

      setDocuments(docsData);
      setCollections(collectionsData);
      setSettings(settingsData);
      setViewMode(settingsData.defaultView);
      setSortField(settingsData.sortBy);
      setSortOrder(settingsData.sortOrder);
    } catch (err) {
      console.error('Failed to load library data:', err);
      setError('Failed to load document library');
      toast.error('Failed to load document library');
    } finally {
      setIsLoading(false);
    }
  };

  // Search and filter documents
  const filteredDocuments = useMemo(() => {
    let filtered = documents;

    // Collection filter
    if (selectedCollection !== 'all') {
      const collection = collections.find(c => c.id === selectedCollection);
      if (collection) {
        filtered = filtered.filter(doc => collection.documentIds.includes(doc.id));
      } else if (selectedCollection === 'recent') {
        filtered = filtered
          .sort((a, b) => b.metadata.lastAccessedDate.getTime() - a.metadata.lastAccessedDate.getTime())
          .slice(0, 20);
      } else if (selectedCollection === 'favorites') {
        filtered = filtered.filter(doc => doc.metadata.isFavorite);
      } else if (selectedCollection === 'pinned') {
        filtered = filtered.filter(doc => doc.metadata.isPinned);
      }
    }

    // Text search
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.metadata.title.toLowerCase().includes(query) ||
        doc.metadata.author?.toLowerCase().includes(query) ||
        doc.metadata.subject?.toLowerCase().includes(query) ||
        doc.metadata.description?.toLowerCase().includes(query) ||
        doc.metadata.keywords?.some(keyword => keyword.toLowerCase().includes(query))
      );
    }

    // Tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(doc =>
        selectedTags.some(tag => doc.metadata.tags.includes(tag))
      );
    }

    // Category filter
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(doc =>
        selectedCategories.some(category => doc.metadata.categories.includes(category))
      );
    }

    // Sort documents
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'name':
          aValue = a.metadata.title.toLowerCase();
          bValue = b.metadata.title.toLowerCase();
          break;
        case 'dateAdded':
          aValue = a.metadata.addedDate.getTime();
          bValue = b.metadata.addedDate.getTime();
          break;
        case 'dateModified':
          aValue = a.metadata.modificationDate?.getTime() || 0;
          bValue = b.metadata.modificationDate?.getTime() || 0;
          break;
        case 'size':
          aValue = a.metadata.fileSize;
          bValue = b.metadata.fileSize;
          break;
        case 'lastAccessed':
          aValue = a.metadata.lastAccessedDate.getTime();
          bValue = b.metadata.lastAccessedDate.getTime();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [documents, collections, selectedCollection, searchQuery, selectedTags, selectedCategories, sortField, sortOrder]);

  // Get unique tags and categories for filters
  const availableTags = useMemo(() => {
    const tags = new Set<string>();
    documents.forEach(doc => doc.metadata.tags.forEach(tag => tags.add(tag)));
    return Array.from(tags).sort();
  }, [documents]);

  const availableCategories = useMemo(() => {
    const categories = new Set<string>();
    documents.forEach(doc => doc.metadata.categories.forEach(category => categories.add(category)));
    return Array.from(categories).sort();
  }, [documents]);

  // Handle document actions
  const handleDocumentClick = (document: DocumentInstance) => {
    onDocumentSelect(document);
  };

  const handleDocumentOpen = (document: DocumentInstance) => {
    onDocumentOpen(document);
    // Update last accessed date
    documentLibrary.updateDocument(document.id, {
      metadata: {
        ...document.metadata,
        lastAccessedDate: new Date(),
        openCount: document.metadata.openCount + 1
      }
    });
  };

  const handleToggleFavorite = async (document: DocumentInstance) => {
    try {
      await documentLibrary.updateDocument(document.id, {
        metadata: {
          ...document.metadata,
          isFavorite: !document.metadata.isFavorite
        }
      });
      await loadLibraryData();
      toast.success(
        document.metadata.isFavorite ? 'Removed from favorites' : 'Added to favorites'
      );
    } catch (err) {
      toast.error('Failed to update favorite status');
    }
  };

  const handleTogglePin = async (document: DocumentInstance) => {
    try {
      await documentLibrary.updateDocument(document.id, {
        metadata: {
          ...document.metadata,
          isPinned: !document.metadata.isPinned
        }
      });
      await loadLibraryData();
      toast.success(
        document.metadata.isPinned ? 'Unpinned document' : 'Pinned document'
      );
    } catch (err) {
      toast.error('Failed to update pin status');
    }
  };

  const handleDeleteDocument = async (document: DocumentInstance) => {
    try {
      await documentLibrary.removeDocument(document.id);
      await loadLibraryData();
      toast.success('Document removed from library');
    } catch (err) {
      toast.error('Failed to remove document');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading document library...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">{error}</p>
          <Button onClick={loadLibraryData} className="mt-2">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h2 className="text-lg font-semibold">Document Library</h2>
          <p className="text-sm text-muted-foreground">
            {filteredDocuments.length} of {documents.length} documents
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="h-4 w-4 mr-1" />
            Filters
          </Button>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="p-4 space-y-4 border-b">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Collection selector */}
            <Select value={selectedCollection} onValueChange={setSelectedCollection}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Documents</SelectItem>
                <SelectItem value="recent">Recent</SelectItem>
                <SelectItem value="favorites">Favorites</SelectItem>
                <SelectItem value="pinned">Pinned</SelectItem>
                <Separator />
                {collections.filter(c => !c.isSystem).map(collection => (
                  <SelectItem key={collection.id} value={collection.id}>
                    {collection.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            {/* Sort controls */}
            <Select value={sortField} onValueChange={(value) => setSortField(value as SortField)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="dateAdded">Date Added</SelectItem>
                <SelectItem value="dateModified">Modified</SelectItem>
                <SelectItem value="size">Size</SelectItem>
                <SelectItem value="lastAccessed">Last Accessed</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>

            {/* View mode */}
            <div className="flex border rounded-md">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-none border-x"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'compact' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('compact')}
                className="rounded-l-none"
              >
                <FileText className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="p-4 border-b bg-muted/20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tags filter */}
            {availableTags.length > 0 && (
              <div>
                <label className="text-sm font-medium mb-2 block">Tags</label>
                <div className="flex flex-wrap gap-1">
                  {availableTags.map(tag => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        setSelectedTags(prev =>
                          prev.includes(tag)
                            ? prev.filter(t => t !== tag)
                            : [...prev, tag]
                        );
                      }}
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Categories filter */}
            {availableCategories.length > 0 && (
              <div>
                <label className="text-sm font-medium mb-2 block">Categories</label>
                <div className="flex flex-wrap gap-1">
                  {availableCategories.map(category => (
                    <Badge
                      key={category}
                      variant={selectedCategories.includes(category) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        setSelectedCategories(prev =>
                          prev.includes(category)
                            ? prev.filter(c => c !== category)
                            : [...prev, category]
                        );
                      }}
                    >
                      <Folder className="h-3 w-3 mr-1" />
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Document Grid/List */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {filteredDocuments.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-lg font-medium mb-1">No documents found</p>
                <p className="text-sm text-muted-foreground">
                  {searchQuery || selectedTags.length > 0 || selectedCategories.length > 0
                    ? 'Try adjusting your search or filters'
                    : 'Add some documents to get started'}
                </p>
              </div>
            </div>
          ) : (
            <div className={cn(
              viewMode === 'grid' && "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",
              viewMode === 'list' && "space-y-2",
              viewMode === 'compact' && "space-y-1"
            )}>
              {filteredDocuments.map(document => (
                <DocumentCard
                  key={document.id}
                  document={document}
                  viewMode={viewMode}
                  onSelect={() => handleDocumentClick(document)}
                  onOpen={() => handleDocumentOpen(document)}
                  onToggleFavorite={() => handleToggleFavorite(document)}
                  onTogglePin={() => handleTogglePin(document)}
                  onDelete={() => handleDeleteDocument(document)}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}

// Document Card Component
interface DocumentCardProps {
  document: DocumentInstance;
  viewMode: ViewMode;
  onSelect: () => void;
  onOpen: () => void;
  onToggleFavorite: () => void;
  onTogglePin: () => void;
  onDelete: () => void;
}

function DocumentCard({
  document,
  viewMode,
  onSelect,
  onOpen,
  onToggleFavorite,
  onTogglePin,
  onDelete
}: DocumentCardProps) {
  const { metadata } = document;

  if (viewMode === 'grid') {
    return (
      <Card className="group hover:shadow-md transition-shadow cursor-pointer" onClick={onSelect}>
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-sm font-medium truncate" title={metadata.title}>
                {metadata.title}
              </CardTitle>
              {metadata.author && (
                <p className="text-xs text-muted-foreground truncate mt-1">
                  by {metadata.author}
                </p>
              )}
            </div>
            <div className="flex items-center gap-1 ml-2">
              {metadata.isFavorite && <Star className="h-3 w-3 text-yellow-500 fill-current" />}
              {metadata.isPinned && <Pin className="h-3 w-3 text-blue-500" />}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Thumbnail placeholder */}
          <div className="aspect-[3/4] bg-muted rounded-md mb-3 flex items-center justify-center">
            <FileText className="h-8 w-8 text-muted-foreground" />
          </div>

          {/* Metadata */}
          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex items-center justify-between">
              <span>{formatFileSize(metadata.fileSize)}</span>
              <span>{metadata.pageCount} pages</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{metadata.addedDate.toLocaleDateString()}</span>
            </div>
            {metadata.openCount > 0 && (
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>Opened {metadata.openCount} times</span>
              </div>
            )}
          </div>

          {/* Tags */}
          {metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {metadata.tags.slice(0, 2).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {metadata.tags.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{metadata.tags.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center gap-1">
              <Button size="sm" variant="ghost" onClick={onToggleFavorite}>
                <Star className={cn(
                  "h-3 w-3",
                  metadata.isFavorite ? "text-yellow-500 fill-current" : "text-muted-foreground"
                )} />
              </Button>
              <Button size="sm" variant="ghost" onClick={onTogglePin}>
                <Pin className={cn(
                  "h-3 w-3",
                  metadata.isPinned ? "text-blue-500" : "text-muted-foreground"
                )} />
              </Button>
            </div>
            <div className="flex items-center gap-1">
              <Button size="sm" variant="ghost" onClick={onOpen}>
                <Eye className="h-3 w-3" />
              </Button>
              <Button size="sm" variant="ghost" onClick={onDelete}>
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (viewMode === 'list') {
    return (
      <Card className="group hover:shadow-sm transition-shadow cursor-pointer" onClick={onSelect}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* Thumbnail */}
            <div className="w-12 h-16 bg-muted rounded flex items-center justify-center flex-shrink-0">
              <FileText className="h-6 w-6 text-muted-foreground" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium truncate" title={metadata.title}>
                    {metadata.title}
                  </h3>
                  {metadata.author && (
                    <p className="text-sm text-muted-foreground truncate">
                      by {metadata.author}
                    </p>
                  )}
                  <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                    <span>{formatFileSize(metadata.fileSize)}</span>
                    <span>{metadata.pageCount} pages</span>
                    <span>{metadata.addedDate.toLocaleDateString()}</span>
                    {metadata.openCount > 0 && (
                      <span>Opened {metadata.openCount} times</span>
                    )}
                  </div>
                </div>

                {/* Status indicators */}
                <div className="flex items-center gap-2 ml-4">
                  {metadata.isFavorite && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                  {metadata.isPinned && <Pin className="h-4 w-4 text-blue-500" />}
                </div>
              </div>

              {/* Tags */}
              {metadata.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {metadata.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {metadata.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{metadata.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button size="sm" variant="ghost" onClick={onToggleFavorite}>
                <Star className={cn(
                  "h-4 w-4",
                  metadata.isFavorite ? "text-yellow-500 fill-current" : "text-muted-foreground"
                )} />
              </Button>
              <Button size="sm" variant="ghost" onClick={onTogglePin}>
                <Pin className={cn(
                  "h-4 w-4",
                  metadata.isPinned ? "text-blue-500" : "text-muted-foreground"
                )} />
              </Button>
              <Button size="sm" variant="ghost" onClick={onOpen}>
                <Eye className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="ghost" onClick={onDelete}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Compact view
  return (
    <div className="group flex items-center gap-3 p-2 hover:bg-muted/50 rounded-md transition-colors cursor-pointer" onClick={onSelect}>
      <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <span className="font-medium truncate" title={metadata.title}>
            {metadata.title}
          </span>
          <div className="flex items-center gap-1 ml-2">
            {metadata.isFavorite && <Star className="h-3 w-3 text-yellow-500 fill-current" />}
            {metadata.isPinned && <Pin className="h-3 w-3 text-blue-500" />}
          </div>
        </div>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>{formatFileSize(metadata.fileSize)}</span>
          <span>•</span>
          <span>{metadata.pageCount} pages</span>
          <span>•</span>
          <span>{metadata.addedDate.toLocaleDateString()}</span>
        </div>
      </div>
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button size="sm" variant="ghost" onClick={onOpen}>
          <Eye className="h-3 w-3" />
        </Button>
        <Button size="sm" variant="ghost" onClick={onDelete}>
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
