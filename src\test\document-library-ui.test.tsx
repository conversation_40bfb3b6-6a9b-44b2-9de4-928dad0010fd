import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DocumentLibrary from '../components/library/document-library';
import { createDefaultDocumentMetadata } from '../lib/types/pdf';
import type { DocumentInstance } from '../lib/types/pdf';

// Mock the document library
vi.mock('../lib/document-library', () => ({
  documentLibrary: {
    initialize: vi.fn().mockResolvedValue(undefined),
    initializeDefaultCollections: vi.fn().mockResolvedValue(undefined),
    getAllDocuments: vi.fn().mockResolvedValue([]),
    getAllCollections: vi.fn().mockResolvedValue([]),
    getSettings: vi.fn().mockResolvedValue({
      defaultView: 'grid',
      sortBy: 'dateAdded',
      sortOrder: 'desc',
      showThumbnails: true,
      thumbnailSize: 'medium',
      autoGenerateThumbnails: true,
      maxStorageSize: 500 * 1024 * 1024,
      enableAutoBackup: true,
      backupInterval: 24
    }),
    updateDocument: vi.fn().mockResolvedValue(undefined),
    removeDocument: vi.fn().mockResolvedValue(undefined),
  }
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  }
}));

describe('DocumentLibrary Component', () => {
  const mockOnDocumentSelect = vi.fn();
  const mockOnDocumentOpen = vi.fn();

  const sampleDocuments: DocumentInstance[] = [
    {
      id: 'doc1',
      file: new File(['content'], 'document1.pdf', { type: 'application/pdf' }),
      title: 'Sample Document 1',
      isLoading: false,
      hasError: false,
      numPages: 10,
      lastAccessed: Date.now(),
      metadata: {
        ...createDefaultDocumentMetadata('document1.pdf'),
        title: 'Sample Document 1',
        author: 'John Doe',
        tags: ['important', 'work'],
        categories: ['reports'],
        isFavorite: true,
        isPinned: false,
        pageCount: 10,
        fileSize: 1024 * 1024, // 1MB
      },
      pageNumber: 1,
      scale: 1.0,
      rotation: 0,
      searchText: '',
      bookmarks: [],
      annotations: [],
      formData: {}
    },
    {
      id: 'doc2',
      file: new File(['content'], 'document2.pdf', { type: 'application/pdf' }),
      title: 'Sample Document 2',
      isLoading: false,
      hasError: false,
      numPages: 5,
      lastAccessed: Date.now() - 86400000, // 1 day ago
      metadata: {
        ...createDefaultDocumentMetadata('document2.pdf'),
        title: 'Sample Document 2',
        author: 'Jane Smith',
        tags: ['reference'],
        categories: ['manuals'],
        isFavorite: false,
        isPinned: true,
        pageCount: 5,
        fileSize: 512 * 1024, // 512KB
      },
      pageNumber: 1,
      scale: 1.0,
      rotation: 0,
      searchText: '',
      bookmarks: [],
      annotations: [],
      formData: {}
    }
  ];

  beforeEach(async () => {
    vi.clearAllMocks();

    // Setup default mocks - the documentLibrary is already mocked in setup.ts
    const { documentLibrary } = await import('@/lib/document-library');
    vi.mocked(documentLibrary.getAllDocuments).mockResolvedValue(sampleDocuments);
    vi.mocked(documentLibrary.getAllCollections).mockResolvedValue([
      {
        id: 'recent',
        name: 'Recent',
        description: 'Recently accessed documents',
        documentIds: ['doc1', 'doc2'],
        isSystem: true,
        createdDate: new Date(),
        modifiedDate: new Date()
      },
      {
        id: 'favorites',
        name: 'Favorites',
        description: 'Favorite documents',
        documentIds: ['doc1'],
        isSystem: true,
        createdDate: new Date(),
        modifiedDate: new Date()
      }
    ]);
  });

  it('should render the document library', async () => {
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Document Library')).toBeInTheDocument();
    });

    expect(screen.getByText('2 of 2 documents')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument();
  });

  it('should display documents in grid view', async () => {
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
      expect(screen.getByText('Sample Document 2')).toBeInTheDocument();
    });

    expect(screen.getByText('by John Doe')).toBeInTheDocument();
    expect(screen.getByText('by Jane Smith')).toBeInTheDocument();
  });

  it('should filter documents by search query', async () => {
    const user = userEvent.setup();
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search documents...');
    await user.type(searchInput, 'Document 1');

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
      expect(screen.queryByText('Sample Document 2')).not.toBeInTheDocument();
    });
  });

  it('should sort documents by different criteria', async () => {
    const user = userEvent.setup();

    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    });

    // Verify that sort controls are present (Select component should be rendered)
    // Note: Select options are not visible until the dropdown is opened
    const sortButtons = screen.getAllByRole('button');
    expect(sortButtons.length).toBeGreaterThan(0);

    // Verify that documents are displayed (sorting functionality is tested at the component level)
    expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    expect(screen.getByText('Sample Document 2')).toBeInTheDocument();
  });

  it('should switch between view modes', async () => {
    const user = userEvent.setup();
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    });

    // Find the list view button (should be the second button in the view mode group)
    const viewModeButtons = screen.getAllByRole('button');
    const listViewButton = viewModeButtons.find(button => 
      button.querySelector('svg') && button.getAttribute('class')?.includes('rounded-none')
    );

    if (listViewButton) {
      await user.click(listViewButton);
      // In list view, documents should still be visible but with different layout
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    }
  });

  it('should handle document selection', async () => {
    const user = userEvent.setup();
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    });

    const documentCard = screen.getByText('Sample Document 1').closest('[role="button"], .cursor-pointer, .group');
    if (documentCard) {
      await user.click(documentCard);
      expect(mockOnDocumentSelect).toHaveBeenCalledTimes(1);

      // Check that it was called with a document that has the expected properties
      const calledWith = mockOnDocumentSelect.mock.calls[0][0];
      expect(calledWith).toMatchObject({
        id: expect.any(String),
        title: 'Sample Document 1',
        file: expect.any(File),
        metadata: expect.objectContaining({
          title: 'Sample Document 1'
        })
      });
    }
  });

  it('should toggle favorite status', async () => {
    const user = userEvent.setup();
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Sample Document 1')).toBeInTheDocument();
    });

    // Find the favorite button (star icon)
    const favoriteButtons = screen.getAllByRole('button');
    const favoriteButton = favoriteButtons.find(button => 
      button.querySelector('svg') && button.getAttribute('class')?.includes('text-yellow-500')
    );

    if (favoriteButton) {
      await user.click(favoriteButton);
      expect(documentLibrary.updateDocument).toHaveBeenCalled();
    }
  });

  it('should show empty state when no documents', async () => {
    const { documentLibrary } = await import('../lib/document-library');
    documentLibrary.getAllDocuments.mockResolvedValue([]);
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('No documents found')).toBeInTheDocument();
    });

    expect(screen.getByText('Add some documents to get started')).toBeInTheDocument();
  });

  it('should show loading state', async () => {
    const { documentLibrary } = await import('../lib/document-library');
    documentLibrary.getAllDocuments.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    expect(screen.getByText('Loading document library...')).toBeInTheDocument();
  });

  it('should handle errors gracefully', async () => {
    const { documentLibrary } = await import('../lib/document-library');
    documentLibrary.getAllDocuments.mockRejectedValue(new Error('Database error'));
    
    render(
      <DocumentLibrary
        onDocumentSelect={mockOnDocumentSelect}
        onDocumentOpen={mockOnDocumentOpen}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load document library')).toBeInTheDocument();
    });

    expect(screen.getByText('Retry')).toBeInTheDocument();
  });
});
