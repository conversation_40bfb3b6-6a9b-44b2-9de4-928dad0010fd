"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  Eye, 
  BarChart3, 
  Settings, 
  FileText,
  Layers,
  Clock,
  HardDrive
} from 'lucide-react';
import VirtualizedPDFViewer from '../core/virtualized-pdf-viewer';
import PDFViewer from '../core/pdf-viewer';
import type { VirtualRenderConfig } from '@/lib/virtual-renderer';

interface VirtualPDFDemoProps {
  className?: string;
}

export default function VirtualPDFDemo({ className }: VirtualPDFDemoProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [enableVirtualization, setEnableVirtualization] = useState(true);
  const [virtualConfig, setVirtualConfig] = useState<Partial<VirtualRenderConfig>>({
    overscanCount: 2,
    maxConcurrentRenders: 3,
    maxCachedPages: 10,
    renderDelay: 100,
    adaptiveQuality: true,
  });
  const [performanceStats, setPerformanceStats] = useState({
    renderTime: 0,
    memoryUsage: 0,
    cachedPages: 0,
    visiblePages: 0,
  });

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
    }
  }, []);

  const handleConfigChange = useCallback((key: keyof VirtualRenderConfig, value: any) => {
    setVirtualConfig(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleDocumentLoadSuccess = useCallback((pdf: { numPages: number }) => {
    console.log(`PDF loaded with ${pdf.numPages} pages`);
  }, []);

  const renderConfigPanel = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Virtual Rendering Configuration
        </CardTitle>
        <CardDescription>
          Adjust virtual rendering settings to optimize performance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <Label htmlFor="virtualization-toggle">Enable Virtualization</Label>
          <Switch
            id="virtualization-toggle"
            checked={enableVirtualization}
            onCheckedChange={setEnableVirtualization}
          />
        </div>

        {enableVirtualization && (
          <>
            <div className="space-y-2">
              <Label>Overscan Count: {virtualConfig.overscanCount}</Label>
              <Slider
                value={[virtualConfig.overscanCount || 2]}
                onValueChange={([value]) => handleConfigChange('overscanCount', value)}
                min={0}
                max={5}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Number of pages to render outside the visible area
              </p>
            </div>

            <div className="space-y-2">
              <Label>Max Concurrent Renders: {virtualConfig.maxConcurrentRenders}</Label>
              <Slider
                value={[virtualConfig.maxConcurrentRenders || 3]}
                onValueChange={([value]) => handleConfigChange('maxConcurrentRenders', value)}
                min={1}
                max={8}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Maximum pages rendering simultaneously
              </p>
            </div>

            <div className="space-y-2">
              <Label>Max Cached Pages: {virtualConfig.maxCachedPages}</Label>
              <Slider
                value={[virtualConfig.maxCachedPages || 10]}
                onValueChange={([value]) => handleConfigChange('maxCachedPages', value)}
                min={5}
                max={50}
                step={5}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Maximum pages to keep in memory cache
              </p>
            </div>

            <div className="space-y-2">
              <Label>Render Delay: {virtualConfig.renderDelay}ms</Label>
              <Slider
                value={[virtualConfig.renderDelay || 100]}
                onValueChange={([value]) => handleConfigChange('renderDelay', value)}
                min={0}
                max={500}
                step={50}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Delay before rendering non-visible pages
              </p>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="adaptive-quality">Adaptive Quality</Label>
              <Switch
                id="adaptive-quality"
                checked={virtualConfig.adaptiveQuality || false}
                onCheckedChange={(checked) => handleConfigChange('adaptiveQuality', checked)}
              />
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );

  const renderPerformanceStats = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Performance Metrics
        </CardTitle>
        <CardDescription>
          Real-time performance statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Render Time</span>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              {performanceStats.renderTime}ms
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium">Memory Usage</span>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              {performanceStats.memoryUsage}MB
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Cached Pages</span>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              {performanceStats.cachedPages}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">Visible Pages</span>
            </div>
            <Badge variant="outline" className="w-full justify-center">
              {performanceStats.visiblePages}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderComparison = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-green-500" />
            Virtual Rendering
          </CardTitle>
          <CardDescription>
            Optimized for large documents with lazy loading
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Memory Efficient</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Fast Initial Load</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Smooth Scrolling</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Large Document Support</span>
              <Badge variant="default">✓</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-500" />
            Traditional Rendering
          </CardTitle>
          <CardDescription>
            Standard approach with all pages loaded
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Memory Efficient</span>
              <Badge variant="secondary">Limited</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Fast Initial Load</span>
              <Badge variant="secondary">Slow</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Smooth Scrolling</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Large Document Support</span>
              <Badge variant="destructive">✗</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6" />
            Virtual PDF Rendering Demo
          </CardTitle>
          <CardDescription>
            Experience the performance benefits of virtual page rendering for large PDF documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="file-input">Select PDF File</Label>
              <input
                id="file-input"
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                className="mt-2 block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
              />
            </div>

            {!selectedFile && (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Select a PDF file to see virtual rendering in action</p>
                <p className="text-xs mt-1">Large documents (100+ pages) show the most benefit</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {selectedFile && (
        <Tabs defaultValue="viewer" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="viewer">PDF Viewer</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
          </TabsList>

          <TabsContent value="viewer" className="space-y-6">
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
              <div className="xl:col-span-3">
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {enableVirtualization ? 'Virtual' : 'Traditional'} PDF Viewer
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="h-[600px] border rounded-lg overflow-hidden">
                      {enableVirtualization ? (
                        <VirtualizedPDFViewer
                          file={selectedFile}
                          enableVirtualization={true}
                          virtualConfig={virtualConfig}
                          onDocumentLoadSuccess={handleDocumentLoadSuccess}
                          className="h-full"
                        />
                      ) : (
                        <PDFViewer
                          file={selectedFile}
                          onClose={() => setSelectedFile(null)}
                        />
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                {renderPerformanceStats()}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="config" className="space-y-6">
            {renderConfigPanel()}
          </TabsContent>

          <TabsContent value="comparison" className="space-y-6">
            {renderComparison()}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
