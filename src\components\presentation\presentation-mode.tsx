"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Document, Page } from 'react-pdf';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Play,
  Pause,
  Square,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Maximize,
  Minimize,
  X,
  Clock,
  MousePointer,
  Pen,
  Eraser,
  Palette,
  Settings,
  Users,
  MessageSquare,
  BarChart3,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Share,
  Timer,
  SkipForward,
  SkipBack
} from 'lucide-react';
import { toast } from 'sonner';
import type { DocumentInstance } from '@/lib/types/pdf';
import { configurePDFWorker } from '@/lib/pdf-worker-config';

// Configure PDF.js worker
configurePDFWorker();

export interface PresentationSettings {
  autoAdvance: boolean;
  autoAdvanceInterval: number; // seconds
  showTimer: boolean;
  showNotes: boolean;
  showProgress: boolean;
  enableDrawing: boolean;
  enableLaserPointer: boolean;
  transitionEffect: 'none' | 'fade' | 'slide' | 'zoom';
  backgroundColor: string;
  cursorSize: number;
  penColor: string;
  penSize: number;
}

export interface PresentationNote {
  pageNumber: number;
  content: string;
  timestamp: number;
}

export interface DrawingStroke {
  id: string;
  pageNumber: number;
  points: { x: number; y: number; pressure?: number }[];
  color: string;
  size: number;
  tool: 'pen' | 'highlighter' | 'laser';
  timestamp: number;
}

export interface PresentationState {
  isPresenting: boolean;
  isPaused: boolean;
  currentPage: number;
  startTime: number;
  elapsedTime: number;
  totalDuration?: number;
  notes: PresentationNote[];
  drawings: DrawingStroke[];
  settings: PresentationSettings;
}

interface PresentationModeProps {
  document: DocumentInstance;
  onClose: () => void;
  initialSettings?: Partial<PresentationSettings>;
  onPresentationEnd?: (stats: PresentationStats) => void;
  className?: string;
}

export interface PresentationStats {
  totalDuration: number;
  pagesViewed: number[];
  timePerPage: Record<number, number>;
  drawingStrokes: number;
  notesAdded: number;
}

const DEFAULT_SETTINGS: PresentationSettings = {
  autoAdvance: false,
  autoAdvanceInterval: 30,
  showTimer: true,
  showNotes: true,
  showProgress: true,
  enableDrawing: true,
  enableLaserPointer: true,
  transitionEffect: 'fade',
  backgroundColor: '#000000',
  cursorSize: 20,
  penColor: '#ff0000',
  penSize: 3
};

export default function PresentationMode({
  document,
  onClose,
  initialSettings,
  onPresentationEnd,
  className
}: PresentationModeProps) {
  // Presentation state
  const [state, setState] = useState<PresentationState>({
    isPresenting: false,
    isPaused: false,
    currentPage: 1,
    startTime: 0,
    elapsedTime: 0,
    notes: [],
    drawings: [],
    settings: { ...DEFAULT_SETTINGS, ...initialSettings }
  });

  // UI state
  const [showControls, setShowControls] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [showNotes, setShowNotes] = useState(false);
  const [currentTool, setCurrentTool] = useState<'pointer' | 'pen' | 'highlighter' | 'laser' | 'eraser'>('pointer');
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentStroke, setCurrentStroke] = useState<DrawingStroke | null>(null);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const autoAdvanceRef = useRef<NodeJS.Timeout | null>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Document state
  const [numPages, setNumPages] = useState(0);
  const [scale, setScale] = useState(1.0);

  // Start presentation
  const startPresentation = useCallback(() => {
    setState(prev => ({
      ...prev,
      isPresenting: true,
      isPaused: false,
      startTime: Date.now(),
      elapsedTime: 0
    }));

    // Enter fullscreen
    if (containerRef.current && containerRef.current.requestFullscreen) {
      containerRef.current.requestFullscreen().catch(console.error);
    }

    // Start timer
    timerRef.current = setInterval(() => {
      setState(prev => ({
        ...prev,
        elapsedTime: Date.now() - prev.startTime
      }));
    }, 1000);

    // Setup auto-advance if enabled
    if (state.settings.autoAdvance) {
      setupAutoAdvance();
    }

    toast.success('Presentation started');
  }, [state.settings.autoAdvance]);

  // Stop presentation
  const stopPresentation = useCallback(() => {
    setState(prev => {
      const stats: PresentationStats = {
        totalDuration: prev.elapsedTime,
        pagesViewed: [prev.currentPage], // This would be tracked more comprehensively
        timePerPage: { [prev.currentPage]: prev.elapsedTime },
        drawingStrokes: prev.drawings.length,
        notesAdded: prev.notes.length
      };

      onPresentationEnd?.(stats);

      return {
        ...prev,
        isPresenting: false,
        isPaused: false
      };
    });

    // Clear timers
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    if (autoAdvanceRef.current) {
      clearTimeout(autoAdvanceRef.current);
      autoAdvanceRef.current = null;
    }

    // Exit fullscreen
    if (document.fullscreenElement) {
      document.exitFullscreen().catch(console.error);
    }

    toast.success('Presentation ended');
  }, [onPresentationEnd]);

  // Pause/resume presentation
  const togglePause = useCallback(() => {
    setState(prev => ({
      ...prev,
      isPaused: !prev.isPaused
    }));

    if (state.isPaused) {
      // Resume auto-advance
      if (state.settings.autoAdvance) {
        setupAutoAdvance();
      }
    } else {
      // Pause auto-advance
      if (autoAdvanceRef.current) {
        clearTimeout(autoAdvanceRef.current);
        autoAdvanceRef.current = null;
      }
    }
  }, [state.isPaused, state.settings.autoAdvance]);

  // Setup auto-advance
  const setupAutoAdvance = useCallback(() => {
    if (autoAdvanceRef.current) {
      clearTimeout(autoAdvanceRef.current);
    }

    autoAdvanceRef.current = setTimeout(() => {
      if (state.currentPage < numPages) {
        goToPage(state.currentPage + 1);
        setupAutoAdvance();
      } else {
        stopPresentation();
      }
    }, state.settings.autoAdvanceInterval * 1000);
  }, [state.currentPage, state.settings.autoAdvanceInterval, numPages]);

  // Navigation
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= numPages) {
      setState(prev => ({
        ...prev,
        currentPage: page
      }));

      // Reset auto-advance timer
      if (state.settings.autoAdvance && state.isPresenting && !state.isPaused) {
        setupAutoAdvance();
      }
    }
  }, [numPages, state.settings.autoAdvance, state.isPresenting, state.isPaused, setupAutoAdvance]);

  const nextPage = useCallback(() => goToPage(state.currentPage + 1), [state.currentPage, goToPage]);
  const prevPage = useCallback(() => goToPage(state.currentPage - 1), [state.currentPage, goToPage]);

  // Drawing functionality
  const startDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (currentTool === 'pointer') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newStroke: DrawingStroke = {
      id: `stroke_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      pageNumber: state.currentPage,
      points: [{ x, y }],
      color: state.settings.penColor,
      size: state.settings.penSize,
      tool: currentTool === 'laser' ? 'laser' : currentTool === 'highlighter' ? 'highlighter' : 'pen',
      timestamp: Date.now()
    };

    setCurrentStroke(newStroke);
    setIsDrawing(true);
  }, [currentTool, state.currentPage, state.settings.penColor, state.settings.penSize]);

  const continueDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !currentStroke) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setCurrentStroke(prev => prev ? {
      ...prev,
      points: [...prev.points, { x, y }]
    } : null);
  }, [isDrawing, currentStroke]);

  const endDrawing = useCallback(() => {
    if (currentStroke) {
      setState(prev => ({
        ...prev,
        drawings: [...prev.drawings, currentStroke]
      }));
    }
    setCurrentStroke(null);
    setIsDrawing(false);
  }, [currentStroke]);

  // Auto-hide controls
  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    setShowControls(true);
    controlsTimeoutRef.current = setTimeout(() => {
      if (state.isPresenting) {
        setShowControls(false);
      }
    }, 3000);
  }, [state.isPresenting]);

  // Mouse move handler for auto-hiding controls
  const handleMouseMove = useCallback(() => {
    if (state.isPresenting) {
      resetControlsTimeout();
    }
  }, [state.isPresenting, resetControlsTimeout]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!state.isPresenting) return;

      switch (event.key) {
        case 'ArrowRight':
        case ' ':
          event.preventDefault();
          nextPage();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          prevPage();
          break;
        case 'Escape':
          event.preventDefault();
          stopPresentation();
          break;
        case 'p':
        case 'P':
          event.preventDefault();
          togglePause();
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          // Toggle fullscreen
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [state.isPresenting, nextPage, prevPage, stopPresentation, togglePause]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
      if (autoAdvanceRef.current) clearTimeout(autoAdvanceRef.current);
      if (controlsTimeoutRef.current) clearTimeout(controlsTimeoutRef.current);
    };
  }, []);

  // Format time
  const formatTime = useCallback((milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  }, []);

  return (
    <div
      ref={containerRef}
      className={`h-screen flex flex-col bg-black text-white relative ${className}`}
      onMouseMove={handleMouseMove}
      style={{ backgroundColor: state.settings.backgroundColor }}
    >
      {/* Main presentation area */}
      <div className="flex-1 flex items-center justify-center relative">
        <Document
          file={document.file}
          onLoadSuccess={onDocumentLoadSuccess}
          className="max-w-full max-h-full"
        >
          <Page
            pageNumber={state.currentPage}
            scale={scale}
            className="shadow-lg"
          />
        </Document>

        {/* Drawing canvas overlay */}
        {state.settings.enableDrawing && (
          <canvas
            ref={canvasRef}
            className="absolute inset-0 pointer-events-auto"
            style={{
              cursor: currentTool === 'pointer' ? 'default' : 
                     currentTool === 'laser' ? 'crosshair' : 
                     currentTool === 'eraser' ? 'grab' : 'crosshair'
            }}
            onMouseDown={startDrawing}
            onMouseMove={continueDrawing}
            onMouseUp={endDrawing}
            onMouseLeave={endDrawing}
          />
        )}

        {/* Progress indicator */}
        {state.settings.showProgress && state.isPresenting && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <Progress
              value={(state.currentPage / numPages) * 100}
              className="w-64 h-2 bg-white/20"
            />
          </div>
        )}
      </div>

      {/* Controls overlay */}
      {showControls && (
        <div className="absolute inset-x-0 bottom-0 bg-black/80 backdrop-blur-sm p-4 transition-all duration-300">
          <div className="flex items-center justify-between">
            {/* Left controls */}
            <div className="flex items-center gap-2">
              {!state.isPresenting ? (
                <Button
                  onClick={startPresentation}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Presentation
                </Button>
              ) : (
                <>
                  <Button
                    onClick={togglePause}
                    variant="outline"
                    size="sm"
                  >
                    {state.isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={stopPresentation}
                    variant="outline"
                    size="sm"
                  >
                    <Square className="h-4 w-4" />
                  </Button>
                </>
              )}

              <Separator orientation="vertical" className="h-6" />

              {/* Navigation */}
              <Button
                onClick={prevPage}
                disabled={state.currentPage <= 1}
                variant="outline"
                size="sm"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm px-2">
                {state.currentPage} / {numPages}
              </span>
              <Button
                onClick={nextPage}
                disabled={state.currentPage >= numPages}
                variant="outline"
                size="sm"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Center - Timer */}
            {state.settings.showTimer && state.isPresenting && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span className="text-lg font-mono">
                  {formatTime(state.elapsedTime)}
                </span>
                {state.isPaused && (
                  <Badge variant="secondary">Paused</Badge>
                )}
              </div>
            )}

            {/* Right controls */}
            <div className="flex items-center gap-2">
              {/* Drawing tools */}
              {state.settings.enableDrawing && (
                <>
                  <Button
                    onClick={() => setCurrentTool('pointer')}
                    variant={currentTool === 'pointer' ? 'default' : 'outline'}
                    size="sm"
                  >
                    <MousePointer className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => setCurrentTool('pen')}
                    variant={currentTool === 'pen' ? 'default' : 'outline'}
                    size="sm"
                  >
                    <Pen className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => setCurrentTool('laser')}
                    variant={currentTool === 'laser' ? 'default' : 'outline'}
                    size="sm"
                  >
                    <MousePointer className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={() => setCurrentTool('eraser')}
                    variant={currentTool === 'eraser' ? 'default' : 'outline'}
                    size="sm"
                  >
                    <Eraser className="h-4 w-4" />
                  </Button>

                  <Separator orientation="vertical" className="h-6" />
                </>
              )}

              <Button
                onClick={() => setShowSettings(!showSettings)}
                variant="outline"
                size="sm"
              >
                <Settings className="h-4 w-4" />
              </Button>

              <Button
                onClick={onClose}
                variant="outline"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Settings panel */}
      {showSettings && (
        <div className="absolute top-4 right-4 bg-black/90 backdrop-blur-sm rounded-lg p-4 w-80">
          <h3 className="text-lg font-semibold mb-4">Presentation Settings</h3>
          {/* Settings content would go here */}
          <Button
            onClick={() => setShowSettings(false)}
            variant="outline"
            size="sm"
            className="mt-4"
          >
            Close
          </Button>
        </div>
      )}
    </div>
  );
}
