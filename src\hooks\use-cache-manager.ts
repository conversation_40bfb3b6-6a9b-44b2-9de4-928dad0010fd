"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { getCacheManager, type CacheManagerConfig, type CacheManagerStats } from '@/lib/cache/cache-manager';
import type { CachedPageData } from '@/lib/cache/page-cache';
import type { ThumbnailData } from '@/lib/cache/thumbnail-cache';

/**
 * React Hook for Cache Management
 * Provides easy access to caching functionality with React state management
 */

export interface UseCacheManagerOptions {
  config?: Partial<CacheManagerConfig>;
  enableAutoStats?: boolean;
  statsInterval?: number;
  enableOfflineDetection?: boolean;
}

export interface CacheManagerHookReturn {
  // Status
  isReady: boolean;
  isOnline: boolean;
  stats: CacheManagerStats | null;
  
  // Page cache operations
  getPage: (documentId: string, pageNumber: number, scale?: number, rotation?: number) => Promise<CachedPageData | null>;
  setPage: (pageData: Omit<CachedPageData, 'timestamp' | 'accessCount' | 'lastAccessed' | 'size'>) => Promise<void>;
  preloadPages: (documentId: string, centerPage: number, range?: number, scale?: number, rotation?: number) => Promise<void>;
  
  // Thumbnail cache operations
  getThumbnail: (documentId: string, pageNumber: number, scale?: number) => Promise<ThumbnailData | null>;
  setThumbnail: (thumbnailData: Omit<ThumbnailData, 'timestamp' | 'lastAccessed' | 'accessCount' | 'size'>) => Promise<void>;
  generateThumbnail: (pdfPage: any, documentId: string, pageNumber: number, options?: any) => Promise<ThumbnailData | null>;
  
  // Cache management
  clearCache: (type?: 'all' | 'pages' | 'thumbnails' | 'serviceWorker') => Promise<void>;
  optimizeCache: () => Promise<void>;
  refreshStats: () => Promise<void>;
  
  // Configuration
  updateConfig: (updates: Partial<CacheManagerConfig>) => void;
}

export const useCacheManager = (options: UseCacheManagerOptions = {}): CacheManagerHookReturn => {
  const {
    config = {},
    enableAutoStats = true,
    statsInterval = 30000, // 30 seconds
    enableOfflineDetection = true,
  } = options;

  const [isReady, setIsReady] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [stats, setStats] = useState<CacheManagerStats | null>(null);
  
  const cacheManagerRef = useRef(getCacheManager(config));
  const statsIntervalRef = useRef<NodeJS.Timeout>();

  // Initialize cache manager
  useEffect(() => {
    const initializeManager = async () => {
      try {
        const manager = cacheManagerRef.current;
        
        // Wait for initialization
        while (!manager.isReady()) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        setIsReady(true);
        
        // Get initial stats
        if (enableAutoStats) {
          const initialStats = await manager.getStats();
          setStats(initialStats);
          setIsOnline(!initialStats.global.isOnline);
        }
      } catch (error) {
        console.error('Failed to initialize cache manager:', error);
      }
    };

    initializeManager();
  }, [enableAutoStats]);

  // Set up automatic stats updates
  useEffect(() => {
    if (!enableAutoStats || !isReady) return;

    const updateStats = async () => {
      try {
        const manager = cacheManagerRef.current;
        const newStats = await manager.getStats();
        setStats(newStats);
        setIsOnline(!newStats.global.isOnline);
      } catch (error) {
        console.error('Failed to update cache stats:', error);
      }
    };

    // Initial stats update
    updateStats();

    // Set up interval
    statsIntervalRef.current = setInterval(updateStats, statsInterval);

    return () => {
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current);
      }
    };
  }, [isReady, enableAutoStats, statsInterval]);

  // Set up offline detection
  useEffect(() => {
    if (!enableOfflineDetection) return;

    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial state
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [enableOfflineDetection]);

  // Page cache operations
  const getPage = useCallback(async (
    documentId: string,
    pageNumber: number,
    scale: number = 1.0,
    rotation: number = 0
  ): Promise<CachedPageData | null> => {
    if (!isReady) return null;
    
    try {
      return await cacheManagerRef.current.getPage(documentId, pageNumber, scale, rotation);
    } catch (error) {
      console.error('Failed to get page from cache:', error);
      return null;
    }
  }, [isReady]);

  const setPage = useCallback(async (
    pageData: Omit<CachedPageData, 'timestamp' | 'accessCount' | 'lastAccessed' | 'size'>
  ): Promise<void> => {
    if (!isReady) return;
    
    try {
      await cacheManagerRef.current.setPage(pageData);
      
      // Update stats if auto-stats is enabled
      if (enableAutoStats) {
        const newStats = await cacheManagerRef.current.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Failed to set page in cache:', error);
    }
  }, [isReady, enableAutoStats]);

  const preloadPages = useCallback(async (
    documentId: string,
    centerPage: number,
    range: number = 2,
    scale: number = 1.0,
    rotation: number = 0
  ): Promise<void> => {
    if (!isReady) return;
    
    try {
      await cacheManagerRef.current.preloadPages(documentId, centerPage, range, scale, rotation);
    } catch (error) {
      console.error('Failed to preload pages:', error);
    }
  }, [isReady]);

  // Thumbnail cache operations
  const getThumbnail = useCallback(async (
    documentId: string,
    pageNumber: number,
    scale: number = 1.0
  ): Promise<ThumbnailData | null> => {
    if (!isReady) return null;
    
    try {
      return await cacheManagerRef.current.getThumbnail(documentId, pageNumber, scale);
    } catch (error) {
      console.error('Failed to get thumbnail from cache:', error);
      return null;
    }
  }, [isReady]);

  const setThumbnail = useCallback(async (
    thumbnailData: Omit<ThumbnailData, 'timestamp' | 'lastAccessed' | 'accessCount' | 'size'>
  ): Promise<void> => {
    if (!isReady) return;
    
    try {
      await cacheManagerRef.current.setThumbnail(thumbnailData);
      
      // Update stats if auto-stats is enabled
      if (enableAutoStats) {
        const newStats = await cacheManagerRef.current.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Failed to set thumbnail in cache:', error);
    }
  }, [isReady, enableAutoStats]);

  const generateThumbnail = useCallback(async (
    pdfPage: any,
    documentId: string,
    pageNumber: number,
    options?: any
  ): Promise<ThumbnailData | null> => {
    if (!isReady) return null;
    
    try {
      const thumbnail = await cacheManagerRef.current.generateThumbnail(pdfPage, documentId, pageNumber, options);
      
      // Update stats if auto-stats is enabled and thumbnail was generated
      if (enableAutoStats && thumbnail) {
        const newStats = await cacheManagerRef.current.getStats();
        setStats(newStats);
      }
      
      return thumbnail;
    } catch (error) {
      console.error('Failed to generate thumbnail:', error);
      return null;
    }
  }, [isReady, enableAutoStats]);

  // Cache management operations
  const clearCache = useCallback(async (
    type: 'all' | 'pages' | 'thumbnails' | 'serviceWorker' = 'all'
  ): Promise<void> => {
    if (!isReady) return;
    
    try {
      await cacheManagerRef.current.clearCache(type);
      
      // Update stats after clearing
      if (enableAutoStats) {
        const newStats = await cacheManagerRef.current.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }, [isReady, enableAutoStats]);

  const optimizeCache = useCallback(async (): Promise<void> => {
    if (!isReady) return;
    
    try {
      await cacheManagerRef.current.optimizeCache();
      
      // Update stats after optimization
      if (enableAutoStats) {
        const newStats = await cacheManagerRef.current.getStats();
        setStats(newStats);
      }
    } catch (error) {
      console.error('Failed to optimize cache:', error);
    }
  }, [isReady, enableAutoStats]);

  const refreshStats = useCallback(async (): Promise<void> => {
    if (!isReady) return;
    
    try {
      const newStats = await cacheManagerRef.current.getStats();
      setStats(newStats);
      setIsOnline(!newStats.global.isOnline);
    } catch (error) {
      console.error('Failed to refresh cache stats:', error);
    }
  }, [isReady]);

  // Configuration
  const updateConfig = useCallback((updates: Partial<CacheManagerConfig>): void => {
    if (!isReady) return;
    
    try {
      cacheManagerRef.current.updateConfig(updates);
    } catch (error) {
      console.error('Failed to update cache config:', error);
    }
  }, [isReady]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (statsIntervalRef.current) {
        clearInterval(statsIntervalRef.current);
      }
    };
  }, []);

  return {
    // Status
    isReady,
    isOnline,
    stats,
    
    // Page cache operations
    getPage,
    setPage,
    preloadPages,
    
    // Thumbnail cache operations
    getThumbnail,
    setThumbnail,
    generateThumbnail,
    
    // Cache management
    clearCache,
    optimizeCache,
    refreshStats,
    
    // Configuration
    updateConfig,
  };
};

// Convenience hooks for specific cache types
export const usePageCache = (options: UseCacheManagerOptions = {}) => {
  const cacheManager = useCacheManager(options);
  
  return {
    isReady: cacheManager.isReady,
    getPage: cacheManager.getPage,
    setPage: cacheManager.setPage,
    preloadPages: cacheManager.preloadPages,
    clearPages: () => cacheManager.clearCache('pages'),
    pageStats: cacheManager.stats?.pages,
  };
};

export const useThumbnailCache = (options: UseCacheManagerOptions = {}) => {
  const cacheManager = useCacheManager(options);
  
  return {
    isReady: cacheManager.isReady,
    getThumbnail: cacheManager.getThumbnail,
    setThumbnail: cacheManager.setThumbnail,
    generateThumbnail: cacheManager.generateThumbnail,
    clearThumbnails: () => cacheManager.clearCache('thumbnails'),
    thumbnailStats: cacheManager.stats?.thumbnails,
  };
};
