// Search components - consolidated with enhanced features
export { default as PDFSearch } from "./pdf-search";

// Legacy exports for backward compatibility
export { default as PDFSearchSimple } from "./pdf-search";
export { default as PDFSearchEnhanced } from "./pdf-search";
export { default as PDFSearchUnified } from "./pdf-search";
export { default as PDFSearchFixed } from "./pdf-search";

// Enhanced search component (standalone)
export { default as EnhancedSearch } from "./enhanced-search";

// Advanced search components
export { default as AdvancedSearchInterface } from "./advanced-search-interface";
export { default as SearchResultsDisplay } from "./search-results-display";
export { default as SearchAnalytics } from "./search-analytics";

// Export types from enhanced search component
export type {
  SearchResult,
  SearchOptions,
  SearchHistory,
  SearchSuggestion,
} from "./enhanced-search";

// Export types from advanced search components
export type {
  AdvancedSearchQuery,
  SearchFilters,
  SearchFacets,
} from "./advanced-search-interface";

export type {
  SearchAnalyticsData,
} from "./search-analytics";
